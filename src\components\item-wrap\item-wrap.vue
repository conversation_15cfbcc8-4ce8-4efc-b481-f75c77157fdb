<script setup lang="ts">
// import BorderBox13 from "@/components/datav/border-box-13";
withDefaults(
  defineProps<{
    // 标题
    title: number | string;
  }>(),
  {
    title: ""
  }
);
</script>

<template>
  <div class="item_title" v-if="title !== ''">
    <div class="zuo"></div>
    <span class="title-inner"> &nbsp;&nbsp;{{ title }}&nbsp;&nbsp; </span>
    <div class="you"></div>
  </div>
  <div :class="title !== '' ? 'item_title_content' : 'item_title_content_def'">
    <slot></slot>
  </div>
</template>

<style scoped lang="scss">
$item-title-height: 38px;
$item_title_content-height: calc(100% - 38px);
.item_title {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: $item-title-height;
  line-height: $item-title-height;
  color: #31abe3;
  text-align: center;

  // background: linear-gradient(to right, transparent, #0f0756, transparent);
  .zuo,
  .you {
    width: 58px;
    height: 14px;
    background-image: url("@/assets/img/titles/zuo.png");
  }
  .you {
    transform: rotate(180deg);
  }
  .title-inner {
    font-weight: 900;
    letter-spacing: 2px;
    background: linear-gradient(92deg, #0072ff 0%, #00eaff 48.8525390625%, #01aaff 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
:deep(.dv-border-box-content) {
  box-sizing: border-box;
  padding: 6px 16px 0;
}
.item_title_content {
  height: $item_title_content-height;
  background: rgb(89 98 123 / 23%);
  border-radius: 8px; /* 添加圆角 */
  box-shadow: 0 4px 16px rgb(0 0 0 / 20%); /* 添加阴影 */
  transition: all 0.3s ease; /* 添加过渡动画 */
}
.item_title_content_def {
  width: 100%;
  height: 100%;
}
</style>
