<!-- 💥 这里是一次性加载 LayoutComponents -->
<template>
  <el-watermark id="watermark" :font="font" :content="watermark ? ['赢合科技'] : ''">
    <component :is="LayoutComponents[layout]" />
    <ThemeDrawer />
  </el-watermark>
</template>

<script setup lang="ts" name="layout">
import { computed, reactive, watch, type Component } from "vue";
import { LayoutType } from "@/stores/interface";
import { useGlobalStore, useMqttStore, useMessageStore } from "@/stores/modules";
import ThemeDrawer from "./components/ThemeDrawer/index.vue";
import LayoutVertical from "./LayoutVertical/index.vue";
import LayoutClassic from "./LayoutClassic/index.vue";
import LayoutTransverse from "./LayoutTransverse/index.vue";
import LayoutColumns from "./LayoutColumns/index.vue";
import * as htmlToImage from "html-to-image";
import { useUserStore } from "@/stores/modules/user";
import { useTabsStore } from "@/stores/modules/tabs";

const userStore = useUserStore();
// import { uploadFeishuImage } from "@/utils";

import { useRouter } from "vue-router";
const tabStore = useTabsStore();

const router = useRouter();
const LayoutComponents: Record<LayoutType, Component> = {
  vertical: LayoutVertical,
  classic: LayoutClassic,
  transverse: LayoutTransverse,
  columns: LayoutColumns
};

const globalStore = useGlobalStore();
const isDark = computed(() => globalStore.isDark);
const layout = computed(() => globalStore.layout);
const watermark = computed(() => globalStore.watermark);
const mqttStore = useMqttStore(); //mqttStore
const messageStore = useMessageStore(); //messageStore
const openMqtt = import.meta.env.VITE_MQTT === "true"; //mqtt开关
const font = reactive({ color: "rgba(0, 0, 0, .15)" });
watch(isDark, () => (font.color = isDark.value ? "rgba(255, 255, 255, .15)" : "rgba(0, 0, 0, .15)"), {
  immediate: true
});
// import { useAuthStore } from "@/stores/modules";
import { calculateDelay, validateSchedule } from "@/utils";
import { userCenterApi } from "@/api";
import moment from "moment";
// import { menuApi } from "@/api";
// const authStore = useAuthStore();

// const menuList = computed(() => authStore.showMenuListGet);
const shittime = ref();
let timerManager;
const shitpush = () => {
  clearInterval(shittime.value);
  timerManager = {
    timers: new Set<number>(),
    addTimer: (timerId: number) => {
      timerManager.timers.add(timerId);
    },
    clearAll: () => {
      timerManager.timers.forEach(timerId => clearTimeout(timerId));
      timerManager.timers.clear();
    }
  };
  timerManager.clearAll();

  // 主监控定时器
  shittime.value = setInterval(async () => {
    if (!userStore.isAlarm) {
      timerManager.clearAll();
      return;
    }

    if (timerManager.timers.size > 0) return;

    const chooseModule = userStore.chooseModuleGet;
    const { data } = await userCenterApi.getAuthMenuList({ id: chooseModule });
    const paths = extractPaths(data);
    const taskShitset = new Set<number>();

    const shitiid = new Set();
    const ingoreids = [
      648696782794821, 648697115516997, 651535198396485, 651535566975045, 651535946457157, 651508965347397, 651516148518981, 648085807235141
    ];
    const timeCounts = new Map(); // 用 Map 替代 Set

    paths.forEach(item => {
      const { warningInfos, id } = item;
      const warningInfos1 = JSON.parse(warningInfos);

      warningInfos1
        .filter(item => item.isWaring == "ENABLE")
        .forEach(({ timePoints, frequencyType, frequencyValue }) => {
          timePoints.forEach(timePoint1 => {
            let timePoint;
            if (shitiid.has(id + timePoint1)) return;
            console.log("%c%s %s %s", "color: red;", item.title, id, frequencyType, "当前id");

            shitiid.add(id + timePoint1);
            if (timeCounts.has(timePoint1)) {
              // 获取当前时间已出现的次数
              const count = timeCounts.get(timePoint1);

              // 计算新增的秒数（40 × 出现次数）
              const newTime = moment(timePoint1, "HH:mm")
                .add(30 * count, "seconds")
                .format("HH:mm:ss");

              // 更新 Map 的计数
              timeCounts.set(timePoint1, count + 1);
              timeCounts.set(newTime, 1); // 新时间也记录到 Map

              timePoint = newTime;
            } else {
              // 第一次出现的时间
              timeCounts.set(timePoint1, 1);
              timePoint = `${timePoint1}:00`;
            }

            // 为每个任务创建独立的定时器管理
            let isRunning = false;

            const scheduleNext = () => {
              if (isRunning) return;
              isRunning = true;

              const now = Date.now();
              const delay = calculateDelay(timePoint, frequencyType, frequencyValue, taskShitset, now);

              const timerId = window.setTimeout(async () => {
                isRunning = false;

                // 即使验证不通过也继续调度
                if (!validateSchedule(new Date(), frequencyType, frequencyValue)) {
                  console.log("\x1b[32m校验不通过\x1b[0m");

                  scheduleNext();
                  return;
                }

                // 执行任务逻辑
                const query: any = {};
                if (ingoreids.includes(id)) query.sign = true;

                await new Promise<void>(resolve => {
                  const removeGuard = router.afterEach(async to => {
                    if (to.path === item.path) {
                      removeGuard();
                      setTimeout(async () => {
                        const node = document.getElementById(id);
                        if (!node) return resolve();

                        if (!ingoreids.includes(id)) {
                          try {
                            const blob = await htmlToImage.toBlob(node, { quality: 0.95, pixelRatio: 2 });
                            const formData = new FormData();
                            formData.append("file", blob, `${id}.png`);
                            formData.append("type", item.title);
                            await fetch("/api/sys/mes/uploadFeishuImage", { method: "POST", body: formData });
                          } catch (e) {
                            console.error("上传失败:", e);
                          }
                        }
                        resolve();
                      }, 3000);
                    }
                  });

                  tabStore.closeMultipleTab();
                  router.push({ path: item.path, query });
                });

                // 继续调度下一次
                scheduleNext();
              }, delay);

              timerManager.addTimer(timerId);
            };

            // 初始调度
            scheduleNext();
          });
        });
    });
  }, 2000);
};

function extractPaths(menus) {
  const paths = [];
  menus.forEach(menu => {
    if (menu.path && (!menu.children || menu.children.length === 0) && menu.warningInfos) {
      paths.push(menu);
    }
    if (menu.children && menu.children.length > 0) {
      paths.push(...extractPaths(menu.children));
    }
  });
  return paths;
}
watch(
  () => userStore.isAlarm,
  newValue => {
    if (newValue) {
      shitpush();
    } else {
      clearInterval(shittime.value);
      if (timerManager) {
        console.log("已清理");

        timerManager?.clearAll();
      }
    }
  },
  { immediate: true }
);
onMounted(async () => {
  await initMqtt();
  // shitpush();
});

onUnmounted(() => {
  if (openMqtt) {
    mqttStore.disconnect(); //销毁mqtt
  }
  clearInterval(shittime.value);
});

/**初始化mqtt */
async function initMqtt() {
  if (openMqtt) {
    await mqttStore.initMqttClient(); //初始化mqtt
  } else {
    await messageStore.getNewMessageInterval(); //定时获取新消息
  }
}
</script>

<style scoped lang="scss">
.layout {
  min-width: 600px;
}
</style>
