<template>
  <div class="main-box" id="shit">
    <div class="table-box">
      <div>
        <SearchForm :columns="searchColumns" :search-param="searchParam" :search-col="3" :search="handleSearch" :reset="handleReset">
          <template #any>
            <el-button @click="exportData" :icon="Document">表格导出</el-button>
            <el-button @click="exportImgs" :icon="Picture">图片导出</el-button>
          </template>
        </SearchForm>
      </div>
      <!-- 操作按钮 -->

      <div class="charts table-main">
        <div class="chart-item table-main" id="chart4">
          <v-chart :autoresize="true" :option="lineChartOptions" class="chart" />
        </div>

        <!--  产能-->

        <!-- OEE -->
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { Document, Picture } from "@element-plus/icons-vue";
import { alramAnalysisApi } from "@/api";

// import { ref } from "vue";
import moment from "moment";
import type { ColumnProps } from "@/components/ProTable/interface";
import SearchForm from "@/components/SearchForm/index.vue"; // 引入 SearchForm 组件
// import { exportMultipleTablesToExcel } from "@/utils";

import * as XLSX from "xlsx";
import { exportElementsAsImages } from "@/utils";
const searchParam = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")] // 初始化time为当天日期范围
});

const exportDataObj = ref([]);

const data = ref([]);
// 搜索字段配置
const searchColumns = ref<ColumnProps[]>([
  {
    prop: "time",
    label: "时间",
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": "Start date",
        "end-placeholder": "End date"
        // defaultValue: [todayStart, todayEnd]
      }
    },
    isShow: false
  }
]);

// 查询
const handleSearch = async () => {
  // 调用子组件方法
  const data1 = await alramAnalysisApi.alarmFaultStatsGet();
  data.value = data1.data;
};

// 重置
const handleReset = () => {
  // 重置searchParam
  console.log("重置");

  searchParam.value = {
    time: [moment().startOf("day"), moment().endOf("day")] // 重置为当天日期范围
  };
};

const exportImgs = () => {
  console.log(searchParam, "searchParam");
  // 格式化开始时间和结束时间
  const startTime = moment(searchParam.value.time[0]).format("YYYY-MM-DD");
  const endTime = moment(searchParam.value.time[1]).format("YYYY-MM-DD");
  const exportItems = [{ elementId: "chart1", fileName: `ng统计${startTime}-${endTime}.png` }];
  try {
    exportElementsAsImages(exportItems);
    ElMessage.success("图片批量导出成功");
  } catch (error) {
    ElMessage.error("图片批量导出失败，请查看错误提示");
  }
};
// 导出数据
const exportData = () => {
  const wb = XLSX.utils.book_new();

  // 导出文件
  for (let index = 0; index < exportDataObj.value.length; index++) {
    const element = exportDataObj.value[index];
    const data = element.data.split("\n").map(row => row.split(","));
    const ws = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(wb, ws, element.name);
  }
  XLSX.writeFile(wb, "生产数据总览.xlsx");

  ElMessage.success("导出成功");
};
onMounted(() => {
  handleSearch();
});
</script>

<style lang="scss" scoped>
.charts {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
  width: 100%;
  height: 100%;

  // 根据图表数量和布局，设置合适的高度，这里以适应两个图表高度为例
  // height: calc(50% - 10px);
}

// 修改chart - item样式
.chart-item {
  display: flex;
  flex: 1 0 calc(50% - 10px); /* 每行两个，减去间距 */
  align-items: stretch; /* 内容撑满高度 */
  height: calc(50% - 1rem); /* 两行布局，高度50%减去间距 */
  overflow: hidden; /* 溢出隐藏 */
  background: white; /* 可选背景色 */
  border: 1px solid #e0e0e0; /* 可选边框 */
  border-radius: 8px; /* 可选圆角 */
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%); /* 可选阴影 */
}
.chart {
  width: 100%;
  height: 100%;
}
</style>
