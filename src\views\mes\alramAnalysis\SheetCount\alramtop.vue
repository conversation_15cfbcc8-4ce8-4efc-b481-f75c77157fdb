<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchFaultData"
    :tool-buttons="['refresh', 'download', 'table']"
    :machines="machines"
    @data-loaded="handleDataLoaded"
    @export-data="handleExportData"
    chart-height="222px"
    ref="userTable"
  />
</template>

<script setup lang="ts">
import { onMounted, ref, computed, watch } from "vue";
import moment from "moment";
// import { ElMessage } from "element-plus";
import { alramAnalysisApi } from "@/api";
// import axios from "axios";
import { useI18n } from "vue-i18n";

const props = defineProps({
  machines: {
    type: Array as PropType<Array<{ id: string; name: string }>>,
    default: () => []
  }
});

const emits = defineEmits(["dataReady", "exportData"]);

// const machines = ref<any[]>([]);
const handleExportData = (csvContent: string) => {
  emits("exportData", { data: csvContent });
};

// 颜色配置
const COLORS = {
  fault_count: "#00bfff",
  font: "#666",
  splitLine: "#eee"
};

const { t } = useI18n();

// 基础配置
const baseOptions = ref({
  title: {
    subtext: t("sheetCount.faultCountTop10"),
    left: "center",
    textStyle: {
      fontSize: 16,
      color: COLORS.font
    },
    top: -12
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["bar", "line"],
        title: {
          bar: t("common.switchToBar"),
          line: t("common.switchToLine")
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const fault_count = params[0];
      return `
        <div style="padding:5px;min-width:120px">
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.fault_count};border-radius:50%"></span>
            ${t("sheetCount.alarmName")}: ${fault_count.name}<br/>
            ${t("sheetCount.faultCount")}: ${fault_count.value}
          </div>
        </div>
      `;
    }
  },
  legend: {
    show: false
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      color: COLORS.font,
      interval: 0,
      rotate: 45
    }
  },
  yAxis: {
    type: "value",
    name: t("sheetCount.faultCount"),
    min: 0,
    axisLabel: {
      color: COLORS.font
    },
    splitLine: { lineStyle: { color: COLORS.splitLine } }
  },
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true
  },
  series: []
});

// 监听语言变化，更新基础配置中的文本
watch(
  () => t("sheetCount.faultCountTop10"),
  newValue => {
    baseOptions.value.title.subtext = newValue;
  }
);

watch(
  () => t("sheetCount.faultCount"),
  newValue => {
    baseOptions.value.yAxis.name = newValue;
  }
);

watch(
  () => t("common.switchToBar"),
  newValue => {
    baseOptions.value.toolbox.feature.magicType.title.bar = newValue;
  }
);

watch(
  () => t("common.switchToLine"),
  newValue => {
    baseOptions.value.toolbox.feature.magicType.title.line = newValue;
  }
);

// 图表配置
const chartOptions = computed(() => baseOptions.value);

const userTable = ref(null);

defineExpose({
  tableRef: userTable
});

// 2. 切换机台数据获取函数（Type=5）
const fetchFaultData = async (params: { time: Date[]; machine?: string }) => {
  try {
    // 统一时间格式为YYYY-MM-DD HH:mm:ss
    const startTime = moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss");
    const endTime = moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss");

    const queryParams = {
      Deck: params.machine || (props.machines.length > 0 ? props.machines[0].id : ""),
      StartDate: startTime,
      EndDate: endTime,
      Type: 5 // 固定为故障类型
    };

    const response = await alramAnalysisApi.getListMesReportData({ ...queryParams });
    const data1 = transformFaultData(response.data.list);
    return {
      data: {
        categories: data1.categories,
        seriesData: [data1.seriesData]
      }
    };
  } catch (error) {
    // 返回默认值，避免 undefined
    console.error("获取故障数据失败，详细错误:", error);
    return { categories: [], seriesData: [] };
  }
};

// 3. 修改数据转换方法（不再处理机台信息）
const transformFaultData = (
  responseData: any[]
): {
  categories: string[];
  seriesData: number[];
} => {
  const faultMap = new Map();

  responseData.forEach(item => {
    const faultName = item.fault_name || "未知故障";
    const faultCount = item.fault_count || 0;
    faultMap.set(faultName, (faultMap.get(faultName) || 0) + faultCount);
  });

  // 按故障次数排序并取前10
  const sortedFaults = Array.from(faultMap.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10);

  return {
    categories: sortedFaults.map(fault => fault[0]),
    seriesData: sortedFaults.map(fault => fault[1])
  };
};
const handleDataLoaded = (data: { categories: string[]; seriesData: number[] }) => {
  if (!data || !data.seriesData || data.seriesData.length === 0) {
    return;
  }
  const [seriesData] = data.seriesData;
  baseOptions.value = {
    ...baseOptions.value,
    xAxis: {
      ...baseOptions.value.xAxis,
      data: data.categories
    },
    series: [
      {
        name: t("sheetCount.faultCount"),
        type: "bar",
        data: seriesData,
        itemStyle: { color: COLORS.fault_count },
        label: {
          show: true,
          position: "top",
          formatter: "{c}"
        }
      }
    ]
  };
};
onMounted(async () => {
  // await fetchMachines();
});
</script>
