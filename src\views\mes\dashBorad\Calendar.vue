<template>
  <div class="maintenance-calendar bg-white rounded-xl shadow-lg overflow-hidden">
    <div class="p-6">
      <h2 class="text-2xl font-bold text-gray-800 mb-4">本月维保日历</h2>

      <!-- 日历头部 -->
      <div class="grid grid-cols-7 gap-1 mb-2">
        <div v-for="day in weekDays" :key="day" class="text-center font-medium text-gray-500 py-2">
          {{ day }}
        </div>
      </div>

      <!-- 日历主体 -->
      <div v-for="(week, weekIndex) in calendarData" :key="weekIndex" class="grid grid-cols-7 gap-1">
        <!-- 日期单元格 -->
        <div
          v-for="(dayData, dayIndex) in week"
          :key="dayIndex"
          class="border border-gray-100 rounded-md hover:bg-gray-50 transition-colors"
          :class="{ 'bg-gray-50': !dayData.day }"
        >
          <!-- 日期数字 -->
          <div class="text-right px-2 py-1 text-sm font-medium">
            {{ dayData.day || "" }}
          </div>

          <!-- 维保数据 -->
          <div v-for="(value, index) in dayData.values" :key="index" class="text-center text-xs px-1 py-0.5" :class="getValueClass(value)">
            {{ formatValue(value) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from "vue";

interface DayData {
  day: number | null;
  values: (number | string)[];
}

export default defineComponent({
  name: "MaintenanceCalendar",
  setup() {
    const weekDays = ["一", "二", "三", "四", "五", "六", "日"];

    // 模拟数据 - 实际使用时可以替换为props传入
    const calendarData = computed<DayData[][]>(() => {
      return [
        // 第一周
        [
          { day: null, values: [] },
          { day: null, values: [] },
          { day: null, values: [] },
          { day: null, values: [] },
          { day: null, values: [] },
          { day: null, values: [] },
          { day: null, values: [] }
        ],
        // 第二周
        [
          { day: 388, values: [] },
          { day: 4, values: [375, 8, 11] },
          { day: 5, values: [87.8, 7, "7.2"] },
          { day: 6, values: [37.9, 9, "10+"] },
          { day: 7, values: [88, 9, "11+"] },
          { day: 8, values: [12, 7, "7.2"] },
          { day: 9, values: [8, 7, 6] }
        ],
        // 第三周
        [
          { day: 10, values: [] },
          { day: 11, values: [8, "12+"] },
          { day: 12, values: [7, "6+"] },
          { day: 13, values: [9, "11+"] },
          { day: 14, values: [9, "15+"] },
          { day: 15, values: [7, "4+"] },
          { day: 16, values: [7, 2] }
        ],
        // 第四周
        [
          { day: 17, values: [] },
          { day: 18, values: [8, "12+"] },
          { day: 19, values: [4, "6+"] },
          { day: 20, values: [9, "11+"] },
          { day: 21, values: [9, "15+"] },
          { day: 22, values: [7, "4+"] },
          { day: 23, values: [1, 2] }
        ],
        // 第五周
        [
          { day: 24, values: [4] },
          { day: 25, values: [] },
          { day: 26, values: [1] },
          { day: 27, values: [] },
          { day: 28, values: [] },
          { day: 29, values: [] },
          { day: 30, values: [] }
        ],
        // 第六周
        [
          { day: 31, values: [] },
          { day: null, values: [] },
          { day: null, values: [] },
          { day: null, values: [] },
          { day: null, values: [] },
          { day: null, values: [] },
          { day: null, values: [] }
        ]
      ];
    });

    const formatValue = (value: number | string): string => {
      if (typeof value === "number") {
        return value.toString();
      }
      return value;
    };

    const getValueClass = (value: number | string): string => {
      if (typeof value === "string" && value.includes("+")) {
        return "text-red-500 font-bold";
      }
      if (typeof value === "number" && value > 100) {
        return "text-blue-600 font-medium";
      }
      return "text-gray-700";
    };

    return {
      weekDays,
      calendarData,
      formatValue,
      getValueClass
    };
  }
});
</script>

<style scoped>
.maintenance-calendar {
  width: 100%;
  height: 100%;

  /* max-width: 600px; */
  transition: all 0.3s ease;
}
.maintenance-calendar:hover {
  box-shadow: 0 10px 25px rgb(0 0 0 / 10%);
}
</style>
