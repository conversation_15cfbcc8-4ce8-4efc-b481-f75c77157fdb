<template>
  <div class="main-box" id="651515820077125">
    <div class="table-main">
      <SearchForm :columns="searchColumns" :search-param="{}" :search-col="3" :search="handleSearch" :reset="handleReset">
        <template #any>
          <el-button @click="exportData" :icon="Document">{{ t("energyConsumption.current.exportTable") }}</el-button>
          <el-button @click="exportImgs" :icon="Picture">{{ t("energyConsumption.current.exportImage") }}</el-button>
        </template>
      </SearchForm>
      <div class="flex w-full flex-1 gap-[20px]">
        <GasFlow ref="GasFlowRef" :gas-data="gasData" id="chart2" />
        <Energe ref="EnergeRef" :elec-data="elecData" id="chart1" />
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup name="实时能耗">
import GasFlow from "./GasFlow.vue";
import Energe from "./Energe.vue";
import { Document, Picture } from "@element-plus/icons-vue";
import { exportElementsAsImages, transformChart, exportMultipleTablesToExcel } from "@/utils";
import moment from "moment";
import { ref } from "vue";
import { ColumnProps } from "@/components/ProTable/interface";
import { productionReportCapacityApi } from "@/api";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const shitrender = () => {
  return <MachineSwitcher machine-list={machines.value} onChange={handleMachineChange} />;
};
const machines = ref([
  {
    id: "产线1",
    name: "产线1"
  }
]);
const handleMachineChange = (value: any) => {
  console.log(value, "value");
  // machines.value = value;
};
const searchColumns = ref<ColumnProps[]>([
  {
    prop: "machine",
    search: {
      isCustom: true,
      render: shitrender
    },
    isShow: false
  }
]);
const GasFlowRef = ref();
const EnergeRef = ref();

// 模拟动态数据
const elecData = ref();

const gasData = ref();
const handleSearch = async () => {
  try {
    const { data } = await productionReportCapacityApi.getListMesReportData({ Type: 15 });
    elecData.value = {
      powerData: data.list.map(item => item.power),
      consumedData: data.list.map(item => item.cee),
      machines: data.list.map(item => item.machine)
    };
    gasData.value = {
      realTimeFlowData: data.list.map(item => item.real_gas),
      totalConsumptionData: data.list.map(item => item.cumu_gas),
      machines: data.list.map(item => item.machine)
    };
  } catch (error) {
    console.error("获取数据失败:", error);
  }
};

// 重置
const handleReset = () => {
  // 重置数据
  handleSearch();
};
onMounted(() => {
  handleSearch();
});
const exportImgs = () => {
  const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");

  const exportItems = [
    { elementId: "chart2", fileName: `${t("energyConsumption.current.gas.title")}${currentTime}.png` },
    { elementId: "chart1", fileName: `${t("energyConsumption.current.electricity.title")}${currentTime}.png` }
  ];
  try {
    exportElementsAsImages(exportItems);
    ElMessage.success(t("energyConsumption.current.exportSuccess"));
  } catch (error) {
    ElMessage.error(t("energyConsumption.current.exportError"));
  }
};

// 导出数据
const exportData = () => {
  const arr1 = transformChart(GasFlowRef.value.chartRef, "机台");
  const arr2 = transformChart(EnergeRef.value.chartRef, "机台");

  // 获取当前时间并格式化
  const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");

  // 将当前时间拼接到文件名中
  const fileName = `${t("energyConsumption.current.title")}_${currentTime}.xlsx`;

  exportMultipleTablesToExcel([arr1, arr2], [t("energyConsumption.current.gas.title"), t("energyConsumption.current.electricity.title")], fileName);
};
</script>

<style lang="scss" scoped>
.chart {
  height: 100%; /* 设置图表高度 */
}
</style>
