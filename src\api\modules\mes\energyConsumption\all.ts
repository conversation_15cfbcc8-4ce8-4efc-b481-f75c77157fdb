import { moduleRequest } from "@/api/request";

const http = moduleRequest("/sys/mes/"); // 复用基础路径

// 单机能耗信息表接口
const singleMachineEnergyConsumptionApi = {
  /** 获取单机能耗信息 */
  getSingleMachineEnergyConsumption(params?: object) {
    return http.get("SingleMachineEnergyConsumption", params);
  }
};

// 当前总电能能耗表接口
const totalElectricEnergyConsumptionApi = {
  /** 获取当前总电能能耗信息 */
  getTotalElectricEnergyConsumption(params?: object) {
    return http.get("TotalElectricEnergyConsumption", params);
  }
};

// 气能流量统计主表接口
const gasFlowStatisticsApi = {
  /** 获取气能流量统计信息 */
  getGasFlowStatistics(params?: object) {
    return http.get("GasFlowStatistics", params);
  }
};

// 电能及气流能耗总览表接口
const energyOverviewApi = {
  /** 获取电能及气流能耗总览信息 */
  getEnergyOverview(params?: object) {
    return http.get("EnergyOverview", params);
  }
};

export { singleMachineEnergyConsumptionApi, totalElectricEnergyConsumptionApi, gasFlowStatisticsApi, energyOverviewApi };
