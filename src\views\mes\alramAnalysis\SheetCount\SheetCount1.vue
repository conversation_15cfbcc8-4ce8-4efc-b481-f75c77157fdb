<template>
  <div class="dashboard">
    <div class="table-container">
      <el-table :data="alerts" style="width: 100%; padding: 10px">
        <el-table-column prop="station" label="工位名称" width="180"> </el-table-column>
        <el-table-column prop="type" label="类型" width="180"> </el-table-column>
        <el-table-column prop="alertName" label="告警名称" width="180"> </el-table-column>
        <el-table-column prop="alertInfo" label="告警信息" width="180"> </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="180"> </el-table-column>
        <el-table-column prop="duration" label="持续时间" width="180"> </el-table-column>
        <el-table-column prop="status" label="当前状态" width="180">
          <template #default="{ row }">
            <el-tag :type="statusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="charts">
      <div class="chart-item">
        <v-chart :option="barChartOptions" class="chart" />
      </div>
      <div class="chart-item">
        <v-chart :option="pieChartOptions1" class="chart" />
      </div>
      <div class="chart-item">
        <v-chart :option="pieChartOptions2" class="chart" />
      </div>
    </div>
    <div class="charts">
      <div class="chart-item" style="width: 100%">
        <v-chart :option="lineChartOptions" class="chart" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { ElTable, ElTableColumn, ElTag } from "element-plus";

const alerts = ref([
  {
    station: "正极制片",
    type: "伺服电机",
    alertName: "伺服故障",
    alertInfo: "ERR CODE:9701",
    startTime: "2022-08-20 03:29",
    duration: "18:09",
    status: "待处理"
  },
  {
    station: "正极激光模切",
    type: "安全门",
    alertName: "安全门打开",
    alertInfo: "",
    startTime: "2022-10-02 13:01",
    duration: "23:12",
    status: "处理中"
  },
  {
    station: "热压工位",
    type: "气缸",
    alertName: "气缸2老化",
    alertInfo: "",
    startTime: "2022-04-25 16:27",
    duration: "14:08",
    status: "已完成"
  }
]);

const statusType = status => {
  switch (status) {
    case "待处理":
      return "info";
    case "处理中":
      return "warning";
    case "已完成":
      return "success";
    default:
      return "";
  }
};

// Bar Chart Options
const barChartOptions = ref({
  title: {
    text: "故障分布柏拉图",
    left: "center"
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    }
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: "切换为折线图",
          bar: "切换为柱状图"
        }
      },
      saveAsImage: { show: true, title: "保存为图片" }
    }
  },
  grid: {
    left: "3%",
    right: "4%",
    bottom: "6%",
    containLabel: true
  },
  legend: {
    data: ["故障数量", "故障率"],
    top: "bottom"
  },
  xAxis: {
    type: "value",
    boundaryGap: [0, 0.01]
  },
  yAxis: {
    type: "category",
    data: ["故障类型1", "故障类型2", "故障类型3", "故障类型4", "故障类型5"]
  },
  series: [
    {
      name: "故障数量",
      data: [33, 32, 24, 69, 78],
      type: "bar",
      showBackground: true,
      label: {
        show: true
      }
    },
    {
      name: "故障率",
      data: [100, 96, 94, 89, 78],
      type: "line",
      smooth: true
    }
  ]
});

// Pie Chart Options 1
// Pie Chart Options 1
const pieChartOptions1 = ref({
  tooltip: {
    trigger: "item",
    formatter: ({ name, value, percent }) => {
      return `${name}: ${value} (${percent.toFixed(2)}%)`;
    }
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: "切换为折线图",
          bar: "切换为柱状图"
        }
      },
      saveAsImage: { show: true, title: "保存为图片" }
    }
  },
  title: {
    text: "整机故障类型占比",
    left: "center"
  },
  // grid: {
  //   left: "3%",
  //   right: "4%",
  //   bottom: "20%",
  //   containLabel: true
  // },
  legend: {
    data: ["故障类型1", "故障类型2", "故障类型3", "其他"],
    orient: "vertical",
    left: "left"
  },
  series: [
    {
      name: "故障类型",
      type: "pie",
      radius: "50%",
      data: [
        { value: 40, name: "故障类型1" },
        { value: 20, name: "故障类型2" },
        { value: 30, name: "故障类型3" },
        { value: 10, name: "其他" }
      ],
      label: {
        show: true,
        formatter: ({ name, value, percent }) => {
          return `${name}\n${value} (${percent.toFixed(2)}%)`;
        }
      }
    }
  ]
});

// Pie Chart Options 2
const pieChartOptions2 = ref({
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: "切换为折线图",
          bar: "切换为柱状图"
        }
      },
      saveAsImage: { show: true, title: "保存为图片" }
    }
  },
  tooltip: {
    trigger: "item",
    formatter: ({ name, value, percent }) => {
      return `${name}: ${value} (${percent.toFixed(2)}%)`;
    }
  },
  title: {
    text: "设备状态统计",
    left: "center"
  },
  legend: {
    data: ["正常", "故障", "维护"],
    orient: "vertical",
    left: "left"
  },
  series: [
    {
      name: "设备状态",
      type: "pie",
      radius: "50%",
      data: [
        { value: 100, name: "正常" },
        { value: 300, name: "故障" },
        { value: 200, name: "维护" }
      ],
      label: {
        show: true,
        formatter: ({ name, value, percent }) => {
          return `${name}\n${value} (${percent.toFixed(2)}%)`;
        }
      }
    }
  ]
});

// Line Chart Options
const lineChartOptions = ref({
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: "切换为折线图",
          bar: "切换为柱状图"
        }
      },
      saveAsImage: { show: true, title: "保存为图片" }
    }
  },
  title: {
    text: "MTTR MTBF",
    left: "center"
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "cross"
    }
  },
  legend: {
    data: ["MTTR", "MTBF", "维修次数"],
    top: "bottom"
  },
  xAxis: [
    {
      type: "category",
      data: [
        "正极制片",
        "正极激光模切",
        "热压工位",
        "负极制片",
        "负极激光模切",
        "叠片台1",
        "叠片台2",
        "叠片台3",
        "热压工位1",
        "热压工位2",
        "热压工位3",
        "热压工位4",
        "热压工位5",
        "热压工位6",
        "测量",
        "下料"
      ]
    }
  ],
  yAxis: [
    {
      type: "value",
      name: "MTTR",
      min: 0,
      max: 250,
      position: "left",
      axisLine: {
        lineStyle: {
          color: "#1f77b4"
        }
      },
      axisLabel: {
        formatter: "{value}"
      }
    },
    {
      type: "value",
      name: "MTBF",
      min: 0,
      max: 250,
      position: "right",
      axisLine: {
        lineStyle: {
          color: "#2ca02c"
        }
      },
      axisLabel: {
        formatter: "{value}"
      }
    }
  ],
  series: [
    {
      name: "MTTR",
      type: "bar",
      data: [120, 132, 101, 134, 90, 230, 210, 180, 150, 120, 150, 80, 70, 110, 130, 100]
    },
    {
      name: "MTBF",
      type: "bar",
      data: [220, 182, 191, 234, 290, 330, 310, 320, 250, 220, 210, 180, 160, 190, 200, 150]
    },
    {
      name: "维修次数",
      type: "line",
      yAxisIndex: 1,
      data: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]
    }
  ]
});
</script>

<style scoped lang="scss">
.dashboard {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  overflow: hidden;
  background-color: #f0f2f5; // 浅灰色背景
  border-radius: 12px; // 圆角
}
.table-container {
  width: 100%;

  //   padding: 20px;
  margin-bottom: 20px;
  background-color: white; // 白色背景
  border-radius: 8px; // 圆角
  box-shadow: inset 0 0 4px rgb(0 0 0 / 10%); // 内阴影
}
.charts {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  margin-top: 20px;
}
.chart-item {
  width: 30%;
  padding: 10px;

  //   margin: 10px;
  overflow: hidden;
  background-color: white; // 图表区域背景
  border-radius: 8px; // 圆角
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%); // 外阴影
}
.chart {
  width: 100%;
  height: 400px;
}
</style>
