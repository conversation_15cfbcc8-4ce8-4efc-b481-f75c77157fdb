<template>
  <div :class="['status-widget', { 'scale-60': scaled }]" :style="positionStyles">
    <div class="flex justify-between items-center mb-4">
      <div class="flex items-center">
        <el-icon>
          <component :is="statusIcon" />
        </el-icon>
        <div class="text-lg font-semibold">{{ statusLabel }} {{ waitTime }}{{ t("deviceStatus.minutes") }}</div>
      </div>
    </div>
    <div class="flex flex-wrap space-x-2 w-[180px] justify-center items-center">
      <div
        v-for="status in statuses"
        :key="status.value"
        :class="[
          'status-item',
          'border',
          'text-left',
          'border-gray-400',
          'text-gray-600',
          {
            'bg-blue-600 text-white': activeStatus === status.value && status.value === 'running',
            'bg-yellow-600 text-white': activeStatus === status.value && status.value === 'waiting',
            'bg-red-600 text-white': activeStatus === status.value && status.value === 'fault',
            'bg-gray-600 text-white': activeStatus === status.value && status.value === 'stopped',
            'bg-purple-600 text-white': activeStatus === status.value && status.value === 'maintenance'
          }
        ]"
      >
        {{ status.label }}
        <!-- 标注内容 -->
        <div v-if="activeStatus === status.value" class="text-xs mt-1">
          <span v-if="status.value === 'maintenance'">{{ t("deviceStatus.maintenanceInProgress") }}</span>
          <span v-if="status.value === 'fault'">{{ t("deviceStatus.deviceAging") }}</span>
        </div>
      </div>
    </div>
    <!-- 新增风速和浓度显示 -->
    <div class="mt-4">
      <div class="text-sm text-gray-700">🌀{{ t("deviceStatus.windSpeed") }}: {{ windSpeed }} {{ t("deviceStatus.metersPerSecond") }}</div>
      <div class="text-sm text-gray-700">💨{{ t("deviceStatus.concentration") }}: {{ concentration }} {{ t("deviceStatus.ppm") }}</div>
      <!-- 需要维护信息 -->
      <div v-if="activeStatus === 'maintenance'" class="text-sm text-red-600 mt-2 blink">{{ value }}{{ t("deviceStatus.needsMaintenance") }}</div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, computed } from "vue";
import { ElIcon } from "element-plus";
import { Warning, Clock, Check, Tools } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
  waitTime: {
    type: [String, Number],
    default: "0"
  },
  top: {
    type: [String, Number],
    default: "0"
  },
  left: {
    type: [String, Number],
    default: "0"
  },
  scaled: {
    type: Boolean,
    default: true
  },
  activeStatus: {
    type: String,
    default: ""
  },
  windSpeed: {
    type: [String, Number],
    default: "0"
  },
  concentration: {
    type: [String, Number],
    default: "0"
  }
});

const statuses = computed(() => [
  { label: t("deviceStatus.running"), value: "running", icon: Check },
  { label: t("deviceStatus.waiting"), value: "waiting", icon: Clock },
  { label: t("deviceStatus.stopped"), value: "stopped", icon: Clock },
  { label: t("deviceStatus.fault"), value: "fault", icon: Warning },
  { label: t("deviceStatus.maintenance"), value: "maintenance", icon: Tools }
]);

const statusLabel = computed(() => {
  const status = statuses.value.find(s => s.value === props.activeStatus);
  return status ? status.label : t("deviceStatus.unknownStatus");
});

const statusIcon = computed(() => {
  const status = statuses.value.find(s => s.value === props.activeStatus);
  return status ? status.icon : ElIcon;
});

const positionStyles = computed(() => ({
  top: typeof props.top === "number" ? `${props.top}px` : props.top,
  left: typeof props.left === "number" ? `${props.left}px` : props.left,
  transformOrigin: "top left",
  transform: props.scaled ? "scale(0.6)" : "scale(1)"
}));
</script>

<style scoped>
.status-widget {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background-color: #f0f0f0f0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
}
.status-item {
  position: relative;
  padding: 0.5rem 1rem;
  border-radius: 4px;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

/* 闪烁效果的 CSS 类 */
.blink {
  animation: blink 1s ease-in-out infinite alternate;
}

@keyframes blink {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
</style>
