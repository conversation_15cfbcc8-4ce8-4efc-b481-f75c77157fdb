<!-- 
 * @Description: 单文件流转履历
 * @Date: 2024-08-11
!-->
<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      title="单文件流转履历"
      :columns="columns"
      :request-api="fetchBatteryResumeData"
      :init-param="initParam"
      :data-callback="dataCallback"
    >
      <!-- 表格 header 按钮 -->
      <template #tableHeader>
        <el-space>
          <s-button icon="download" @click="exportData">导出</s-button>
          <s-button @click="batchQuery">批量查询</s-button>
        </el-space>
      </template>
      <!-- 操作 -->
      <template #operation="scope">
        <s-button type="primary" size="small" @click="viewResume(scope.row)">查看履历</s-button>
      </template>
    </ProTable>

    <!-- 批量查询弹窗 -->
    <el-dialog v-model="batchQueryVisible" title="批量查询" width="500px">
      <!-- 批量查询内容 -->
    </el-dialog>

    <!-- 查看履历弹窗 -->
    <el-dialog v-model="resumeVisible" title="流转履历" width="80%" destroy-on-close>
      <div class="resume-dialog">
        <!-- 筛选条件 -->
        <div class="filter-container">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="产品">
                <el-select v-model="resumeFilter.product" placeholder="请选择" clearable>
                  <el-option label="热压后锂电芯-162Ah" value="热压后锂电芯-162Ah"></el-option>
                  <el-option label="一次卷绕半成品电芯-162Ah" value="一次卷绕半成品电芯-162Ah"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工单号">
                <el-select v-model="resumeFilter.workOrderNo" placeholder="请选择" clearable>
                  <el-option label="12000000775" value="12000000775"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="派工单号">
                <el-select v-model="resumeFilter.dispatchNo" placeholder="请选择" clearable>
                  <el-option label="12000000775-20250408-L-2012" value="12000000775-20250408-L-2012"></el-option>
                  <el-option label="12000000775-20250410-L-Z121" value="12000000775-20250410-L-Z121"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="工序">
                <el-select v-model="resumeFilter.process" placeholder="请选择" clearable>
                  <el-option label="切膜" value="Z012"></el-option>
                  <el-option label="X-ray" value="Z030"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="filter-buttons">
            <s-button type="primary" @click="searchResume">查询</s-button>
            <s-button @click="resetResumeFilter">重置</s-button>
          </div>
        </div>

        <!-- 流转履历表格 -->
        <el-table :data="resumeTableData" style="width: 100%" border stripe>
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="productCode" label="产品编码" />
          <el-table-column prop="productName" label="产品名称" />
          <el-table-column prop="workOrderNo" label="工单号" />
          <el-table-column prop="dispatchNo" label="派工单号" />
          <el-table-column prop="processCode" label="工序编码" />
          <el-table-column prop="processName" label="工序名称" />
          <el-table-column prop="deviceCode" label="设备编码" />
          <el-table-column prop="deviceName" label="设备名称" />
          <el-table-column prop="flowResult" label="流转结果">
            <template #default="scope">
              <el-tag type="success" v-if="scope.row.flowResult === 'OK'">{{ scope.row.flowResult }}</el-tag>
              <el-tag type="danger" v-else>{{ scope.row.flowResult }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="shift" label="班次" />
          <el-table-column prop="shiftDate" label="班次日期" />
          <el-table-column prop="remark" label="载具序列" />
          <el-table-column prop="operation" label="操作" width="120">
            <template #default="scope">
              <s-button link type="primary" size="small" @click="viewMaterialConsumption(scope.row)">物料消耗</s-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="resumePagination.pageNum"
            v-model:page-size="resumePagination.pageSize"
            :page-sizes="[10, 20, 30, 40]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="resumePagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 物料消耗弹窗 -->
    <MaterialConsumption ref="materialConsumptionRef" />
  </div>
</template>

<script setup lang="ts" name="batteryResume">
import { ref, reactive } from "vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { ElMessage, ElMessageBox } from "element-plus";
import MaterialConsumption from "./components/materialConsumption.vue";

// 获取 ProTable 元素
const proTable = ref<ProTableInstance>();

// 初始化参数
const initParam = reactive({
  // 默认查询参数
});

// API调用函数 (需要根据实际API替换)
const fetchBatteryResumeData = () => {
  // 这里替换为实际的API调用
  return new Promise(resolve => {
    // 模拟API调用
    setTimeout(() => {
      // 使用图片中的示例数据
      const result = {
        code: 200,
        data: {
          list: [
            {
              id: "1",
              batteryCode: "720001362407122104531",
              standardCode: "-",
              productName: "热压后锂电芯-162Ah",
              workStation: "热压3下",
              inPerson: "[1896202757]认领",
              planTime: "-",
              outPerson: "-",
              inTime: "[1896202757]认领",
              outTime: "2024-08-10 17:56:43",
              outEndTime: "2024-08-10 17:58:04"
            }
          ],
          total: 1,
          pageSize: 20,
          pageNum: 1
        },
        msg: "成功"
      };
      resolve(result);
    }, 300);
  });
};

// 处理返回数据
const dataCallback = (data: any) => {
  return {
    list: data.list,
    total: data.total,
    pageNum: data.pageNum,
    pageSize: data.pageSize
  };
};

// 表格配置项，根据图片显示内容配置
const columns: ColumnProps[] = [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "id", label: "序号", width: 60 },
  {
    prop: "batteryCode",
    label: "电芯码/标准电芯码",
    width: 200,
    search: {
      el: "input",
      props: { placeholder: "请输入电芯码" }
    }
  },
  {
    prop: "standardCode",
    label: "标准芯码",
    search: {
      el: "select",
      props: { placeholder: "请选择", clearable: true }
    }
  },
  {
    prop: "status",
    label: "状态",
    search: {
      el: "select",
      props: { placeholder: "请选择", clearable: true }
    }
  },
  {
    prop: "workOrderNo",
    label: "工单号",
    search: {
      el: "select",
      props: { placeholder: "请选择", clearable: true }
    }
  },
  {
    prop: "product",
    label: "产品",
    search: {
      el: "select",
      props: { placeholder: "请选择", clearable: true }
    }
  },
  {
    prop: "currentProcess",
    label: "当前工序",
    search: {
      el: "select",
      props: { placeholder: "请选择", clearable: true }
    }
  },
  { prop: "productName", label: "产品名称" },
  { prop: "workOrderNo", label: "工单号" },
  { prop: "dispatchNo", label: "派工单号" },
  { prop: "status", label: "状态" },
  { prop: "processCode", label: "工序编码" },
  { prop: "processName", label: "工序名称" },
  { prop: "nextProcessCode", label: "下一工序编码" },
  { prop: "nextProcessName", label: "下一工序名称" },
  { prop: "flowResult", label: "流转结果" },
  { prop: "lineCode", label: "产线编码" },
  { prop: "lineName", label: "产线名称" },
  { prop: "stationCode", label: "工位编码" },
  { prop: "stationName", label: "工位名称" },
  { prop: "deviceCode", label: "设备编码" },
  { prop: "deviceName", label: "设备名称" },
  { prop: "shift", label: "班次" },
  { prop: "shiftDate", label: "班次日期" },
  { prop: "carrierCode", label: "载具编码" },
  { prop: "shimStation", label: "垫片工位" },
  { prop: "hotPressStation", label: "热压工位" },
  { prop: "inPerson", label: "进站人" },
  { prop: "inTime", label: "进站时间" },
  { prop: "expectEndTime", label: "预计结束时间" },
  { prop: "outPerson", label: "出站人" },
  { prop: "outTime", label: "出站时间" },
  { prop: "gear", label: "档位" },
  { prop: "createTime", label: "创建时间" },
  { prop: "operation", label: "操作", width: 100, fixed: "right" }
];

// 批量查询相关
const batchQueryVisible = ref(false);
const batchQuery = () => {
  batchQueryVisible.value = true;
};

// 导出数据
const exportData = () => {
  ElMessageBox.confirm("确认导出当前筛选条件下的数据?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    // 实现导出逻辑
    ElMessage.success("导出成功");
  });
};

// 查看履历相关
const resumeVisible = ref(false);
const resumeFilter = reactive({
  product: "",
  workOrderNo: "",
  dispatchNo: "",
  process: ""
});
const resumeTableData = ref([
  {
    id: "1",
    productCode: "2001140001",
    productName: "热压后锂电芯-162Ah",
    workOrderNo: "12000000775",
    dispatchNo: "12000000775-20250408-L-2012",
    processCode: "Z012",
    processName: "切膜",
    deviceCode: "JHM01C01312-00",
    deviceName: "切膜-铁科-L2-3",
    flowResult: "OK",
    shift: "白班",
    shiftDate: "2025-04-10",
    remark: "JHM01014"
  },
  {
    id: "2",
    productCode: "2001100001",
    productName: "一次卷绕半成品电芯-162Ah",
    workOrderNo: "12000000775",
    dispatchNo: "12000000775-20250410-L-Z121",
    processCode: "Z030",
    processName: "X-ray",
    deviceCode: "JHM01C03102-00",
    deviceName: "X-ray-L2",
    flowResult: "OK",
    shift: "白班",
    shiftDate: "2025-04-10",
    remark: "-"
  },
  {
    id: "3",
    productCode: "2001100001",
    productName: "一次卷绕半成品电芯-162Ah",
    workOrderNo: "12000000775",
    dispatchNo: "12000000775-20250410-L-Z121",
    processCode: "Z041",
    processName: "超声波侧续焊&模块检测&hipot1",
    deviceCode: "JHM01C07102-00",
    deviceName: "超声波侧续焊-L2",
    flowResult: "OK",
    shift: "白班",
    shiftDate: "2025-04-10",
    remark: "-"
  },
  {
    id: "4",
    productCode: "2001100001",
    productName: "一次卷绕半成品电芯-162Ah",
    workOrderNo: "12000000775",
    dispatchNo: "12000000775-20250410-L-Z121",
    processCode: "Z050",
    processName: "负极极耳贴顶片侧焊",
    deviceCode: "JHM01C09302-00",
    deviceName: "负极极耳贴顶片侧焊行-L2",
    flowResult: "OK",
    shift: "白班",
    shiftDate: "2025-04-10",
    remark: "-"
  }
]);
const resumePagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 4
});

// 查看履历
const viewResume = (row: any) => {
  console.log("查看履历", row);
  // 打开查看履历弹窗
  resumeVisible.value = true;
  // 这里可以根据row信息加载对应的履历数据
  // ElMessage.success(`查看电芯码 ${row.batteryCode} 的履历信息`);
};

// 查询履历
const searchResume = () => {
  console.log("查询履历", resumeFilter);
  // 实际场景中这里应该调用API获取数据
  ElMessage.success("查询成功");
};

// 重置履历筛选条件
const resetResumeFilter = () => {
  resumeFilter.product = "";
  resumeFilter.workOrderNo = "";
  resumeFilter.dispatchNo = "";
  resumeFilter.process = "";
};

// 分页处理
const handleSizeChange = (val: number) => {
  resumePagination.pageSize = val;
  // 实际场景中这里应该重新获取数据
};

const handleCurrentChange = (val: number) => {
  resumePagination.pageNum = val;
  // 实际场景中这里应该重新获取数据
};

// 物料消耗弹窗引用
const materialConsumptionRef = ref<InstanceType<typeof MaterialConsumption> | null>(null);

// 查看物料消耗
const viewMaterialConsumption = (row: any) => {
  console.log("查看物料消耗", row);
  materialConsumptionRef.value?.open(row);
};
</script>

<style lang="scss" scoped>
.table-box {
  width: 100%;
  height: 100%;
}
.resume-dialog {
  .filter-container {
    padding: 15px;
    margin-bottom: 20px;
    background-color: #f8f8f9;
    border-radius: 4px;
    .filter-buttons {
      margin-top: 15px;
      text-align: right;
      .el-button {
        margin-left: 10px;
      }
    }
  }
  .pagination-container {
    margin-top: 20px;
    text-align: right;
  }
}
</style>
