<template>
  <div class="dashboard">
    <el-button size="small" @click="exportData" class="export-btn">{{ $t("alarmRealtime.exportData") }}</el-button>
    <div class="alert-header">
      <span>{{ $t("alarmRealtime.currentAlarm.factory") }}</span>
      <span>{{ $t("alarmRealtime.currentAlarm.workshop") }}</span>
      <span>{{ $t("alarmRealtime.currentAlarm.productionLine") }}</span>
      <span>{{ $t("alarmRealtime.currentAlarm.machine") }}</span>
      <span>{{ $t("alarmRealtime.currentAlarm.station") }}</span>
      <span>{{ $t("alarmRealtime.currentAlarm.faultType") }}</span>
      <span>{{ $t("alarmRealtime.currentAlarm.faultName") }}</span>
      <span>{{ $t("alarmRealtime.currentAlarm.alarmInfo") }}</span>
      <span>{{ $t("alarmRealtime.currentAlarm.startTime") }}</span>
      <span>{{ $t("alarmRealtime.currentAlarm.duration") }}</span>
      <span>{{ $t("alarmRealtime.currentAlarm.handler") }}</span>
      <span>{{ $t("alarmRealtime.currentAlarm.status") }}</span>
    </div>
    <div class="right_bottom_wrap beautify-scroll-def" :class="{ 'overflow-y-auto': !indexConfig.rightBottomSwiper }">
      <component
        :is="comName"
        :list="props.alerts"
        v-model="state.scroll"
        :single-height="state.defaultOption.singleHeight"
        :step="state.defaultOption.step"
        :limit-scroll-num="state.defaultOption.limitScrollNum"
        :hover="state.defaultOption.hover"
        :single-wait-time="state.defaultOption.singleWaitTime"
        :wheel="state.defaultOption.wheel"
      >
        <div class="alert-list">
          <ul>
            <li v-for="(alert, index) in props.alerts" :key="index" class="alert-item">
              <span>{{ alert.factory }}</span>
              <span>{{ alert.workshop }}</span>
              <span>{{ alert.production_line }}</span>
              <span>{{ alert.machine }}</span>
              <span>{{ alert.station }}</span>
              <span>{{ alert.fault_type }}</span>
              <span>{{ alert.fault_name }}</span>
              <span>{{ alert.alarm_info }}</span>
              <span>{{ alert.start_time }}</span>
              <span>{{ alert.duration_minutes }}</span>
              <span>{{ alert.man }}</span>
              <span>{{ alert.status }}</span>
            </li>
          </ul>
        </div>
      </component>
    </div>
  </div>
</template>

<script setup lang="ts">
import SeamlessScroll from "@/components/seamless-scroll";
import { computed, onMounted, reactive, ref, defineProps } from "vue";
import EmptyCom from "@/components/empty-com";
import { exportMultipleTablesToExcel } from "@/utils";
import moment from "moment";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

// 定义父组件传入的 props
const props = defineProps({
  alerts: {
    type: Array as () => Array<{
      top10_alarm_count: number;
      top10_fault_time: number;
      factory: string;
      workshop: string;
      production_line: string;
      machine: string;
      station: string;
      fault_type: string;
      fault_name: string;
      alarm_info: string;
      start_time: string;
      duration_minutes: number;
      man: string;
      status: string;
    }>,
    default: () => []
  }
});

const defaultOption = ref({
  step: 4, // 数值越大速度滚动越快
  hover: true, // 是否开启鼠标悬停stop
  wheel: false, //在开启鼠标悬停的情况下是否开启滚轮滚动，默认不开启
  openWatch: true, // 开启数据实时监控刷新dom
  direction: 1, // 0向下 1向上 2向左 3向右
  limitScrollNum: 2, // 开始无缝滚动的数据量 this.dataList.length
  singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
  singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
  singleWaitTime: 3000 // 单步运动停止的时间(默认值1000ms)
});

const indexConfig = ref({
  leftBottomSwiper: true, //左轮播
  rightBottomSwiper: true //右下轮播
});

const state = reactive<any>({
  list: [],
  defaultOption: {
    ...defaultOption.value,
    singleHeight: 252,
    limitScrollNum: 4,
    singleWaitTime: 1000,
    step: 1.5
  },
  scroll: true
});

const getData = () => {
  // 这里由于数据由父组件传入，不需要再请求数据
};

const comName = computed(() => {
  if (indexConfig.value.rightBottomSwiper) {
    return SeamlessScroll;
  } else {
    return EmptyCom;
  }
});

const exportData = () => {
  const headers = [
    t("alarmRealtime.currentAlarm.factory"),
    t("alarmRealtime.currentAlarm.workshop"),
    t("alarmRealtime.currentAlarm.productionLine"),
    t("alarmRealtime.currentAlarm.machine"),
    t("alarmRealtime.currentAlarm.station"),
    t("alarmRealtime.currentAlarm.faultType"),
    t("alarmRealtime.currentAlarm.faultName"),
    t("alarmRealtime.currentAlarm.alarmInfo"),
    t("alarmRealtime.currentAlarm.startTime"),
    t("alarmRealtime.currentAlarm.duration")
  ];
  const data = props.alerts.map(alert => [
    alert.factory,
    alert.workshop,
    alert.production_line,
    alert.machine,
    alert.station,
    alert.fault_type,
    alert.fault_name,
    alert.alarm_info,
    alert.start_time,
    alert.duration_minutes
  ]);

  const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");
  const fileName = `${t("alarmRealtime.currentAlarm.title")}_${currentTime}.xlsx`;
  const arr = [headers, ...data];
  exportMultipleTablesToExcel([arr], [t("alarmRealtime.currentAlarm.title")], fileName);
};

onMounted(() => {
  getData();
});

defineExpose({
  exportData
});
</script>

<style scoped lang="scss">
/* 全局变量 */
$primary-color: #409eff;
$bg-light: #f5f7fa;
$bg-medium: #e4e7ed;
$text-dark: rgb(0 0 0 / 90%);
$text-muted: rgb(0 0 0 / 60%);
$border-color: rgb(0 0 0 / 10%);
.dashboard {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  .export-btn {
    position: absolute;
    top: -1rem;
    right: 1rem;
    z-index: 10;
    padding: 0.5rem 1rem;
    color: white;
    background: $primary-color;
    border: none;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
    &:hover {
      background: darken($primary-color, 10%);
      transform: translateY(-1px);
    }
  }
  .alert-header {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: $bg-medium;
    border-radius: 8px;
    span {
      overflow: hidden;
      color: $text-dark;
      text-align: center;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .right_bottom_wrap {
    flex: 1;
    overflow: hidden;
    border-radius: 8px;

    /* 自定义滚动条 */
    &.overflow-y-auto {
      scrollbar-color: $primary-color $bg-light;
      scrollbar-width: thin;
      &::-webkit-scrollbar {
        width: 6px;
      }
      &::-webkit-scrollbar-track {
        background: $bg-light;
      }
      &::-webkit-scrollbar-thumb {
        background: $primary-color;
        border-radius: 3px;
      }
    }
    .alert-list {
      width: 100%;
      min-height: 200px;
      ul {
        padding: 0;
        margin: 0;
        list-style: none;
        li.alert-item {
          display: grid;
          grid-template-columns: repeat(12, 1fr);
          gap: 0.5rem;
          padding: 1rem 1.5rem;
          background: transparent;
          border-bottom: 1px solid $border-color;
          transition: all 0.3s ease;
          &:hover {
            background: rgb(64 158 255 / 10%);
            transform: translateX(4px);
          }
          span {
            overflow: hidden;
            font-size: 0.95rem;
            color: $text-dark;
            text-align: center;
            text-overflow: ellipsis;
            white-space: nowrap;
            &:nth-child(8) {
              /* 告警信息列特殊处理 */
              padding-left: 1rem;
              text-align: left;
            }
          }
        }
      }
    }
  }
}

/* 响应式设计 */
@media (width <= 1200px) {
  .dashboard {
    padding: 1rem;
  }
  .alert-header,
  .alert-item {
    grid-template-columns: repeat(6, 1fr);

    /* 隐藏部分列 */
    span:nth-child(7),
    span:nth-child(8),
    span:nth-child(9),
    span:nth-child(10) {
      display: none;
    }
  }
}

/* 状态标签样式 */
.status-tag {
  display: inline-flex;
  gap: 0.3rem;
  align-items: center;
  padding: 0.2rem 0.5rem;
  font-size: 0.85rem;
  border-radius: 4px;
  &.fault {
    color: white;
    background: #f56c6c;
  }
  &.warning {
    color: white;
    background: #e6a23c;
  }
  &.normal {
    color: white;
    background: #67c23a;
  }
}

/* 动画效果 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.alert-item {
  animation: slideIn 0.5s ease-out;
}

/* 分隔线样式 */
.divider {
  height: 1px;
  margin: 1.5rem 0;
  background: linear-gradient(to right, transparent, $border-color, transparent);
}
</style>
