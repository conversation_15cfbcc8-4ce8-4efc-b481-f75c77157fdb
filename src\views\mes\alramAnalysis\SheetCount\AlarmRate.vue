<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table']"
    @data-loaded="handleDataLoaded"
    :machines="machines"
    @export-data="handleExportData"
    chart-height="220px"
    ref="userTable"
  />
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import moment from "moment";
import { ElMessage } from "element-plus";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
import { computed } from "vue";

const { t } = useI18n();

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  machines: {
    type: Array as PropType<Array<{ id: string; name: string }>>,
    default: () => []
  }
});

const emits = defineEmits(["dataReady", "exportData"]);

// const machines = ref<any[]>([]);
// const handleExportData = (csvContent: string) => {
//   emits("exportData", { data: csvContent });
// };
// const handleExportData = () => {
//   try {
//     // 提取图表数据
//     const categories = chartOptions.value.xAxis.data; // X轴数据
//     // const seriesData = chartOptions.value.series[0].data.map(item => item.value); // Y轴数据
//     const seriesData = chartOptions.value.series[0]?.data.map(item => item.value) || []; // Y轴数据

//     // 构造二维数组
//     const data = [["时间", "故障率"]]; // 表头
//     categories.forEach((category, index) => {
//       data.push([category, seriesData[index]]);
//     });

//     // 转换为 CSV 字符串
//     const csvContent = data.map(row => row.join(",")).join("\n");

//     // 触发父组件事件
//     emits("exportData", { data: csvContent });
//   } catch (error) {
//     console.error("导出失败:", error);
//     ElMessage.error("导出失败，请查看控制台错误信息！");
//   }
// };
// 颜色配置
const COLORS = {
  failure_rate: "#00bfff",
  font: "#666",
  splitLine: "#eee"
};

// 存储图表数据的响应式变量
const chartData = ref<{ categories: string[]; seriesData: number[] }>({ categories: [], seriesData: [] });

// 图表配置
const chartOptions = computed(() => ({
  title: {
    subtext: t("alarmRealtime.alarmRate.title"),
    left: "center",
    textStyle: {
      fontSize: 16,
      color: COLORS.font
    },
    top: -12
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["bar", "line"],
        title: {
          bar: t("alarmRealtime.time.switchToBar"),
          line: t("alarmRealtime.time.switchToLine")
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const failure_rate = params[0];
      return `
        <div style="padding:5px;min-width:120px">
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.failure_rate};border-radius:50%"></span>
            ${t("common.time")}: ${failure_rate.name}<br/>
            ${t("alarmRealtime.alarmRate.rate")}: ${failure_rate.value.toFixed(2)}%
          </div>
        </div>
      `;
    }
  },
  xAxis: {
    type: "category",
    data: chartData.value.categories,
    axisLabel: {
      color: COLORS.font,
      interval: 0,
      rotate: 45,
      formatter: (value: string) => {
        if (value.includes("-")) {
          return value.split(" - ").join("\n");
        }
        return value;
      }
    }
  },
  yAxis: {
    type: "value",
    name: t("alarmRealtime.alarmRate.ratePercentage"),
    min: 0,
    axisLabel: {
      color: COLORS.font,
      formatter: (value: number) => `${value.toFixed(2)}%`
    },
    splitLine: { lineStyle: { color: COLORS.splitLine } }
  },
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true
  },
  series: [
    {
      name: t("alarmRealtime.alarmRate.rate"),
      type: "bar",
      data: chartData.value.seriesData,
      itemStyle: { color: COLORS.failure_rate },
      label: {
        show: true,
        position: "top",
        formatter: (params: any) => `${params.value.toFixed(2)}%`
      }
    }
  ]
}));
// 转换数据函数
const transformData = (
  responseData: any[],
  timeType: string
): {
  categories: string[];
  seriesData: number[];
  machines: any[];
} => {
  const timeMap = new Map();
  const machineSet = new Set();

  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Mon":
      formatPattern = "MM-DD";
      break;
    case "Year":
      formatPattern = "YYYY-MM";
      break;
    default:
      formatPattern = "HH:00";
  }

  responseData.forEach(item => {
    const timeKey = moment(item.start_time).format(formatPattern);
    const rate = item.failure_rate || 0;
    const machine = item.machine || "未知机台";

    if (!timeMap.has(timeKey)) {
      timeMap.set(timeKey, {
        time: timeKey,
        rate: 0,
        count: 0,
        machines: new Set()
      });
    }

    const timeData = timeMap.get(timeKey);
    timeData.rate += rate;
    timeData.count++;
    timeData.machines.add(machine);
    machineSet.add(machine);
  });

  const sortedTimes = Array.from(timeMap.entries())
    .map(([time, data]) => ({
      time,
      avgRate: data.rate / data.count
    }))
    .sort((a, b) => moment(a.time, formatPattern).valueOf() - moment(b.time, formatPattern).valueOf());

  return {
    categories: sortedTimes.map(item => item.time),
    seriesData: sortedTimes.map(item => item.avgRate),
    machines: Array.from(machineSet).map(machine => ({ id: machine, name: machine }))
  };
};

// 数据获取函数
const fetchData = async (params: { time: Date[]; machine?: string }): Promise<{ data: { categories: string[]; seriesData: number[] } }> => {
  try {
    const startTime = moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss.SSS");
    const endTime = moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss");

    const queryParams = {
      Deck: params.machine || (props.machines.length > 0 ? props.machines[0].id : ""),
      StartDate: startTime,
      EndDate: endTime,
      Type: 10
    };

    const response = await alramAnalysisApi.getListMesReportData(queryParams);
    const timeType = response.data.list.length > 0 && response.data.list[0].type ? response.data.list[0].type : "Hour";

    const data1 = transformData(response.data.list, timeType);
    // machines.value = data1.machines;

    const exposedata = {
      categories: data1.categories,
      seriesData: data1.seriesData,
      timeType
    };
    emits("dataReady", exposedata);

    return {
      data: {
        categories: data1.categories,
        seriesData: data1.seriesData
      }
    };
  } catch (error) {
    ElMessage.error(t("common.message.operationFailed"));
    console.error("获取故障率数据失败:", error);
    return {
      data: {
        categories: [],
        seriesData: []
      }
    };
  }
};
// const handleDataLoaded = (data: { categories: string[]; seriesData: number[] }) => {
//   if (!data.seriesData || data.seriesData.length === 0) {
//     chartOptions.value = {
//       ...chartOptions.value,
//       xAxis: {
//         ...chartOptions.value.xAxis,
//         data: []
//       },
//       series: []
//     };
//     return;
//   }

//   chartOptions.value = {
//     ...chartOptions.value,
//     xAxis: {
//       ...chartOptions.value.xAxis,
//       data: data.categories
//     },
//     series: [
//       {
//         name: "故障率",
//         type: "bar",
//         data: data.seriesData.map(value => ({
//           value,
//           itemStyle: { color: COLORS.failure_rate }
//         })),
//         label: {
//           show: true,
//           position: "top",
//           // 移除百分比格式化
//           formatter: (params: any) => `${params.value.toFixed(2)}`
//         }
//       }
//     ]
//   };
// };
// const handleDataLoaded = (data: { categories: string[]; seriesData: number[] }) => {
//   if (!data.seriesData || data.seriesData.length === 0) {
//     chartOptions.value = {
//       ...chartOptions.value,
//       xAxis: {
//         ...chartOptions.value.xAxis,
//         data: []
//       },
//       series: []
//     };
//     return;
//   }

//   chartOptions.value = {
//     ...chartOptions.value,
//     xAxis: {
//       ...chartOptions.value.xAxis,
//       data: data.categories
//     },
//     series: [
//       {
//         name: "故障率(%)",
//         type: "bar",
//         data: data.seriesData.map(value => ({
//           value,
//           itemStyle: { color: COLORS.failure_rate }
//         })),
//         label: {
//           show: true,
//           position: "top",
//           formatter: (params: any) => `${(params.value * 100).toFixed(2)}%`
//         }
//       }
//     ]
//   };
// };
// 图表配置（修改 series 数据格式）
const handleDataLoaded = (data: { categories: string[]; seriesData: number[] }) => {
  if (!data.seriesData || data.seriesData.length === 0) {
    chartOptions.value = {
      ...chartOptions.value,
      xAxis: { data: [] },
      series: []
    };
    return;
  }

  chartData.value = data;
};

// 导出数据
const handleExportData = () => {
  const categories = chartData.value.categories;
  const seriesData = chartData.value.seriesData;

  const data = [[t("common.time"), t("alarmRealtime.alarmRate.ratePercentage")]];
  categories.forEach((category, index) => {
    data.push([category, String(seriesData[index])]);
  });

  const csvContent = data.map(row => row.join(",")).join("\n");
  emits("exportData", { data: csvContent });
};
const userTable = ref(null);

defineExpose({
  tableRef: userTable
});

// 监听props.data变化
watch(
  () => props.data,
  newData => {
    if (newData && newData.length > 0) {
      const defaultParams = {
        time: [moment().startOf("day").toDate(), moment().endOf("day").toDate()],
        machine: ""
      };
      fetchData(defaultParams);
    }
  },
  { immediate: true }
);
</script>
