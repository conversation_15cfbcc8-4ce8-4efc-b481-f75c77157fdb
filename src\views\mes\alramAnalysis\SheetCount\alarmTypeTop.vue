<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table']"
    :machines="machines"
    @data-loaded="handleDataLoaded"
    @export-data="handleExportData"
    chart-height="222px"
    ref="userTable"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from "vue";
import moment from "moment";
import { ElMessage } from "element-plus";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";

const emits = defineEmits(["dataReady", "exportData"]);
const props = defineProps({
  machines: {
    type: Array as PropType<Array<{ id: string; name: string }>>,
    default: () => []
  }
});
// 机台列表
// const machines = ref<any[]>([]);
// 当前选中的机台
const currentMachine = ref<string>("");
// 存储当前活跃的请求控制器
let currentController: AbortController | null = null;

const { t } = useI18n();

const handleExportData = (csvContent: string) => {
  emits("exportData", { data: csvContent });
};

// 颜色配置
const COLORS = {
  fault_count: "#00bfff",
  font: "#666",
  splitLine: "#eee"
};

// 基础配置
const baseOptions = ref({
  title: {
    subtext: t("sheetCount.faultTypeTop10"),
    left: "center",
    textStyle: {
      fontSize: 16,
      color: COLORS.font
    },
    top: -12
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["bar", "line"],
        title: {
          bar: t("common.switchToBar"),
          line: t("common.switchToLine")
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const faultData = params[0];
      return `
        <div style="padding:5px;min-width:120px">
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.fault_count};border-radius:50%"></span>
            ${t("sheetCount.faultType")}: ${faultData.name}<br/>
            ${t("sheetCount.count")}: ${faultData.value}
          </div>
        </div>
      `;
    }
  },
  legend: {
    show: false
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      color: COLORS.font,
      interval: 0,
      rotate: 45
    }
  },
  yAxis: {
    type: "value",
    name: t("sheetCount.count"),
    min: 0,
    axisLabel: {
      color: COLORS.font
    },
    splitLine: { lineStyle: { color: COLORS.splitLine } }
  },
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true
  },
  series: []
});

// 监听语言变化，更新基础配置中的文本
watch(
  () => t("sheetCount.faultTypeTop10"),
  newValue => {
    if (baseOptions.value) {
      baseOptions.value.title.subtext = newValue;
    }
  },
  { immediate: true }
);

watch(
  () => t("common.switchToBar"),
  newValue => {
    baseOptions.value.toolbox.feature.magicType.title.bar = newValue;
  }
);

watch(
  () => t("common.switchToLine"),
  newValue => {
    baseOptions.value.toolbox.feature.magicType.title.line = newValue;
  }
);

watch(
  () => t("sheetCount.count"),
  newValue => {
    baseOptions.value.yAxis.name = newValue;
  }
);

// 图表配置
const chartOptions = computed(() => baseOptions.value);

// // 1. 获取机台列表（Type=0）
// const fetchMachines = async () => {
//   try {
//     console.log("开始获取机台列表"); // 调试日志
//     const response = await alramAnalysisApi.getListMesReportData({
//       Type: 0
//     });
//     console.log("机台列表接口响应:", response); // 调试日志
//     const machineList = response.data.list;
//     if (!machineList || machineList.length === 0) {
//       console.error("机台列表为空");
//       return;
//     }
//     // 根据实际字段名映射数据
//     machines.value = machineList.map(item => ({
//       id: item.MachineName || "未知机台", // 替换为实际字段名
//       name: item.MachineName || "未知机台"
//     }));
//     console.log("机台列表已更新:", machines.value); // 调试日志
//   } catch (error) {
//     console.error("机台列表请求失败:", error);
//     ElMessage.error("获取机台列表失败");
//   }
// };
// 数据获取函数
const fetchData = async (params: { time: Date[]; machine?: string }) => {
  try {
    const startTime = moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss.SSS");
    const endTime = moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString();
    const queryParams = {
      Deck: params.machine || (props.machines.length > 0 ? props.machines[0].id : ""),
      StartDate: startTime,
      EndDate: endTime,
      Type: 6
    };
    const response = await alramAnalysisApi.getListMesReportData({ ...queryParams });
    const { categories, seriesData } = transformData(response.data.list);
    // 更新图表标题显示当前机台
    chartOptions.value.title.subtext = `故障类型 Top10${currentMachine.value ? ` (机台: ${currentMachine.value})` : ""}`;
    emits("dataReady", {
      categories,
      seriesData: [seriesData],
      isCompare: false,
      listName: ["故障类型数量"]
    });
    return {
      data: {
        categories,
        seriesData: [seriesData]
      }
    };
  } catch (error) {
    if ((error as any).name === "CanceledError") {
      console.log("请求已取消");
    } else {
      ElMessage.error("获取故障类型数据失败");
      console.error("获取故障类型数据失败:", error);
    }
    return {
      data: {
        categories: [],
        seriesData: []
      }
    };
  }
};
// 转换数据函数
const transformData = (responseData: any[]) => {
  if (!responseData || !Array.isArray(responseData)) {
    return {
      categories: [],
      seriesData: []
    };
  }

  // 按故障数量排序并取前10
  const sortedData = [...responseData].sort((a, b) => (b.type_count || 0) - (a.type_count || 0)).slice(0, 10);

  const categories = sortedData.map(item => item.fault_type || "未知类型");
  const seriesData = sortedData.map(item => item.type_count || 0);

  return {
    categories,
    seriesData
  };
};

// 数据加载回调
const handleDataLoaded = (data: { categories: string[]; seriesData: number[][] }) => {
  const [seriesData] = data.seriesData;
  baseOptions.value = {
    ...baseOptions.value,
    title: {
      ...baseOptions.value.title,
      subtext: t("sheetCount.faultTypeTop10")
    },
    xAxis: {
      ...baseOptions.value.xAxis,
      data: data.categories
    },
    series: [
      {
        name: t("sheetCount.faultTypeCount"),
        type: "bar",
        data: seriesData,
        itemStyle: { color: COLORS.fault_count },
        label: {
          show: true,
          position: "top",
          formatter: "{c}"
        }
      }
    ]
  };
};

const userTable = ref<any>(null);

// 暴露方法供外部调用
defineExpose({
  tableRef: userTable
});

onMounted(async () => {
  // await fetchMachines();
});

// 组件卸载时取消未完成的请求
onUnmounted(() => {
  if (currentController) {
    currentController.abort();
  }
});
</script>
