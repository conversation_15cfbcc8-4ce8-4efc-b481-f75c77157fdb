<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'compare']"
    @data-loaded="handleDataLoaded"
    :compare-list="compareList"
    default-compare-id="total_electric"
    :machines="machines"
    :init-params="initParams"
    ref="userTable"
    @export-data="handleExportData"
    chart-height="222px"
  />
</template>

<script setup lang="tsx">
// import type { ColumnProps } from "@/components/ProTable/interface";
import moment from "moment";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const initParams = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
  type: 1
});
const machines = ref<any[]>([]);
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");
// 颜色配置常量
const COLORS = {
  total_electric: "#409EFF",
  total_gas: "#F56C6C",
  avg_electric: "#67C23A",
  avg_gas: "#E6A23C",
  font: chartColor,
  splitLine: "#eee"
};

const compareList = [
  {
    label: t("common.compareList.total_electric"),
    value: "total_electric"
  },
  {
    label: t("common.compareList.total_gas"),
    value: "total_gas"
  },
  {
    label: t("common.compareList.avg_electric"),
    value: "avg_electric"
  },
  {
    label: t("common.compareList.avg_gas"),
    value: "avg_gas"
  }
];

// 图表配置
const chartOptions = ref({
  title: [
    // 主标题 - 电能及气流量能耗统计
    {
      text: t("menu.energyConsumption"),
      left: "6%",
      top: 0,
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
        color: COLORS.font
      }
    },
    // 副标题 - 总生产量
    {
      subtext: t("common.compareList.total_electric") + ": 0", // 总生产量：0
      left: "center",
      top: -10, // 适当调整位置使其在主标题下方
      textStyle: {
        fontSize: 16,
        color: COLORS.font
      },
      subtextStyle: {
        color: COLORS.font
      }
    }
  ],
  // title: {
  //   // text: "能耗",
  //   // subtext: "总电能消耗：0",
  //   left: "center",
  //   textStyle: {
  //     fontSize: 16,
  //     color: COLORS.font
  //   },
  //   subtextStyle: {
  //     color: COLORS.font
  //   }
  //   // top: -2
  // },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: t("common.compareList.switchToLine"), // 切换为折线图
          bar: t("common.compareList.switchToBar") // 切换为柱状图
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const total_electric = params.find((p: any) => p.seriesName === t("common.compareList.total_electric"));
      const total_gas = params.find((p: any) => p.seriesName === t("common.compareList.total_gas"));
      const avg_electric = params.find((p: any) => p.seriesName === t("common.compareList.avg_electric"));
      const avg_gas = params.find((p: any) => p.seriesName === t("common.compareList.avg_gas"));
      return `
        <div style="padding:5px;min-width:120px">
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.total_electric};border-radius:50%"></span>
            ${t("common.compareList.total_electric")}: ${total_electric?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.total_gas};border-radius:50%"></span>
            ${t("common.compareList.total_gas")}: ${total_gas?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.avg_electric};border-radius:50%"></span>
            ${t("common.compareList.avg_electric")}: ${avg_electric?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.avg_gas};border-radius:50%"></span>
            ${t("common.compareList.avg_gas")}: ${avg_gas?.data}
          </div>
        </div>
      `;
    }
  },
  legend: {
    data: [
      t("common.compareList.total_electric"),
      t("common.compareList.total_gas"),
      t("common.compareList.avg_electric"),
      t("common.compareList.avg_gas")
    ],
    left: "12%",
    top: 18,
    textStyle: { color: COLORS.font }
  },
  xAxis: {
    type: "category",
    data: [],
    name: t("common.compareList.time"), //"时间",
    axisLabel: {
      color: COLORS.font,
      interval: 0,
      rotate: 45
    }
  },
  yAxis: [
    {
      type: "value",
      name: t("common.compareList.consumptionQuantity"), //"消耗数量",
      axisLabel: { color: COLORS.font },
      splitLine: { lineStyle: { color: COLORS.splitLine } }
    },
    {
      type: "value",
      name: t("common.compareList.consumptionQuantity"), //"气能 (m³)",
      min: 0,
      max: 200,
      axisLabel: {
        color: COLORS.avg_gas,
        formatter: (value: number) => `${value} m³`
      },
      position: "right"
    }
  ],
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true
  },
  series: []
});

function transformData(responseData, timeType) {
  const machineMap = new Map(); // 用于存储按机器分组的数据
  const timeSet = new Set(); // 用于存储所有时间点
  const machineIdSet = new Set(); // 用于存储所有机器ID

  let formatPattern; // 根据时间类型设置时间格式
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00"; // 小时级时间格式（如 "12:00"）
      break;
    case "Mon":
      formatPattern = "MM-DD"; // 月日格式（如 "01-05"）
      break;
    case "Year":
      formatPattern = "YYYY-MM"; // 年月格式（如 "2023-01"）
      break;
    default:
      formatPattern = "HH:00"; // 默认使用小时级
  }

  // 遍历原始数据，按机器和时间分组统计
  responseData.forEach(item => {
    const machine = item.machine; // 当前机器ID
    const timeKey = moment(item.start_time).format(formatPattern); // 当前时间点（按格式转换）

    // 初始化机器数据
    if (!machineMap.has(machine)) {
      machineMap.set(machine, {
        machine,
        timeBuckets: new Map(), // 按时间分组的桶
        totalElectric: 0, // 该机器总电能消耗
        totalGas: 0, // 该机器总气流量消耗
        avgElectric: 0, // 该机器平均电能消耗
        avgGas: 0 // 该机器平均气能消耗
      });
    }

    const machineData = machineMap.get(machine); // 获取当前机器的数据对象

    // 初始化当前时间桶（如果不存在）
    const bucket = machineData.timeBuckets.get(timeKey) || {
      total_electric: 0,
      total_gas: 0,
      avg_electric: 0,
      avg_gas: 0
    };

    // 累加当前数据到时间桶和总统计中
    bucket.total_electric += item.total_electric;
    bucket.total_gas += item.total_gas;
    bucket.avg_electric += item.avg_electric;
    bucket.avg_gas += item.avg_gas;
    machineData.timeBuckets.set(timeKey, bucket); // 更新时间桶

    machineData.totalElectric += item.total_electric; // 累加总电能
    machineData.totalGas += item.total_gas; // 累加总气流量
    machineData.avgElectric += item.avg_electric; // 累加平均电能
    machineData.avgGas += item.avg_gas; // 累加平均气能

    timeSet.add(timeKey); // 记录时间点
    machineIdSet.add(machine); // 记录机器ID
  });

  // 将时间点按时间顺序排序
  const categories = Array.from(timeSet).sort((a, b) => moment(a, formatPattern) - moment(b, formatPattern));

  // 对每个机器的数据按时间排序并格式化
  machineMap.forEach(machineData => {
    const sortedData = categories.map(timeKey => {
      const bucket = machineData.timeBuckets.get(timeKey) || {
        total_electric: 0,
        total_gas: 0,
        avg_electric: 0,
        avg_gas: 0
      };
      return {
        total_electric: bucket.total_electric,
        total_gas: bucket.total_gas,
        avg_electric: bucket.avg_electric,
        avg_gas: bucket.avg_gas
      };
    });

    // 将数据格式化为字符串（保留两位小数）
    machineData.total_electric = sortedData.map(d => d.total_electric.toFixed(2));
    machineData.total_gas = sortedData.map(d => d.total_gas.toFixed(2));
    machineData.avg_electric = sortedData.map(d => d.avg_electric.toFixed(2));
    machineData.avg_gas = sortedData.map(d => d.avg_gas.toFixed(2));
  });

  // 生成最终返回的数据结构
  const allmachine = Array.from(machineMap.values()); // 所有机器的数据
  const compare = [
    // 对比数据结构（可能冗余，需检查）
    {
      total_electric: allmachine.map(m => ({
        machine: m.machine,
        data: m.total_electric,
        total: m.totalElectric
      })),
      total_gas: allmachine.map(m => ({
        machine: m.machine,
        data: m.total_gas,
        total: m.totalGas
      })),
      avg_electric: allmachine.map(m => ({
        machine: m.machine,
        data: m.avg_electric,
        total: m.avgElectric
      })),
      avg_gas: allmachine.map(m => ({
        machine: m.machine,
        data: m.avg_gas,
        total: m.avgGas
      }))
    }
  ];

  return {
    allmachine,
    compare,
    categories,
    machines: Array.from(machineIdSet).map(id => ({ id, name: id }))
  };
}

const fetchData = async (params: any) => {
  // 构造时间查询参数（开始时间、结束时间）
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss"),
    EndDate: moment(params.time[1])
      .set({ hour: 23, minute: 59, second: 59 }) // 结束时间设为当天23:59:59
      .format("YYYY-MM-DD HH:mm:ss")
  };

  const query = { ...time, Type: 14 }; // 构造API查询参数

  // 调用API获取数据
  const { data } = await alramAnalysisApi.getListMesReportData(query);

  // 确定时间类型（从数据中获取）
  let mode = "Hour";
  if (data && data.list.length > 0) {
    mode = data.list[0].type; // 从第一条数据的type字段获取时间类型
  }

  // 转换数据
  const data1 = transformData(data.list, mode);
  machines.value = data1.machines; // 更新机器列表

  // 根据是否对比模式返回不同数据结构
  if (!params.compareMode) {
    const machine = params.machine;
    console.log(machine, "machine");
    // 非对比模式：获取指定机器的数据（默认第一个机器）
    const machineInfo = data1.allmachine.find(item => item.machine === params.machine) || data1.allmachine[0];
    const { total_electric, total_gas, avg_electric, avg_gas } = machineInfo;
    return {
      data: {
        categories: data1.categories,
        seriesData: [total_electric, total_gas, avg_electric, avg_gas], // 四个系列数据
        isCompare: false
      }
    };
  }

  // 对比模式：返回对比数据
  return {
    data: {
      isCompare: true,
      categories: data1.categories,
      compare: data1.compare
    }
  };
};

const handleDataLoaded = (data: any) => {
  if (!data.isCompare) {
    // 非对比模式：更新图表配置
    const [total_electric, total_gas, avg_electric, avg_gas] = data.seriesData;

    // 更新标题的副文本（总消耗量）
    // const totalElectricSum = total_electric.reduce((a, b) => a + b, 0);
    // const totalGasSum = total_gas.reduce((a, b) => a + b, 0);
    const totalElectricSum = total_electric.reduce((sum, num) => sum + parseFloat(num), 0);
    const totalGasSum = total_gas.reduce((sum, num) => sum + parseFloat(num), 0);
    const formattedTotalElectric = totalElectricSum.toFixed(2);
    const formattedTotalGas = totalGasSum.toFixed(2);

    chartOptions.value = {
      ...chartOptions.value,
      title: [
        // 主标题保持不变
        chartOptions.value.title[0],
        // 只更新副标题的内容
        {
          ...chartOptions.value.title[1],
          subtext: `${t("common.compareList.total_electric")}：${formattedTotalElectric} | ${t("common.compareList.total_gas")}：${formattedTotalGas}`
        }
      ],
      // title: {
      //   ...chartOptions.value.title,
      //   // text: "原材料预警",
      //   // left: "6%",
      //   // subtext: `总电能消耗：${totalElectricSum} | 总气流量消耗：${totalGasSum}`
      //   subtext: `${t("common.compareList.total_electric")}：${formattedTotalElectric} | ${t("common.compareList.total_gas")}：${formattedTotalGas}`,
      //   top: "-5%"
      // },
      xAxis: {
        ...chartOptions.value.xAxis,
        data: data.categories // 更新X轴时间点
      },
      series: [
        // 更新四个系列的数据
        {
          name: t("common.compareList.total_electric"), //"总电能消耗",
          type: "bar",
          data: total_electric,
          itemStyle: { color: COLORS.total_electric },
          label: {
            // 显示柱状图顶端数值
            show: true,
            position: "top",
            formatter: "{c}"
          }
        },
        {
          name: t("common.compareList.total_gas"), //"总气流量消耗",
          type: "bar",
          data: total_gas,
          itemStyle: { color: COLORS.total_gas },
          yAxisIndex: 1, // 使用右侧Y轴
          label: { show: true, position: "top", formatter: "{c}" }
        },
        {
          name: t("common.compareList.avg_electric"), //"平均电能消耗",
          type: "line",
          data: avg_electric.map(Number), // 将字符串转为数字
          itemStyle: { color: COLORS.avg_electric },
          label: { show: true, position: "top", formatter: "{c}" }
        },
        {
          name: t("common.compareList.avg_gas"), //"平均气能消耗",
          type: "line",
          data: avg_gas.map(Number),
          itemStyle: { color: COLORS.avg_gas },
          yAxisIndex: 1, // 使用右侧Y轴
          label: { show: true, position: "top", formatter: "{c}" }
        }
      ]
    };
  }
};
const emits = defineEmits(["data-ready", "export-data"]);

const handleExportData = (csvContent: string) => {
  // 父组件可以在这里做一些处理，然后继续传递给爷组件

  // eslint-disable-next-line vue/custom-event-name-casing
  emits("export-data", {
    data: csvContent
  });
};
const userTable = ref<any>(null);

defineExpose({
  tableRef: userTable
});
</script>
