<!-- 
 * @Description: 饼状图
 * @Author: huguo<PERSON> 
 * @Date: 2023-12-15 15:44:22
!-->
<template>
  <div id="pieChat" class="h-150px"></div>
</template>

<script setup lang="ts" name="pie<PERSON>hart">
import { OpLog, opLogApi } from "@/api";
import { Pie } from "@antv/g2plot";

onMounted(() => {
  opLogApi.pieChart().then(res => {
    createPieChat(res.data);
  });
});

/**
 * 创建折线图,具体文档参考https://g2plot.antv.antgroup.com/api/plots/pie
 * @param data 数据
 */
function createPieChat(data: OpLog.PineChart[]) {
  const piePlot = new Pie("pieChat", {
    appendPadding: 10,
    data,
    angleField: "value",
    colorField: "type",
    radius: 0.9,
    color: ["#409EFF", "#F5222D"],
    label: {
      type: "inner",
      offset: "-30%",
      content: ({ percent }) => `${(percent * 100).toFixed(0)}%`,
      style: {
        fontSize: 14,
        textAlign: "center"
      }
    },
    interactions: [{ type: "element-active" }]
  });
  piePlot.render(); //渲染
}
</script>

<style scoped lang="scss"></style>
