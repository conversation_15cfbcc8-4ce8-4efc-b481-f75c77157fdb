<template>
  <ProChart
    id="648696782794821"
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'compare']"
    @data-loaded="handleDataLoaded"
    :compare-list="compareList"
    default-compare-id="totalCount"
    :machines="machines"
    :init-params="initParams"
    ref="userTable"
    chart-height="222px"
  />
</template>

<script setup lang="tsx">
import moment from "moment";
import { productionReportCapacityApi } from "@/api";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const initParams = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
  type: 1
});
// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const machines = ref<any[]>([]);
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");

// 颜色配置常量
const COLORS = {
  production: "#00bfff",
  defective: "#ff4d4f",
  rate: "#fac858",
  okCount: "#52c41a",
  totalCount: "#1890ff",
  font: chartColor,
  splitLine: "#eee"
};

const compareList = [
  {
    label: t("productionReport.capacity.compareList.totalCount"),
    value: "totalCount"
  },
  {
    label: t("productionReport.capacity.compareList.okCount"),
    value: "okCount"
  },
  {
    label: t("productionReport.capacity.compareList.ngCount"),
    value: "ngCount"
  },
  {
    label: t("productionReport.capacity.compareList.rate"),
    value: "rate"
  }
];
// 图表配置
const chartOptions = ref({
  title: [
    {
      text: t("menu.capacity"),
      left: "6%",
      top: 0,
      textStyle: {
        fontSize: 16,
        fontWeight: "bold",
        color: COLORS.font
      }
    },
    {
      text: `${t("productionReport.capacity.compareList.totalCount")}：0 | ${t("productionReport.capacity.compareList.okCount")}：0 | ${t("productionReport.capacity.compareList.ngCount")}：0 | ${t("productionReport.capacity.compareList.rate")}：0%`, // 初始显示
      left: "center",
      top: 0,
      textStyle: {
        color: COLORS.font,
        fontSize: 12
      }
    }
  ],
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: t("productionReport.capacity.switchToLine"), // 切换为折线图
          bar: t("productionReport.capacity.switchToBar") // 切换为柱状图
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const totalCount = params.find((p: any) => p.seriesName === t("productionReport.capacity.compareList.totalCount"));
      const okCount = params.find((p: any) => p.seriesName === t("productionReport.capacity.compareList.okCount"));
      const ngCount = params.find((p: any) => p.seriesName === t("productionReport.capacity.compareList.ngCount"));
      const rate = params.find((p: any) => p.seriesName === t("productionReport.capacity.compareList.rate"));
      return `
        <div style="padding:5px;min-width:120px">
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.totalCount};border-radius:50%"></span>
             ${t("productionReport.capacity.compareList.totalCount")}: ${totalCount?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.okCount};border-radius:50%"></span>
            ${t("productionReport.capacity.compareList.okCount")}: ${okCount?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.defective};border-radius:50%"></span>
            ${t("productionReport.capacity.compareList.ngCount")}: ${ngCount?.data}
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.rate};border-radius:50%"></span>
            ${t("productionReport.capacity.compareList.rate")}: ${rate?.data}%
          </div>
        </div>
      `;
    }
  },
  legend: {
    data: [
      t("productionReport.capacity.compareList.totalCount"), // 生产总数
      t("productionReport.capacity.compareList.okCount"), // OK数
      t("productionReport.capacity.compareList.ngCount"), // NG数
      t("productionReport.capacity.compareList.rate") // 良率
    ],
    top: 13,
    textStyle: { color: COLORS.font }
  },
  xAxis: {
    type: "category",
    data: [],
    name: t("productionReport.capacity.time"),
    axisLabel: {
      color: COLORS.font,
      interval: 0,
      rotate: 45
    }
  },
  yAxis: [
    {
      type: "value",
      name: t("productionReport.capacity.productionQuantity"), // 生产数量
      axisLabel: { color: COLORS.font },
      splitLine: { lineStyle: { color: COLORS.splitLine } }
    },
    {
      type: "value",
      name: t("productionReport.capacity.yieldRateUnit"), // 良率 (%)
      min: 0,
      max: 100,
      axisLabel: {
        color: COLORS.font,
        formatter: (value: number) => `${value}%`
      },
      position: "right"
    }
  ],
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    // top: 100,
    containLabel: true
  },
  series: []
});
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineName || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // // 添加：设置默认选中第一个机台
    // if (machineList.value.length > 0) {
    //   searchParam.value.machine = machineList.value[0].id; // 设置默认机台 id
    // }

    console.log("机台列表已更新:", machineList.value);
  } catch (error) {
    console.error("机台列表请求失败:", error);
    ElMessage.error("获取机台列表失败");
  }
};
function transformData(responseData, timeType) {
  const machineMap = new Map();
  const timeSet = new Set();
  const machineIdSet = new Set();

  // 根据时间类型确定时间单位和格式
  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Mon":
      formatPattern = "MM-DD";
      break;
    case "Year":
      formatPattern = "YYYY-MM";
      break;
    default:
      formatPattern = "YYYY";
  }

  // 第一次遍历：聚合数据
  responseData.forEach(item => {
    const machine = item.deck;
    const timeKey = moment(item.starttime).format(formatPattern);

    if (!machineMap.has(machine)) {
      machineMap.set(machine, {
        machine,
        timeBuckets: new Map(), // 存储时间段聚合数据
        totalProduction: 0,
        totalDefective: 0
      });
    }

    const machineData = machineMap.get(machine);
    const bucket = machineData.timeBuckets.get(timeKey) || {
      production: 0,
      defective: 0
    };

    // 累加数据到时间段
    bucket.production += item.prodCount;
    bucket.defective += item.badCount;
    machineData.timeBuckets.set(timeKey, bucket);

    // 更新总统计
    machineData.totalProduction += item.prodCount;
    machineData.totalDefective += item.badCount;

    // 记录存在的时间点和机台
    timeSet.add(timeKey);
    machineIdSet.add(machine);
  });

  // 第二次遍历：生成排序后的时间序列
  const categories = Array.from(timeSet).sort((a, b) => moment(a, formatPattern) - moment(b, formatPattern));

  machineMap.forEach(machineData => {
    // 确保所有时间点都有数据（没有的补0）
    const sortedData = categories.map(timeKey => {
      const bucket = machineData.timeBuckets.get(timeKey) || {
        production: 0,
        defective: 0
      };

      const okCount = bucket.production - bucket.defective;
      const totalCount = bucket.production;
      return {
        production: bucket.production,
        defective: bucket.defective,
        okCount: okCount,
        totalCount: totalCount,
        rate: bucket.production > 0 ? (((bucket.production - bucket.defective) / bucket.production) * 100).toFixed(1) : 0
      };
    });

    // 转换为需要的数组格式
    machineData.production = sortedData.map(d => d.production);
    machineData.defective = sortedData.map(d => d.defective);
    machineData.okCount = sortedData.map(d => d.okCount);
    machineData.totalCount = sortedData.map(d => d.totalCount);
    machineData.rate = sortedData.map(d => parseFloat(d.rate));

    // 计算总合格率
    machineData.totalRate =
      machineData.totalProduction > 0
        ? (((machineData.totalProduction - machineData.totalDefective) / machineData.totalProduction) * 100).toFixed(1)
        : 0;
  });

  // 构建最终数据结构
  const allmachine = Array.from(machineMap.values());
  const compare = [
    {
      totalCount: allmachine.map(m => ({
        machine: m.machine,
        data: m.totalCount,
        total: m.totalProduction
      })),
      okCount: allmachine.map(m => ({
        machine: m.machine,
        data: m.okCount,
        total: m.totalProduction - m.totalDefective
      })),
      ngCount: allmachine.map(m => ({
        machine: m.machine,
        data: m.defective,
        total: m.totalDefective
      })),
      rate: allmachine.map(m => ({
        machine: m.machine,
        data: m.rate,
        total: parseFloat(m.totalRate)
      }))
    }
  ];

  return {
    allmachine,
    compare,
    categories,
    machines: Array.from(machineIdSet).map(id => ({ id, name: id }))
  };
}

// 修改数据获取函数
const fetchData = async (params: any) => {
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
    EndDate: moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString()
  };
  const query = {
    ...time,
    type: params.type
  };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);

  // 从数据中获取 mode 类型
  let mode = "Hour"; // 默认值
  if (data && data.list.length > 0) {
    mode = data.list[0].type;
  }

  const data1 = transformData(data.list, mode);
  machines.value = data1.machines;

  // 普通模式数据请求
  if (!params.compareMode) {
    const machine = params.machine;
    const machineInfo = data1.allmachine.find(item => item.machine === machine) || data1.allmachine[0];
    if (!machineInfo) {
      console.error(`未找到机台 ${machine} 的数据`);
      return {
        data: {
          categories: ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"],
          seriesData: [[], [], [], []],
          isCompare: false
        }
      };
    }
    const { defective, okCount, totalCount, rate } = machineInfo;
    return {
      data: {
        categories: data1.categories,
        seriesData: [totalCount, okCount, defective, rate],
        isCompare: false
      }
    };
  }

  // 对比模式数据请求
  return {
    data: {
      isCompare: true,
      categories: data1.categories,
      compare: data1.compare
    }
  };
};

// 修改数据加载回调
const handleDataLoaded = (data: any) => {
  // 普通模式数据处理
  if (!data.isCompare) {
    const [totalCount, okCount, ngCount, rate] = data.seriesData;

    const totalSum = totalCount.reduce((a: number, b: number) => a + b, 0);
    const okSum = okCount.reduce((a: number, b: number) => a + b, 0);
    const ngSum = ngCount.reduce((a: number, b: number) => a + b, 0);
    const overallRate = totalSum > 0 ? ((okSum / totalSum) * 100).toFixed(1) : 0;

    const newTitle = [...chartOptions.value.title];
    newTitle[1] = {
      ...newTitle[1],
      text: `${t("productionReport.capacity.compareList.totalCount")}：${totalSum} | ${t("productionReport.capacity.compareList.okCount")}：${okSum} | ${t("productionReport.capacity.compareList.ngCount")}：${ngSum} | ${t("productionReport.capacity.compareList.rate")}：${overallRate}%`
    };

    chartOptions.value = {
      ...chartOptions.value,
      title: newTitle,
      xAxis: {
        ...chartOptions.value.xAxis,
        data: data.categories
      },
      label: {
        show: true,
        position: "top",
        formatter: "{c}"
      },
      series: [
        {
          name: t("productionReport.capacity.compareList.totalCount"),
          type: "line",
          data: totalCount,
          itemStyle: { color: COLORS.totalCount },
          lineStyle: { color: COLORS.totalCount },
          label: {
            show: true,
            position: "top",
            formatter: "{c}"
          }
        },
        {
          name: t("productionReport.capacity.compareList.okCount"),
          type: "bar",
          data: okCount,
          itemStyle: { color: COLORS.okCount },
          label: {
            show: true,
            position: "top",
            formatter: "{c}"
          }
        },
        {
          name: t("productionReport.capacity.compareList.ngCount"),
          type: "bar",
          data: ngCount,
          itemStyle: { color: COLORS.defective },
          label: {
            show: true,
            position: "top",
            formatter: "{c}"
          }
        },
        {
          name: t("productionReport.capacity.compareList.rate"),
          type: "line",
          yAxisIndex: 1,
          data: rate,
          itemStyle: { color: COLORS.rate },
          label: {
            show: true,
            position: "top",
            formatter: "{c}%"
          }
        }
      ]
    };
  }
};
// 添加 onMounted 钩子，页面加载时自动触发搜索
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
});
const userTable = ref<any>(null);

defineExpose({
  tableRef: userTable
});
</script>
