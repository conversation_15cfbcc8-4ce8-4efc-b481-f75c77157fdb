<template>
  <div class="main-box">
    <div class="table-main">
      <div class="top flex flex-col" id="651546615074885">
        <SearchForm
          class="min-h-[60px]"
          :columns="searchColumns"
          :search-param="searchParam"
          :search-col="4"
          :search="handleSearch"
          :reset="handleReset"
        >
          <template #any>
            <el-button @click="exportData" :icon="Document">{{ $t("alarmRealtime.exportTable") }}</el-button>
            <el-button @click="exportImgs" :icon="Picture">{{ $t("alarmRealtime.exportImage") }}</el-button>
          </template>
        </SearchForm>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 第一行 -->
          <div class="bg-white p-4 rounded-lg shadow-sm h-[300px]">
            <Time ref="timeRef" :data="timeChartData" id="time" class="w-full h-full" />
          </div>
          <div class="bg-white p-4 rounded-lg shadow-sm h-[300px]">
            <Long ref="longRef" :data="longChartData" id="long" class="w-full h-full" />
          </div>

          <!-- 第二行 -->
          <div class="bg-white p-4 rounded-lg shadow-sm h-[300px]">
            <AlarmRate ref="alarmRateRef" :data="alarmRateChartData" id="AlarmRate" class="w-full h-full" />
          </div>
          <div class="bg-white p-4 rounded-lg shadow-sm h-[300px]">
            <Mttr ref="mttrRef" :data="mttrChartData" id="Mttr" class="w-full h-full" />
          </div>
        </div>
        <div class="bottom h-[200px] card m-t[10px] overflow-hidden" id="CurrentAlarm">
          <CurrentAlarm ref="currentAlarmRef" :alerts="currentAlarmData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { ref, onMounted } from "vue";
import { Document, Picture } from "@element-plus/icons-vue";
import { exportElementsAsImages, exportMultipleTablesToExcel, transformChart } from "@/utils";
import Time from "./Time.vue";
import Long from "./Long.vue";
import AlarmRate from "./AlarmRate.vue";
import Mttr from "./Mttr.vue";
import CurrentAlarm from "./CurrentAlarm.vue";
import moment from "moment";
import { ColumnProps } from "@/components/ProChart/interface";
import { productionReportCapacityApi } from "@/api";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

onMounted(async () => {
  handleSearch();
});
const timeRef = ref();
const longRef = ref();
const alarmRateRef = ref();
const mttrRef = ref();
const currentAlarmRef = ref();
const machines = ref([
  {
    id: "产线1",
    name: "产线1"
  }
]);
const handleMachineChange = (value: any) => {
  console.log(value, "value");
  // machines.value = value;
};
const handleReset = () => {
  searchParam.value = {
    time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")]
  };
};

const searchParam = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")]
});

const shitrender = () => {
  return <MachineSwitcher machine-list={machines.value} onChange={handleMachineChange} />;
};
const searchColumns = ref<ColumnProps[]>([
  {
    prop: "time",
    label: t("alarmRealtime.time1"),
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": "Start date",
        "end-placeholder": "End date"
      }
    },
    isShow: false
  },
  {
    prop: "machine",
    label: t("alarmRealtime.machine"),
    search: {
      isCustom: true,
      render: shitrender
    },
    isShow: false
  }
]);

const timeChartData = ref<any>([]);
const longChartData = ref<any>([]);
const alarmRateChartData = ref<any>([]);
const mttrChartData = ref<any>([]);
const currentAlarmData = ref<any>([]);
function mergeMachineData(data: any[]) {
  const machineMap = new Map();

  data.forEach(item => {
    if (!machineMap.has(item.machine)) {
      machineMap.set(item.machine, {
        ...item,
        // 初始化累加字段
        fault_duration: 0,
        fault_count: 0,
        failure_rate: [],
        // 保留第一个出现的非数值字段
        station: item.station,
        type: item.type,
        start_time: item.start_time,
        end_time: item.end_time
      });
    }

    const existing = machineMap.get(item.machine);
    // 数值累加
    existing.fault_duration += item.fault_duration;
    existing.fault_count += item.fault_count;
    existing.failure_rate.push(item.failure_rate);

    // 计算MTTR和MTBF（加权平均）
    existing.mttr_minutes = (existing.mttr_minutes * (existing.failure_rate.length - 1) + item.mttr_minutes) / existing.failure_rate.length;

    existing.mtbf_minutes = (existing.mtbf_minutes * (existing.failure_rate.length - 1) + item.mtbf_minutes) / existing.failure_rate.length;
  });

  // 计算平均故障率
  return Array.from(machineMap.values()).map(item => ({
    ...item,
    mttr_minutes: parseFloat(item.mttr_minutes.toFixed(2)),
    mtbf_minutes: parseFloat(item.mtbf_minutes.toFixed(2)),
    failure_rate: parseFloat((item.failure_rate.reduce((a: number, b: number) => a + b, 0) / item.failure_rate.length).toFixed(2))
  }));
}

const handleSearch = async () => {
  const query = {
    StartDate: moment(searchParam.value.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
    EndDate: moment(searchParam.value.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString(),
    Type: 10
  };
  try {
    const { data } = await productionReportCapacityApi.getListMesReportData(query);
    const data1 = mergeMachineData(data.list as any[]);

    timeChartData.value = data1.map(item => ({ machine_name: item.machine, fault_count: item.fault_count }));
    longChartData.value = data1.map(item => ({ machine_name: item.machine, duration_minutes: item.fault_duration }));
    alarmRateChartData.value = data1.map(item => ({ machine_name: item.machine, fault_count: item.fault_count, total_runs: 100 }));
    mttrChartData.value = data1.map(item => ({
      machine_name: item.machine,
      total_repair_time: item.mttr_minutes,
      fault_count: item.fault_count,
      total_working_time: item.mtbf_minutes
    }));
    currentAlarmData.value = data1.map(item => ({
      top10_alarm_count: item.fault_count,
      top10_fault_time: item.fault_duration,
      factory: "工厂1",
      workshop: "车间1",
      production_line: "线体1",
      machine: item.machine,
      station: item.station,
      fault_type: "故障类型1",
      fault_name: "故障名称1",
      alarm_info: "告警信息1",
      start_time: item.start_time,
      duration_minutes: item.fault_duration,
      man: "暂无",
      status: "未处理"
    }));
  } catch (error) {
    console.error("获取数据失败:", error);
  }
};

const exportData = () => {
  const arr1 = transformChart(timeRef.value.chartRef, t("alarmRealtime.machine"));
  const arr2 = transformChart(longRef.value.chartRef, t("alarmRealtime.machine"));
  const arr3 = transformChart(alarmRateRef.value.chartRef, t("alarmRealtime.machine"));
  const arr4 = transformChart(mttrRef.value.chartRef, t("alarmRealtime.machine"));

  const headers = [
    t("alarmRealtime.currentAlarm.factory"),
    t("alarmRealtime.currentAlarm.workshop"),
    t("alarmRealtime.currentAlarm.productionLine"),
    t("alarmRealtime.currentAlarm.machine"),
    t("alarmRealtime.currentAlarm.station"),
    t("alarmRealtime.currentAlarm.faultType"),
    t("alarmRealtime.currentAlarm.faultName"),
    t("alarmRealtime.currentAlarm.alarmInfo"),
    t("alarmRealtime.currentAlarm.startTime"),
    t("alarmRealtime.currentAlarm.duration")
  ];
  const currentAlarmDataArray = currentAlarmData.value.map(alert => [
    alert.factory,
    alert.workshop,
    alert.production_line,
    alert.machine,
    alert.station,
    alert.fault_type,
    alert.fault_name,
    alert.alarm_info,
    alert.start_time,
    alert.duration_minutes
  ]);

  const arr5 = [headers, ...currentAlarmDataArray];

  const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");
  const fileName = `${t("alarmRealtime.title")}_${currentTime}.xlsx`;

  exportMultipleTablesToExcel(
    [arr1, arr2, arr3, arr4, arr5],
    [
      t("alarmRealtime.charts.time"),
      t("alarmRealtime.charts.long"),
      t("alarmRealtime.charts.alarmRate"),
      t("alarmRealtime.charts.mttr"),
      t("alarmRealtime.charts.currentAlarm")
    ],
    fileName
  );
};

const exportImgs = () => {
  const startTime = moment(searchParam.value.time[0]).format("YYYY-MM-DD");
  const endTime = moment(searchParam.value.time[1]).format("YYYY-MM-DD");
  const exportItems = [
    { elementId: "long", fileName: `${t("alarmRealtime.charts.long")}${startTime}-${endTime}.png` },
    { elementId: "time", fileName: `${t("alarmRealtime.charts.time")}${startTime}-${endTime}.png` },
    { elementId: "AlarmRate", fileName: `${t("alarmRealtime.charts.alarmRate")}${startTime}-${endTime}.png` },
    { elementId: "Mttr", fileName: `${t("alarmRealtime.charts.mttr")}${startTime}-${endTime}.png` },
    { elementId: "CurrentAlarm", fileName: `${t("alarmRealtime.charts.currentAlarm")}${startTime}-${endTime}.png` }
  ];
  try {
    exportElementsAsImages(exportItems);
    console.log(t("alarmRealtime.export.success"));
  } catch (error) {
    console.error(t("alarmRealtime.export.error"));
  }
};
</script>

<style lang="scss" scoped></style>
