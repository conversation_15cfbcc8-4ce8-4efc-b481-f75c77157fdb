<template>
  <div class="main-box" id="651513609064517">
    <!-- {{ treeData }} -->
    <TreeFilter
      :default-expand-all="false"
      ref="treeFilter"
      label="name"
      :title="t('deviceProtect.dailyProtect.organizationList')"
      :request-api="deviceProtectdailyProtectApi.getTree"
      @change1="handleNodeClick"
    >
      <template #shit>
        <CalendarCustom current-theme="light" :header-title="title" :date-data="dateData"> </CalendarCustom>
      </template>
    </TreeFilter>
    <!-- 左侧树状结构 -->
    <!-- <el-tree :data="treeData" :props="treeProps" @node-click="handleNodeClick" class="custom-tree"></el-tree> -->
    <div class="table-box">
      <!-- 右侧表格 -->
      <ProTable
        v-if="!isChart"
        ref="proTableRef"
        :init-param="initParam"
        :columns="columns"
        :request-api="fetchFilteredData"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      >
        <!-- 操作按钮 -->
        <template #tableHeader>
          <div class="button-group">
            <el-button type="primary" @click="openAddDialog">{{ t("deviceProtect.dailyProtect.add") }}</el-button>
            <el-button type="success" @click="importData">{{ t("deviceProtect.import") }}</el-button>
            <el-button type="primary" @click="exportData">{{ t("deviceProtect.dailyProtect.export") }}</el-button>
            <el-button type="danger" :disabled="!selectedRows.length" @click="deleteSelectedRows">{{
              t("deviceProtect.dailyProtect.batchDelete")
            }}</el-button>
          </div>
        </template>
        <!-- <template #toolButton>
          <el-tooltip content="切换表格" placement="top">
            <el-button :icon="Switch" circle @click="changeTable" />
          </el-tooltip>
        </template> -->
        <!-- 操作列 -->
        <template #operation="{ row }">
          <div class="operation-buttons">
            <el-button type="primary" size="small" @click="openEditDialog(row)">{{ t("deviceProtect.dailyProtect.edit") }}</el-button>
            <el-button type="danger" size="small" @click="deleteRow(row)">{{ t("deviceProtect.dailyProtect.delete") }}</el-button>
            <el-button type="info" size="small" @click="openViewDialog(row)">{{ t("deviceProtect.dailyProtect.view") }}</el-button>
          </div>
        </template>
      </ProTable>
      <SwitchChart :init-params="initParam" v-else @toggle-view="changeTable"></SwitchChart>

      <!-- 公共弹窗组件 -->
      <EditForm
        v-model:visible="dialogVisible"
        :title="dialogTitle"
        :form-items="formItems"
        :initial-data="currentRowData"
        :view-mode="dialogType === 'view'"
        :save-text="t('rawMaterialWarning.save')"
        :close-text="t('rawMaterialWarning.close')"
        :machine-list="machineList"
        @save="handleSave"
        @select-change="handleSelectChange"
      />
      <!-- 导入文件选择框 -->
      <el-dialog v-model="importDialogVisible" :title="t('deviceProtect.dailyProtect.import.title')">
        <el-upload ref="uploadRef" action="/api/sys/upload/UploadExcelrcwh" :auto-upload="false" :before-upload="beforeUpload">
          <el-button type="primary">{{ t("deviceProtect.dailyProtect.import.selectFile") }}</el-button>
        </el-upload>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="importDialogVisible = false">{{ t("deviceProtect.dailyProtect.import.cancel") }}</el-button>
            <el-button type="primary" @click="startImport">{{ t("deviceProtect.dailyProtect.import.startImport") }}</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage } from "element-plus"; // 引入 ElMessage
import { deviceProtectdailyProtectApi, productionReportCapacityApi } from "@/api";
import * as XLSX from "xlsx";
// import moment from "moment";
import SwitchChart from "./SwitchChart.vue";
import { clearObjectValues, convertCalendarData } from "@/utils";
// import { Switch } from "@element-plus/icons-vue";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
import { getTableColumns, getFormItems, getInitialFormData } from "./config/index";
// 计算弹窗标题
const dialogTitle = computed(() => {
  const titles = {
    add: t("deviceProtect.dailyProtect.add"),
    edit: t("deviceProtect.dailyProtect.edit"),
    view: t("deviceProtect.dailyProtect.view")
  };
  return titles[dialogType.value] || "弹窗";
});
const currentRowData = ref({});
const columns = computed(() => {
  const cols = getTableColumns(t);

  // 动态设置机台选项
  const machineOptions = machineList.value.map(m => ({
    label: m.id,
    value: m.id
  }));

  const machineNameOptions = machineList.value.map(m => ({
    label: m.name,
    value: m.name
  }));

  // 更新机台相关列的配置
  cols.forEach(col => {
    if (col.prop === "machCode") {
      col.enum = machineOptions;
      col.search.on = {
        change: (val: string) => {
          const selectedMachine = machineList.value.find(machine => machine.id === val);
          proTableRef.value.searchParam.machCode = val;
          proTableRef.value.searchParam.mach = selectedMachine?.name || "";
          proTableRef.value.getTableList();
        }
      };
    } else if (col.prop === "mach") {
      col.enum = machineNameOptions;
      col.search.on = {
        change: (val: string) => {
          const selectedMachine = machineList.value.find(machine => machine.name === val);
          proTableRef.value.searchParam.mach = val;
          proTableRef.value.searchParam.machCode = selectedMachine?.id || "";
          proTableRef.value.getTableList();
        }
      };
    }
  });

  return cols;
});
// 表单字段配置
const formItems = computed(() => {
  const items = getFormItems(t);

  // 动态设置机台选项
  const machineOptions = machineList.value.map(m => ({
    label: m.id,
    value: m.id
  }));

  const machineCodeItem = items.find(item => item.prop === "machCode");
  if (machineCodeItem) {
    machineCodeItem.options = machineOptions;
  }

  return items;
});

// 表单数据
const formData = reactive(getInitialFormData());
// 添加 onMounted 钩子，页面加载时自动触发搜索
// onMounted(async () => {
//   await fetchMachines(); // 挂载时获取机台
// });
// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量

const title = computed(() => t("deviceProtect.dailyProtect.title"));
const dateData = ref([]);
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
  const query = {
    Type: 443,
    IsPage: false
  };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);
  dateData.value = convertCalendarData(data.list);
});
const { t } = useI18n();

// ProTable 实例引用
const proTableRef = ref(null);
const initParam = ref();
const exportParam = ref({});
// 对话框相关
const dialogVisible = ref(false);
const importDialogVisible = ref(false);
const dialogType = ref("add"); // 对话框类型：add, edit, view

// 上传组件引用
const uploadRef = ref(null);
// 选中的行数据
const selectedRows = ref([]);

// // 维护/点检状态选项
// const mcStatusOptions = [
//   { value: "已完成", label: t("deviceProtect.dailyProtect.status.completed") },
//   { value: "未完成", label: t("deviceProtect.dailyProtect.status.uncompleted") },
//   { value: "进行中", label: t("deviceProtect.dailyProtect.status.inProgress") }
// ];
const isChart = ref(false);
const changeTable = () => {
  isChart.value = !isChart.value;
};
// 修改 handleSelectChange 方法，仅更新机台名称，不影响其他字段
const handleSelectChange = ({ prop, value }) => {
  if (prop === "machCode") {
    const selectedMachine = machineList.value.find(machine => machine.id === value);
    formData.machCode = value;
    formData.mach = selectedMachine?.name || "";
  }
};
// 1. 获取机台列表（Type=0）
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineModel || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // 添加：设置默认选中第一个机台
    // if (machineList.value.length > 0) {
    //   param.value.machine = machineList.value[0].id; // 设置默认机台 id
    // }

    console.log("机台列表已更新:", machineList.value);
  } catch (error) {
    // console.error("机台列表请求失败:", error);
    // ElMessage.error("获取机台列表失败");
  }
};
// 模拟数据请求
const fetchData = async params => {
  return deviceProtectdailyProtectApi.ProdMaintenanceCheckGet(params);
};

// 过滤后的数据请求
// const currentFilter = ref(null);
const fetchFilteredData = async params => {
  exportParam.value = params;
  const allData = await fetchData(params);
  exportParam.value.pageSize = allData.data.total;
  return allData;
};

// 树状结构数据
const treeData = ref([]);
// const treeProps = {
//   label: "name",
//   children: "children"
// };

// 构建树状结构数据
const buildTreeData = async () => {
  const allData = await fetchData();
  const factories = {};

  allData.data.list.forEach(item => {
    // 处理工厂
    if (!factories[item.fact]) {
      factories[item.fact] = {
        name: item.fact,
        type: "fact",
        children: []
      };
    }
    const factory = factories[item.fact];

    // 处理车间
    let workshop = factory.children.find(child => child.name === item.wshop);
    if (!workshop) {
      workshop = {
        name: item.wshop,
        type: "wshop",
        children: []
      };
      factory.children.push(workshop);
    }

    // 处理生产线
    let productionLine = workshop.children.find(child => child.name === item.prodLine);
    if (!productionLine) {
      productionLine = {
        name: item.prodLine,
        type: "prodLine",
        children: []
      };
      workshop.children.push(productionLine);
    }

    // 处理机台
    let machine = productionLine.children.find(child => child.name === item.mach);
    if (!machine) {
      machine = {
        name: item.mach,
        type: "mach",
        children: []
      };
      productionLine.children.push(machine);
    }
    // 处理工位
    let workstation = machine.children.find(child => child.name === item.wstation);
    if (!workstation) {
      workstation = {
        name: item.wstation,
        type: "wstation",
        children: []
      };
      machine.children.push(workstation);
    }
  });
  treeData.value = Object.values(factories);
  return {
    data: Object.values(factories)
  };
  treeData.value = Object.values(factories);
};

const resetFormData = (data = {}) => {
  // 获取初始表单数据
  const initial = getInitialFormData();
  console.log("resetFormData 调用，重置前的表单数据：", { ...formData });
  // 合并初始数据和传入数据
  Object.assign(formData, initial, data);
  console.log("resetFormData 调用，重置后的表单数据：", { ...formData });

  // 特殊处理日期字段（字符串转Date对象）
  if (formData.feedingTime && typeof formData.feedingTime === "string") {
    formData.feedingTime = new Date(formData.feedingTime);
  }

  if (formData.operationTime && typeof formData.operationTime === "string") {
    formData.operationTime = new Date(formData.operationTime);
  }
};
const openAddDialog = () => {
  dialogType.value = "add";
  Object.keys(formData).forEach(key => {
    formData[key] = key === "totalQuantity" || key === "usedQuantity" || key === "remainingQuantity" ? 0 : "";
    if (key === "isReplaced") formData[key] = false;
    if (key.includes("Time")) formData[key] = null;
  });
  currentRowData.value = { ...formData };
  dialogVisible.value = true;
};
const openEditDialog = row => {
  dialogType.value = "edit";
  resetFormData(row);
  currentRowData.value = { ...formData };
  dialogVisible.value = true;
};
const openViewDialog = row => {
  dialogType.value = "view";
  resetFormData(row);
  currentRowData.value = { ...formData };
  dialogVisible.value = true;
};
// 保存数据处理
const handleSave = async data => {
  try {
    const params = {
      ...data,
      id: dialogType.value === "edit" ? data.id : undefined
    };

    let response;
    if (dialogType.value === "add") {
      response = await deviceProtectdailyProtectApi.ProdMaintenanceCheckAdd(params);
    } else if (dialogType.value === "edit") {
      response = await deviceProtectdailyProtectApi.ProdMaintenanceCheckAdd(params);
    }

    if (response && response.code === 200) {
      ElMessage.success(dialogType.value === "add" ? t("rawMaterialWarning.message.addSuccess") : t("rawMaterialWarning.message.editSuccess"));

      // 刷新表格和树结构
      proTableRef.value.getTableList();
      // buildTreeData();
    } else {
      ElMessage.error(response.msg || t("rawMaterialWarning.message.operationFailed"));
    }
  } catch (error) {
    ElMessage.error(t("rawMaterialWarning.message.networkError"));
  }
};

// 删除行
const deleteRow = async row => {
  try {
    const response = await deviceProtectdailyProtectApi.ProdMaintenanceCheckDelete({ id: row.id });
    if (response.code === 200) {
      ElMessage.success(`删除 ${row.fact} 相关记录成功`);
      proTableRef.value.getTableList();
      buildTreeData();
    } else {
      ElMessage.error(response.msg || "删除失败");
    }
  } catch (error) {
    ElMessage.error("网络请求失败，请重试");
  }
};

// 批量删除选中行
const deleteSelectedRows = async () => {
  try {
    const ids = selectedRows.value.map(row => row.id);
    if (ids.length === 0) {
      ElMessage.warning("请选择要删除的记录");
      return;
    }

    const response = await deviceProtectdailyProtectApi.ProdMaintenanceCheckDelete({ ids });
    if (response.code === 200) {
      ElMessage.success(`批量删除 ${ids.length} 条记录成功`);
      selectedRows.value = [];
      proTableRef.value.getTableList();
      buildTreeData();
    } else {
      ElMessage.error(response.msg || "批量删除失败");
    }
  } catch (error) {
    ElMessage.error("网络请求失败，请重试");
  }
};

// 处理行点击事件
const handleRowClick = row => {
  console.log("点击行数据：", row);
};

// 处理选中行变化事件
const handleSelectionChange = rows => {
  selectedRows.value = rows;
};

// 打开导入对话框
const importData = () => {
  importDialogVisible.value = true;
};

// 导出数据
const exportData = async () => {
  try {
    // 获取当前表格的所有数据（包含分页参数）
    const params = {
      ...proTableRef.value.searchParam, // 获取当前查询参数
      page: 1,
      pageSize: exportParam.value.pageSize // 获取足够大的分页尺寸
    };

    // 调用API获取完整数据
    const response = await fetchFilteredData(params);
    if (response.code !== 200) throw new Error(response.msg);
    const tableData = response.data.list;

    // 过滤需要导出的列（排除操作列和选择列）
    const exportColumns = columns.value.filter(col => !["selection", "operation", "id"].includes(col.type || col.prop));

    // 准备表头数据
    const headers = exportColumns.map(col => col.label);

    // 准备表格数据（包括数据转换）
    const dataRows = tableData.map(item => {
      return exportColumns.map(col => {
        // 特殊处理时间字段
        if (col.prop === "mcTime" && item[col.prop]) {
          return new Date(item[col.prop]).toLocaleString();
        }
        // 处理状态字段的枚举值
        // if (col.prop === "mcStatus") {
        //   return mcStatusOptions.find(opt => opt.value === item[col.prop])?.label || item[col.prop];
        // }
        // // 处理状态字段的枚举值
        // if (col.prop === "machCode") {
        //   return getMachineOptions.find(opt => opt.value === item[col.prop])?.value || item[col.prop];
        // }
        return item[col.prop] || "";
      });
    });

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...dataRows]);

    // 创建工作簿并导出
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "设备数据");

    // 生成文件名（带日期）
    const exportDate = new Date().toISOString().slice(0, 10);
    XLSX.writeFile(workbook, `设备维护记录_${exportDate}.xlsx`);

    ElMessage.success("导出成功");
  } catch (error) {
    ElMessage.error(`导出失败: ${error.message}`);
  }
};

// 导入文件前置处理
const beforeUpload = () => {
  // 这里可以添加文件类型、大小等验证逻辑
  return true;
};

// 开始导入
const startImport = () => {
  uploadRef.value!.submit();
  // 这里应该替换为实际的导入 API 请求
  importDialogVisible.value = false;
  ElMessage.success("导入成功");
  // 刷新表格数据
  proTableRef.value.getTableList();
  // 重新构建树状结构数据
  buildTreeData();
};

// 处理树节点点击事件
// const handleNodeClick = node => {
//   currentFilter.value = node;
//   initParam.value[node.type] = node.name;
//   console.log(node, "123");

//   proTableRef.value.getTableList();
// };
const handleNodeClick = (nodeObj: any) => {
  const initParam1: any = {};

  let currentNode = nodeObj;
  while (currentNode) {
    const nodeData = currentNode.data;
    if (nodeData && nodeData.type && nodeData.name) {
      // 以节点的type作为键，name作为值
      initParam1[nodeData.type] = nodeData.name;
    }
    currentNode = currentNode.parent;
  }

  const obj = clearObjectValues(initParam.value);
  initParam.value = { ...obj, ...initParam1 };
};

onMounted(() => {
  buildTreeData();
  fetch("/api/user/shit")
    .then(response => {
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      return response.json();
    })
    .then(data => {
      console.log("Data received:", data);
    })
    .catch(error => {
      console.error("There was a problem with the fetch operation:", error);
    });
});
</script>

<style scoped>
:deep(.filter) {
  width: auto !important;
}

/* 自定义树的样式 */
.custom-tree {
  width: 20%;
  padding: 10px;
  margin-right: 20px;
  overflow-y: auto;
  background-color: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}

/* 树节点样式 */
.custom-tree .el-tree-node__content {
  height: 32px;
  padding: 0 8px;
  font-size: 14px;
  line-height: 32px;
  color: #333333;
  border-radius: 4px;
  transition: background-color 0.3s;
}

/* 鼠标悬停节点样式 */
.custom-tree .el-tree-node__content:hover {
  background-color: #eef1f6;
}

/* 选中节点样式 */
.custom-tree .el-tree-node.is-current > .el-tree-node__content {
  color: #ffffff;
  background-color: #409eff;
}

/* 展开/收缩图标样式 */
.custom-tree .el-tree-node__expand-icon {
  font-size: 12px;
  color: #909399;
}

/* 展开图标旋转动画 */
.custom-tree .el-tree-node__expand-icon.expanded {
  transition: transform 0.3s;
  transform: rotate(90deg);
}
.button-group {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  align-items: center;
}
.operation-buttons {
  display: flex;
  flex-wrap: nowrap;
  gap: 4px;
  align-items: center;
}
.dialog-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
.form-dialog {
  :deep(.el-form-item__label) {
    padding-right: 12px;
    white-space: nowrap;
  }
  :deep(.el-form-item__content) {
    flex: 1;
  }
}
:deep(.el-button) {
  white-space: nowrap;
}
:deep(.el-table .cell) {
  white-space: nowrap;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-date-editor) {
  width: 100%;
}
:deep(.el-input-number) {
  width: 100%;
}
</style>
