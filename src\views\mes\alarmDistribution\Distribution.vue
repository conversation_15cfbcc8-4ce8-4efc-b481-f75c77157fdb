<template>
  <div class="container" id="648104980742213">
    <div class="bg-blue-600 text-white p-2 rounded-lg flex items-center justify-center space-x-4 absolute top-[17px] left-[10px]">
      <div>MTTR: <span class="text-xl font-semibold">21000h</span></div>
      <div>MTBF: <span class="text-xl font-semibold text-red-500">24000h</span></div>
    </div>
    <img ref="imageRef" :src="imageUrl" alt="Centered Image" class="centered-image" />
    <!-- 动态注解 -->
    <div
      v-for="(annotation, index) in annotations"
      :key="index"
      class="annotation"
      :style="{
        top: annotationTop(index) + 'px',
        left: annotationLeft(index) + 'px'
      }"
    >
      {{ annotation.text }}
    </div>
    <!-- 动态状态卡片 -->
    <router-link to="/mes/device-protect/same-service-motordetails">
      <DeviceStatus
        v-for="(item, index) in statusData"
        :key="index"
        :wait-time="item.waitTime"
        :top="statusTop(index)"
        :left="statusLeft(index)"
        :scaled="true"
        :active-status="item.activeStatus"
      />
    </router-link>
  </div>
</template>

<script setup lang="ts" name="设备告警分布盘">
import { ref, computed, onMounted, onUnmounted } from "vue";
import alarm from "@/assets/img/alarm.jpg";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const imageRef = ref(null);
const containerWidth = ref(0);
const containerHeight = ref(0);

// 记录上一次窗口的宽度和高度
let prevWidth = 0;
let prevHeight = 0;
// 定义尺寸变化阈值
const sizeThreshold = 100;

const annotationPositions = [
  { top: 0.295, left: 0.155 },
  { top: 0.662, left: 0.265 },
  { top: 0.27, left: 0.53 },
  { top: 0.63, left: 0.53 },
  { top: 0.602, left: 0.735 },
  { top: 0.345, left: 0.78 },
  { top: 0.595, left: 0.825 }
];

const deviceNames = [
  "alarmDistribution.deviceNames.bUnwinding",
  "alarmDistribution.deviceNames.aUnwinding",
  "alarmDistribution.deviceNames.bDieCutting",
  "alarmDistribution.deviceNames.aDieCutting",
  "alarmDistribution.deviceNames.cStacking",
  "alarmDistribution.deviceNames.dStacking",
  "alarmDistribution.deviceNames.gDischarging"
];

const annotations = computed(() =>
  annotationPositions.map((pos, index) => ({
    text: t(deviceNames[index]),
    top: pos.top,
    left: pos.left
  }))
);

const statusData = ref([
  { waitTime: 10, top: 0.09, left: 0.13, activeStatus: "running" },
  { waitTime: 20, top: 0.061, left: 0.5, activeStatus: "stopped" },
  { waitTime: 30, top: 0.1, left: 0.75, activeStatus: "maintenance" },
  { waitTime: 15, top: 0.715, left: 0.235, activeStatus: "waiting" },
  { waitTime: 25, top: 0.685, left: 0.51, activeStatus: "fault" },
  { waitTime: 30, top: 0.655, left: 0.7, activeStatus: "running" },
  { waitTime: 35, top: 0.65, left: 0.81, activeStatus: "waiting" }
]);

const imageUrl = ref(alarm);

// 更新容器尺寸
function updateContainerSize() {
  const container = document.querySelector(".container");
  if (container) {
    containerWidth.value = container.clientWidth;
    containerHeight.value = container.clientHeight;
  }
}

// 注解位置计算
function annotationTop(index) {
  return annotations.value[index].top * containerHeight.value;
}

function annotationLeft(index) {
  return annotations.value[index].left * containerWidth.value;
}

// 状态卡片位置计算
function statusTop(index) {
  return statusData.value[index].top * containerHeight.value;
}

function statusLeft(index) {
  return statusData.value[index].left * containerWidth.value;
}

// 处理页面最大化和向下还原
function handleResize() {
  const currentWidth = window.innerWidth;
  const currentHeight = window.innerHeight;
  if (
    (Math.abs(currentWidth - prevWidth) > sizeThreshold && currentWidth === window.screen.width) ||
    (Math.abs(currentHeight - prevHeight) > sizeThreshold && currentHeight === window.screen.height)
  ) {
    location.reload();
  }
  prevWidth = currentWidth;
  prevHeight = currentHeight;
  updateContainerSize();
}

// 初始化和监听
onMounted(() => {
  prevWidth = window.innerWidth;
  prevHeight = window.innerHeight;
  updateContainerSize();
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
.container {
  position: relative;
  display: flex;
  align-items: center; /* 垂直居中 */
  justify-content: center; /* 水平居中 */
  width: 100%; /* 容器宽度 */
  max-width: 100%;
  height: 100%; /* 容器高度 */
  overflow: hidden; /* 防止图片超出容器 */
  background-color: white;

  /* 白色背景 */
}
.centered-image {
  display: block;
  width: 75%;
  max-height: 100%;
  margin: 0 auto;
  object-fit: contain;
}
.annotation {
  position: absolute;
  padding: 10px;
  font-size: 14px;
  color: white;
  white-space: nowrap;
  background-color: rgb(0 0 0 / 50%);
  border-radius: 5px;
}
</style>
