<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'hideMachineSwitcher']"
    @data-loaded="handleDataLoaded"
    default-compare-id="production"
    :machines="machines"
    :init-params="initParams"
    ref="userTable"
  >
    <template #toolbar-right>
      <el-tooltip :content="$t('common.switchToTable')" placement="top">
        <el-button :icon="Switch" circle @click="changeTable" />
      </el-tooltip>
    </template>
  </ProChart>
</template>

<script setup lang="tsx">
// import moment from "moment";
import { productionReportCapacityApi } from "@/api";
import { Switch } from "@element-plus/icons-vue";
import moment from "moment";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const userTable = ref<any>(null);

const props = defineProps({
  currentTreeType: {
    type: String,
    default: "1" // 默认机台模式
  },
  initParams: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const machines = ref<any[]>([]);
const emit = defineEmits(["toggleView"]);
const changeTable = () => emit("toggleView");

// 颜色配置（调整为处理状态相关）
const COLORS = {
  ProcessedCount: "#409EFF", // 已处理颜色
  OverdueUnprocessedCount: "#FF6B6B", // 未处理颜色
  ExpiringSoon: "#67C23A", // 即将到期颜色
  font: "#666",
  splitLine: "#eee"
};

// 图表配置（核心调整部分）
const chartOptions = ref({
  title: {
    subtext: t("common.compareList.totalUnprocessedQuantity") + ": 0", //"总未处理数量：0",
    left: "center",
    textStyle: { fontSize: 16, color: COLORS.font }
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["bar", "line"],
        title: {
          line: t("common.compareList.switchToLine"), // 切换为折线图
          bar: t("common.compareList.switchToBar") // 切换为柱状图
        }
      }
    }
  },
  tooltip: {
    trigger: "axis",
    formatter: (params: any[]) => {
      return params
        .map(
          p => `
          <div style="display: flex; align-items: center; gap: 8px;">
            <span style="width: 10px; height: 10px; background: ${p.color}; border-radius: 2px; display: inline-block;"></span>
            ${p.seriesName}: ${p.data}
          </div>
        `
        )
        .join("<br>");
    }
  },
  legend: {
    data: [
      t("common.compareList.ProcessedCount"), //"已处理",
      t("common.compareList.unprocessed"), // "未处理",
      t("common.compareList.AlmostDueCount") // "即将到期"
    ],
    top: 30,
    textStyle: { color: COLORS.font }
  },
  xAxis: {
    type: "category",
    data: [],
    name: props.currentTreeType === "0" ? t("common.compareList.line") : t("common.compareList.Machine"),
    axisLabel: { color: COLORS.font, rotate: 45, interval: 0 }
  },
  yAxis: [
    {
      type: "value",
      name: t("common.compareList.count"), //"数量",
      axisLabel: { color: COLORS.OverdueUnprocessedCount },
      splitLine: { lineStyle: { color: COLORS.splitLine } }
    }
  ],
  grid: { left: "3%", right: "3%", bottom: "3%", containLabel: true },
  series: []
});

// 数据转换函数（新增处理状态分组）
function transformData(dataList: any[]) {
  let groupField = "workline";
  if (props.currentTreeType == "1") {
    groupField = "workshop";
  } else if (props.currentTreeType == "2") {
    groupField = "workline";
  } else if (props.currentTreeType == "3") {
    groupField = "deck";
  } else {
    groupField = "deck";
  }

  const groupMap = new Map<string, { ProcessedCount: number; OverdueUnprocessedCount: number; ExpiringSoon: number }>();

  dataList.forEach(item => {
    const key = item[groupField];
    if (!groupMap.has(key)) {
      groupMap.set(key, { ProcessedCount: 0, OverdueUnprocessedCount: 0, ExpiringSoon: 0 });
    }
    const group = groupMap.get(key)!;
    group.ProcessedCount += item.ProcessedCount;
    group.OverdueUnprocessedCount += item.OverdueUnprocessedCount;
    group.ExpiringSoon += item.ExpiringSoon;
  });
  const categories = Array.from(groupMap.keys()).sort();
  const processedData = categories.map(key => groupMap.get(key)!.ProcessedCount);
  const unprocessedData = categories.map(key => groupMap.get(key)!.OverdueUnprocessedCount);
  const upcomingData = categories.map(key => groupMap.get(key)!.ExpiringSoon);

  return {
    categories,
    processedData,
    unprocessedData,
    upcomingData,
    machines: categories.map(name => ({ id: name, name }))
  };
}

// 数据获取函数（调整模拟数据和状态映射）
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const fetchData = async (params: any) => {
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss"),
    EndDate: moment(params.time[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
  };
  const query = { ...params, ...time, Type: 445 };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);

  const { categories, processedData, unprocessedData, upcomingData, machines: machineList } = transformData(data.list);
  machines.value = machineList;

  return {
    data: {
      isCompare: false,
      categories,
      seriesData: [processedData, unprocessedData, upcomingData]
    }
  };
};

// 数据加载回调（调整系列配置）
const handleDataLoaded = (data: any) => {
  const [processedData, unprocessedData, upcomingData] = data.seriesData;

  chartOptions.value = {
    ...chartOptions.value,
    title: {
      ...chartOptions.value.title,
      subtext: `总未处理数量：${unprocessedData.reduce((a, b) => a + b, 0)}`
    },
    xAxis: { ...chartOptions.value.xAxis, data: data.categories },
    series: [
      {
        name: t("common.compareList.ProcessedCount"), //"已处理",
        type: "bar",
        data: processedData,
        itemStyle: { color: COLORS.ProcessedCount },
        label: { show: true, position: "inside", formatter: "{c}" }
      },
      {
        name: t("common.compareList.unprocessed"), //"未处理",
        type: "bar",
        data: unprocessedData,
        itemStyle: { color: COLORS.OverdueUnprocessedCount },
        label: { show: true, position: "inside", formatter: "{c}" }
      },
      {
        name: t("common.compareList.AlmostDueCount"), //"即将到期",
        type: "bar",
        data: upcomingData,
        itemStyle: { color: COLORS.ExpiringSoon },
        label: { show: true, position: "inside", formatter: "{c}" }
      }
    ]
  };
};
</script>
