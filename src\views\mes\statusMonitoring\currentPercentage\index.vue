<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'compare']"
    :search-fields="searchFields"
    @data-loaded="handleDataLoaded"
    :compare-list="compareList"
    :machines="machines"
    :init-params="initParams"
    id="651508965347397"
  >
  </ProChart>
</template>

<script setup lang="tsx">
import { computed, ref, onMounted } from "vue";
import moment from "moment";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const initParams = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
  type: 13 // 根据接口要求设置报表类型
});
// 存储机台
const machines = ref<any[]>([]);
// 存储类型配置
const ngTypes = ref<string[]>([]);
// 存储颜色配置
const colorPalette = ref<string[]>([]);
// 动态生成颜色配置
const generateColors = (count: number) => {
  const palette = [
    "#00bfff",
    "#ff4d4f",
    "#fac858",
    "#87d068",
    "#9a60b4",
    "#ff8c00",
    "#1e90ff",
    "#32cd32",
    "#ff1493",
    "#7b68ee",
    "#00fa9a",
    "#ff6347"
  ];
  return palette.slice(0, count);
};

// 创建堆叠图series配置
const createStackedSeries = (seriesData: any[], ngTypes: string[], colorPalette: string[]) => {
  return ngTypes.map((type, idx) => ({
    name: type,
    type: "bar",
    stack: "total", // 设置堆叠标识
    data: seriesData[idx] || [],
    itemStyle: { color: colorPalette[idx] },
    label: {
      show: true,
      position: "inside", // 堆叠图label显示在内部
      formatter: (params: any) => {
        // 只显示非零值
        return params.value > 0 ? params.value : "";
      },
      fontSize: 11,
      color: "#fff",
      fontWeight: "bold"
    }
  }));
};
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");
// 搜索字段配置
const searchFields = ref([
  {
    prop: "time",
    label: t("common.compareList.time"),
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": "Start date",
        "end-placeholder": "End date"
      }
    }
  }
]);

// 图表基础配置
const baseChartOptions = {
  title: {
    subtext: "", // 移除平均不良率
    left: "center",
    textStyle: { fontSize: 16, color: chartColor }
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: t("productionReport.capacity.switchToLine"), // 切换为折线图
          bar: t("productionReport.capacity.switchToBar") // 切换为柱状图
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      let tip = `<div style="padding:8px;min-width:150px">`;
      tip += `<div style="margin-bottom:5px;font-weight:bold;">${params[0].axisValue}</div>`;

      // 计算总计（仅对堆叠图显示）
      const barParams = params.filter((p: any) => p.seriesType === "bar");
      let total = 0;
      if (barParams.length > 1) {
        total = barParams.reduce((sum: number, p: any) => sum + (p.value || 0), 0);
        tip += `<div style="margin-bottom:5px;color:#666;">总计: ${total}</div>`;
      }

      params.forEach((p: any) => {
        if (p.seriesType === "bar") {
          const percentage = barParams.length > 1 && total > 0 ? `(${((p.value / total) * 100).toFixed(1)}%)` : "";
          tip += `<div style="margin:2px 0;">
            <span style="display:inline-block;width:10px;height:10px;background:${p.color};border-radius:50%;margin-right:5px;"></span>
            ${p.seriesName}: ${p.value} ${percentage}
          </div>`;
        } else if (p.seriesType === "line") {
          tip += `<div style="margin:2px 0;border-top:1px solid #eee;padding-top:3px;">
            <span style="display:inline-block;width:10px;height:2px;background:${p.color};margin-right:5px;"></span>
            ${p.seriesName}: ${p.value}
          </div>`;
        }
      });
      return tip + "</div>";
    }
  },
  legend: { top: 30, textStyle: { color: chartColor } },
  xAxis: {
    type: "category",
    axisLabel: { color: chartColor, interval: 0, rotate: 45 }
  },
  yAxis: [
    {
      type: "value",
      name: t("common.compareList.numberOfStates"), // 修改为状态数量
      axisLabel: { color: chartColor, formatter: "{value}" }, // 去掉百分比符号
      splitLine: { lineStyle: { color: "#eee" } }
    }
  ],
  grid: { left: "3%", right: "3%", bottom: "12%", containLabel: true },
  series: [] as any[]
};

const chartOptions = ref({ ...baseChartOptions });

// 动态生成对比列表，将 ngTypes 数组中的每个元素映射为一个包含 label 和 value 的对象
const compareList = computed(() => ngTypes.value.map(type => ({ label: type, value: type })));
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineName || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // // 添加：设置默认选中第一个机台
    // if (machineList.value.length > 0) {
    //   searchParam.value.machine = machineList.value[0].id; // 设置默认机台 id
    // }

    console.log("机台列表已更新:", machineList.value);
  } catch (error) {
    console.error("机台列表请求失败:", error);
    ElMessage.error("获取机台列表失败");
  }
};
// 从后端获取数据并进行处理
const fetchData = async (params: any) => {
  try {
    const response = await alramAnalysisApi.getListMesReportData({
      StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss"),
      EndDate: moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString(),
      type: 13 // 根据接口要求传递类型参数
    });
    const data = response.data || { list: [] };
    if (!data || !data.list || data.list.length === 0) {
      return {
        data: {
          categories: [],
          seriesData: [],
          isCompare: false
        }
      };
    }
    // if (!data?.list?.length) return { data: { categories: [], seriesData: [], isCompare: false } };
    // 确定数据的时间, 获取到的数据列表 data.list 的第一个元素中提取 type 属性，如果不存在则默认设置为 "Hour"
    const timeType = data.list[0]?.type || "Hour";
    // 调用transformData函数，将获取到data.list和确定的timeType传递进去，对数据进行转换处理，并将转换后的数据存储在transformed中
    const transformed = transformData(data.list, timeType);
    machines.value = transformed.machines;
    // 普通模式
    if (!params.compareMode) {
      // 从params中获取machine，如果不存在，默认machines.value数组中的第一个元素的name属性作为目标机台
      const targetMachine = params.machine || machines.value[0]?.name;
      // 通过目标机台名称从 transformed.machineData 中获取对应的机台数据 machineData
      const machineData = transformed.machineData.get(targetMachine);

      if (!machineData) {
        return {
          data: {
            categories: transformed.categories,
            seriesData: [],
            isCompare: false
          }
        };
      }
      const seriesData = ngTypes.value.map(type => (machineData.has(type) ? machineData.get(type)!.dataPoints.map(d => d.value) : []));

      // 使用map方法遍历ngTypes.value 数组
      // const seriesData = ngTypes.value.map(
      //   type => transformed.compareData.find(d => d.type === type)?.data.find(m => m.machine === targetMachine)?.data || []
      // );

      // 提取目标值
      const targetValues = transformed.categories.map(() => data.list?.[0]?.target_value || 0);

      return {
        data: {
          // 转换后的数据类别
          categories: transformed.categories,
          // 目标机台各类型的数据
          seriesData,
          // 目标值
          targetValues,
          // 状态类型
          ngTypes: ngTypes.value,
          // 是否为对比模式
          isCompare: false
        }
      };
    }

    // 对比模式
    return {
      data: {
        categories: transformed.categories,
        // 转换后的对比数据
        compare: transformed.compareData,
        isCompare: true,
        ngTypes: ngTypes.value
      }
    };
  } catch (error) {
    console.error("数据获取失败:", error);
    return { data: { categories: [], seriesData: [] }, isCompare: false };
  }
};
// 转换从后端获取的数据
function transformData(responseData: any[], timeType: string) {
  const machineMap = new Map<
    string,
    Map<
      string,
      {
        // 数据点、总数和计数的对象
        dataPoints: { time: string; value: number }[];
        total: number;
        count: number;
        average?: string; // 添加平均值属性
      }
    >
  >();
  // 存储时间
  const timeSet = new Set<string>();
  // 状态类型
  const ngTypeSet = new Set<string>();
  // 机台
  const machineSet = new Set<string>();
  // 确定时间格式
  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Mon":
      formatPattern = "MM-DD";
      break;
    case "Year":
      formatPattern = "YYYY-MM";
      break;
    default:
      formatPattern = "HH:00";
  }
  // const formatPattern =
  //   {
  //     Hour: "HH:00",
  //     Mon: "MM-DD",
  //     Year: "YYYY-MM"
  //   }[timeType] || "HH:00";
  // 遍历后端返回的响应数据
  responseData.forEach(item => {
    // 机台
    const machine = item.machine;
    // 状态类型
    const ngType = item.machinestatuss;
    // 时间
    const time = moment(item.start_time).format(formatPattern);
    // 状态数量
    const value = item.prod_count || 0;
    // console.log(value, "1111111111111");
    // 使用 machineMap 存储数据，将相同机台和状态类型的数据点、总数和计数进行累加
    if (!machineMap.has(machine)) {
      machineMap.set(machine, new Map());
    }

    const machineData = machineMap.get(machine)!;
    if (!machineData.has(ngType)) {
      machineData.set(ngType, { dataPoints: [], total: 0, count: 0 });
    }
    const typeData = machineData.get(ngType)!;
    typeData.dataPoints.push({ time, value });
    typeData.total += value;
    typeData.count++;
    timeSet.add(time);
    machineSet.add(machine);
    ngTypeSet.add(ngType);
  });

  //更新 ngTypes、colorPalette 和 machines 的值
  ngTypes.value = Array.from(ngTypeSet);
  colorPalette.value = generateColors(ngTypes.value.length);
  // machines.value = Array.from(machineSet).map(id => ({ id, name: id }));
  // 将时间集合转换为数组并排序，作为图表的类别
  const categories = Array.from(timeSet).sort();

  // // 生成对比数据
  // const compareData = ngTypes.value.map(type => ({
  //   type,
  //   data: Array.from(machineMap.entries()).map(([machine, types]) => ({
  //     machine,
  //     data: types.get(type)?.dataPoints.map(d => d.value) || []
  //     // average: types.get(type) ? (types.get(type)!.total / types.get(type)!.count).toFixed(1) : 0
  //   }))
  // }));
  // 计算各NG类型的平均值
  machineMap.forEach(machineData => {
    machineData.forEach(ngTypeData => {
      ngTypeData.average = (ngTypeData.total / ngTypeData.count).toFixed(1);
    });
  });
  // 构建对比数据
  const compareData: any = {};
  const compare: any[] = [];

  ngTypes.value.forEach(type => {
    compareData[type] = Array.from(machineMap.entries()).map(([machine, ngData]) => ({
      machine,
      data: ngData.has(type) ? ngData.get(type)!.dataPoints.map(d => d.value) : [],
      total: ngData.has(type) ? parseFloat(ngData.get(type)!.average || "0") : 0
    }));
  });
  compare.push(compareData);

  // 返回转换后的数据，包含类别和对比数据
  return {
    categories,
    // compareData,
    compareData: compare,
    machines: Array.from(machineSet).map(id => ({ id, name: id })),
    machineData: machineMap
  };
}
// 处理图表数据加载完成后的数据
const handleDataLoaded = (data: any) => {
  // 判断是否为普通模式
  if (!data.isCompare) {
    // 直接创建堆叠图series
    const barSeries = createStackedSeries(data.seriesData, ngTypes.value, colorPalette.value);

    chartOptions.value = {
      // 基础配置
      ...baseChartOptions,
      xAxis: {
        ...baseChartOptions.xAxis,
        data: data.categories
      } as any,
      legend: {
        ...baseChartOptions.legend,
        data: [...ngTypes.value, "目标值"]
      } as any,
      series: [
        ...barSeries,
        {
          name: "目标值",
          type: "line",
          data: data.targetValues,
          itemStyle: { color: "#FFA500" },
          lineStyle: { type: "dashed" }
        }
      ]
    };
  } else {
    // 对比模式处理（根据需求扩展）
    console.log("对比模式数据:", data);
  }
};

// 添加 onMounted 钩子，页面加载时自动触发搜索
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
});
</script>
