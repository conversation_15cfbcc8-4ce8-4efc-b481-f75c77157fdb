<template>
  <div class="main-box" id="651513961111621">
    <!-- {{ treeData }} -->
    <TreeFilter
      :default-expand-all="false"
      ref="treeFilter"
      label="name"
      :title="t('deviceProtect.dailyProtect.organizationList')"
      :request-api="deviceProtectdailyProtectApi.getTree"
      @change1="handleNodeClick"
    >
      <template #shit>
        <CalendarCustom current-theme="light" :header-title="title" :date-data="dateData"> </CalendarCustom>
      </template>
    </TreeFilter>
    <!-- 左侧树状结构 -->
    <!-- <el-tree :data="treeData" :props="treeProps" @node-click="handleNodeClick" class="custom-tree"></el-tree> -->
    <div class="table-box">
      <!-- 右侧表格 -->
      <ProTable
        v-if="!isChart"
        ref="proTableRef"
        :init-param="initParam"
        :columns="columns"
        :request-api="fetchFilteredData"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      >
        <!-- 操作按钮 -->
        <template #tableHeader>
          <el-button type="primary" @click="exportData">{{ t("deviceProtect.dailyProtect.export") }}</el-button>
        </template>
        <template #toolButton>
          <el-tooltip content="切换表格" placement="top">
            <el-button :icon="Switch" circle @click="changeTable" />
          </el-tooltip>
        </template>
        <!-- 操作列 -->
        <template #operation="{ row }">
          <el-button type="info" size="small" @click="openViewDialog(row)"> {{ t("deviceProtect.dailyProtect.view") }} </el-button>
        </template>
      </ProTable>
      <SwitchChart :current-tree-type="currentTreeType" :init-params="switchchartData" v-else @toggle-view="changeTable"></SwitchChart>

      <!-- 新增/编辑/查看对话框 -->
      <el-dialog
        :width="1200"
        v-model="dialogVisible"
        :title="
          dialogType === 'add'
            ? t('deviceProtect.dailyProtect.add')
            : dialogType === 'edit'
              ? t('deviceProtect.dailyProtect.edit')
              : t('deviceProtect.dailyProtect.view')
        "
      >
        <el-form :model="formData" ref="formRef" label-width="180px" :rules="rules" class="form-dialog">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.factory')" prop="factory">
                <el-input v-model="formData.factory" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.workshop')" prop="workshop">
                <el-input v-model="formData.workshop" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.workline')" prop="workline">
                <el-input v-model="formData.workline" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.deck')">
                <el-select v-model="formData.deck" :disabled="false" @change="handleMachChange">
                  <el-option v-for="option in getMachineOptions" :key="option.value" :label="option.label" :value="option.label" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.machineCode')">
                <el-select
                  v-model="formData.machCode"
                  :placeholder="t('deviceProtect.dailyProtect.form.machineCode')"
                  :disabled="dialogType === 'view'"
                  @change="handleMachCodeChange"
                >
                  <el-option v-for="option in getMachineOptions" :key="option.value" :label="option.value" :value="option.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.station')">
                <el-input v-model="formData.station" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.workstationCode')">
                <el-input v-model="formData.wstationCode" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.maintenanceOrg')">
                <el-input v-model="formData.mcOrgan" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.maintenanceItem')">
                <el-input v-model="formData.mcItem" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.maintenanceStandard')">
                <el-input v-model="formData.mcStandard" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.maintenanceMethod')">
                <el-input v-model="formData.mcMethod" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.maintenanceFrequency')">
                <el-input v-model="formData.mcFrequency" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.maintenanceStatus')">
                <el-select
                  v-model="formData.mcStatus"
                  :placeholder="t('deviceProtect.dailyProtect.form.maintenanceStatus')"
                  :disabled="dialogType === 'view'"
                >
                  <el-option v-for="option in mcStatusOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.maintenancePerson')">
                <el-input v-model="formData.mcPerson" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.maintenanceTime')">
                <el-date-picker
                  v-model="formData.mcTime"
                  type="datetime"
                  :placeholder="t('deviceProtect.dailyProtect.form.maintenanceTime')"
                  :disabled="dialogType === 'view'"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item :label="t('deviceProtect.dailyProtect.form.remarks')">
                <el-input v-model="formData.remarks" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialogVisible = false">{{ t("deviceProtect.dailyProtect.close") }}</el-button>
            <el-button v-if="dialogType !== 'view'" type="primary" @click="saveData">{{ t("deviceProtect.dailyProtect.save") }}</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 导入文件选择框 -->
      <el-dialog v-model="importDialogVisible" title="导入数据">
        <el-upload ref="uploadRef" action="/api/sys/upload/UploadExcelrcwh" :auto-upload="false" :before-upload="beforeUpload">
          <el-button type="primary">选择文件</el-button>
        </el-upload>
        <template #footer>
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="startImport">开始导入</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus"; // 引入 ElMessage
import { deviceProtectdailyProtectApi, productionReportCapacityApi } from "@/api";
import { Switch } from "@element-plus/icons-vue";
import SwitchChart from "./SwitchChart.vue";

import * as XLSX from "xlsx";
import moment from "moment";
import { convertCalendarData } from "@/utils";
import { useI18n } from "vue-i18n";
import { treeArr } from "@/config";
import { alramAnalysisApi } from "@/api";
const { t } = useI18n();

const title = ref("本月保养日历");
const dateData: any = ref([]);
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
  const query = {
    Type: 443,
    IsPage: false
  };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);
  dateData.value = convertCalendarData(data.list);
});
const isChart = ref(false);
const changeTable = () => {
  isChart.value = !isChart.value;
};
// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const getMachineOptions = computed(() => [
  // { label: "全部机台", value: "" }, // 添加空选项
  ...machineList.value.map(item => ({
    label: item.name,
    value: item.id
  }))
]);
// ProTable 实例引用
const proTableRef = ref(null);
const initParam = reactive({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")]
});
const exportParam = ref({});
// 对话框相关
const dialogVisible = ref(false);
const importDialogVisible = ref(false);
const dialogType = ref("add"); // 对话框类型：add, edit, view
// 表单数据
const formData = reactive({
  number: 0,
  factory: "",
  workshop: "",
  workline: "",
  deck: "",
  machCode: "",
  station: "",
  wstationCode: "",
  mcOrgan: "",
  mcItem: "",
  mcStandard: "",
  mcMethod: "",
  mcFrequency: "",
  mcStatus: "",
  mcPerson: "",
  mcTime: null,
  remarks: ""
});
// 表单引用
const formRef = ref(null);
// 上传组件引用
const uploadRef = ref(null);
// 选中的行数据
const selectedRows = ref([]);

// 维护/点检状态选项
const mcStatusOptions = [
  { value: "已完成", label: "已完成" },
  { value: "未完成", label: "未完成" },
  { value: "进行中", label: "进行中" }
];

// 表格列配置
const columns = computed(() => [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "id", label: t("deviceProtect.dailyProtect.table.id"), isShow: false },
  {
    prop: "time",
    label: t("deviceProtect.dailyProtect.table.time"),
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": t("deviceProtect.dailyProtect.table.startDate"),
        "end-placeholder": t("deviceProtect.dailyProtect.table.endDate")
      },
      defaultValue: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")]
    },
    isShow: false
  },
  {
    prop: "factory",
    label: t("deviceProtect.dailyProtect.form.factory"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "workshop",
    label: t("deviceProtect.dailyProtect.form.workshop"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "workline",
    label: t("deviceProtect.dailyProtect.form.workline"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  // {
  //   prop: "deck",
  //   label: t("deviceProtect.dailyProtect.form.deck"),
  //   width: 150,
  //   align: "left",
  //   search: { el: "input" }
  // },
  {
    prop: "deck",
    label: t("deviceProtect.dailyProtect.form.deck"),
    search: {
      el: "select",
      props: {
        clearable: false,
        placeholder: t("deviceProtect.dailyProtect.form.selectMachine")
      },
      on: {
        change: (val: string) => {
          const selectedMachine = machineList.value.find(machine => machine.name === val);
          if (proTableRef.value) {
            proTableRef.value.searchParam.mach = val;
            proTableRef.value.searchParam.machCode = selectedMachine?.id || "";
            proTableRef.value.getTableList();
          }
        }
      }
    },
    enum: computed(() => machineList.value.map(m => ({ label: m.name, value: m.name })))
  },
  {
    prop: "machCode",
    label: t("deviceProtect.dailyProtect.form.machineCode"),
    width: 150,
    align: "left"
    // search: { el: "input" }
  },
  {
    prop: "station",
    label: t("deviceProtect.dailyProtect.form.station"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "wstationCode",
    label: t("deviceProtect.dailyProtect.form.workstationCode"),
    width: 150,
    align: "left"
    // search: { el: "input" }
  },
  {
    prop: "mcOrgan",
    label: t("deviceProtect.dailyProtect.form.maintenanceOrg"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcItem",
    label: t("deviceProtect.dailyProtect.form.maintenanceItem"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcStandard",
    label: t("deviceProtect.dailyProtect.form.maintenanceStandard"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcMethod",
    label: t("deviceProtect.dailyProtect.form.maintenanceMethod"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcFrequency",
    label: t("deviceProtect.dailyProtect.form.maintenanceFrequency"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcStatus",
    label: t("deviceProtect.dailyProtect.form.maintenanceStatus"),
    enum: mcStatusOptions,
    width: 120
  },
  {
    prop: "mcPerson",
    label: t("deviceProtect.dailyProtect.form.maintenancePerson"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcTime",
    label: t("deviceProtect.dailyProtect.form.maintenanceTime"),
    width: 180,
    align: "center"
  },
  {
    prop: "remarks",
    label: t("deviceProtect.dailyProtect.form.remarks"),
    width: 200,
    align: "left"
  },
  { prop: "operation", label: t("deviceProtect.dailyProtect.table.operation"), width: 230, fixed: "right" }
]);
// 1. 获取机台列表（Type=0）
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineModel || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // 添加：设置默认选中第一个机台
    // if (machineList.value.length > 0) {
    //   param.value.machine = machineList.value[0].id; // 设置默认机台 id
    // }

    console.log("机台列表已更新:", machineList.value);
  } catch (error) {
    // console.error("机台列表请求失败:", error);
    // ElMessage.error("获取机台列表失败");
  }
};
// 模拟数据请求
const fetchData = async params => {
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
    EndDate: moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString()
  };
  delete params.time;
  return deviceProtectdailyProtectApi.ProdMaintenanceCheckGet({ ...params, ...time });
};

// 过滤后的数据请求
const currentFilter = ref(null);
const fetchFilteredData = async params => {
  exportParam.value = params;
  const allData = await fetchData(params);
  exportParam.value.pageSize = allData.data.total;

  if (!currentFilter.value) {
    return allData;
  }

  const filteredList = allData.data.list.filter(item => {
    if (currentFilter.value.type === "factory") {
      return item.fact === currentFilter.value.name;
    } else if (currentFilter.value.type === "workshop") {
      return item.wshop === currentFilter.value.name;
    } else if (currentFilter.value.type === "workline") {
      return item.prodLine === currentFilter.value.name;
    } else if (currentFilter.value.type === "deck") {
      return item.mach === currentFilter.value.name;
    }
    return true;
  });

  return {
    data: {
      total: filteredList.length,
      list: filteredList
    }
  };
};

// 树状结构数据
const treeData = ref([]);
// const treeProps = {
//   label: "name",
//   children: "children"
// };

// 构建树状结构数据
const buildTreeData = async () => {
  const allData = await fetchData();
  const factories = {};

  allData.data.list.forEach(item => {
    // 处理工厂
    if (!factories[item.factory]) {
      factories[item.factory] = {
        name: item.factory,
        type: "factory",
        children: []
      };
    }
    const factory = factories[item.factory];

    // 处理车间
    let workshop = factory.children.find(child => child.name === item.workshop);
    if (!workshop) {
      workshop = {
        name: item.workshop,
        type: "workshop",
        children: []
      };
      factory.children.push(workshop);
    }

    // 处理生产线
    let workline = workshop.children.find(child => child.name === item.workline);
    if (!workline) {
      workline = {
        name: item.workline,
        type: "workline",
        children: []
      };
      workshop.children.push(workline);
    }

    // 处理机台
    let deck = workline.children.find(child => child.name === item.deck);
    if (!deck) {
      deck = {
        name: item.deck,
        type: "deck",
        children: []
      };
      workline.children.push(deck);
    }
    // 处理工位
    let station = deck.children.find(child => child.name === item.station);
    if (!station) {
      station = {
        name: item.station,
        type: "station",
        children: []
      };
      station.children.push(station);
    }
  });
  treeData.value = Object.values(factories);
  return {
    data: Object.values(factories)
  };
  treeData.value = Object.values(factories);
};

// 打开查看详情对话框
const openViewDialog = row => {
  dialogType.value = "view";
  // 填充查看详情表单数据
  Object.assign(formData, row);
  dialogVisible.value = true;
};

// 保存数据
const saveData = async () => {
  try {
    // 表单验证（示例，实际需要添加验证规则）
    if (!formData.factory || !formData.workshop || !formData.workline) {
      ElMessage.error("工厂、车间、生产线为必填项");
      return;
    }

    const params = {
      ...formData
    };

    let response;
    if (dialogType.value === "add") {
      delete params.id;
      response = await deviceProtectdailyProtectApi.ProdMaintenanceCheckAdd(params);
    } else if (dialogType.value === "edit") {
      response = await deviceProtectdailyProtectApi.ProdMaintenanceCheckEdit(params);
    }

    if (response.code === 200) {
      ElMessage.success(`${dialogType.value === "add" ? "新增" : "编辑"}成功`);
      dialogVisible.value = false;
      proTableRef.value.getTableList();
      buildTreeData();
    } else {
      ElMessage.error(response.msg || "操作失败");
    }
  } catch (error) {
    ElMessage.error("网络请求失败，请重试");
  }
};

// 删除行

// 处理行点击事件
const handleRowClick = row => {
  console.log("点击行数据：", row);
};

// 处理选中行变化事件
const handleSelectionChange = rows => {
  selectedRows.value = rows;
};

// 打开导入对话框

// 导出数据
const exportData = async () => {
  try {
    // 获取当前表格的所有数据（包含分页参数）
    const params = {
      ...proTableRef.value.searchParam, // 获取当前查询参数
      page: 1,
      pageSize: exportParam.value.pageSize // 获取足够大的分页尺寸
    };

    // 调用API获取完整数据
    const response = await fetchFilteredData(params);
    if (response.code !== 200) throw new Error(response.msg);
    const tableData = response.data.list;

    // 过滤需要导出的列（排除操作列和选择列）
    const exportColumns = columns.filter(col => !["selection", "operation", "id"].includes(col.type || col.prop));

    // 准备表头数据
    const headers = exportColumns.map(col => col.label);

    // 准备表格数据（包括数据转换）
    const dataRows = tableData.map(item => {
      return exportColumns.map(col => {
        // 特殊处理时间字段
        if (col.prop === "mcTime" && item[col.prop]) {
          return new Date(item[col.prop]).toLocaleString();
        }
        // 处理状态字段的枚举值
        if (col.prop === "mcStatus") {
          return mcStatusOptions.find(opt => opt.value === item[col.prop])?.label || item[col.prop];
        }
        return item[col.prop] || "";
      });
    });

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...dataRows]);

    // 创建工作簿并导出
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "设备数据");

    // 生成文件名（带日期）
    const exportDate = new Date().toISOString().slice(0, 10);
    XLSX.writeFile(workbook, `设备维护记录_${exportDate}.xlsx`);

    ElMessage.success("导出成功");
  } catch (error) {
    ElMessage.error(`导出失败: ${error.message}`);
  }
};

// 导入文件前置处理
const beforeUpload = () => {
  // 这里可以添加文件类型、大小等验证逻辑
  return true;
};

// 开始导入
const startImport = () => {
  uploadRef.value!.submit();
  // 这里应该替换为实际的导入 API 请求
  importDialogVisible.value = false;
  ElMessage.success("导入成功");
  // 刷新表格数据
  proTableRef.value.getTableList();
  // 重新构建树状结构数据
  buildTreeData();
};

const currentTreeType = ref<any>(0);

const switchchartData = ref({});
/**
 * 处理树节点点击事件
 * @param nodeObj 点击的节点对象
 */
const handleNodeClick = (nodeObj: any) => {
  currentTreeType.value = nodeObj.level;

  const nodeParams: Record<string, string> = {};
  let currentNode = nodeObj;

  if (!isChart.value && proTableRef.value) {
    treeArr.forEach(key => delete proTableRef.value!.searchParam[key]);
  }

  while (currentNode) {
    const nodeData = currentNode.data;
    if (nodeData?.type && nodeData?.name) {
      nodeParams[nodeData.type] = nodeData.name;

      if (proTableRef.value) {
        proTableRef.value.searchParam[nodeData.type] = nodeData.name;
      }
    }
    currentNode = currentNode.parent;
  }
  switchchartData.value = nodeParams;
  // 根据模式执行相应操作
  if (!isChart.value) {
    proTableRef.value.search();
  }
};

onMounted(() => {
  buildTreeData();
  fetch("/api/user/shit")
    .then(response => {
      if (!response.ok) {
        throw new Error("Network response was not ok");
      }
      return response.json();
    })
    .then(data => {
      console.log("Data received:", data);
    })
    .catch(error => {
      console.error("There was a problem with the fetch operation:", error);
    });
});
</script>

<style scoped>
:deep(.filter) {
  width: auto !important;
}
.button-group {
  display: flex;
  flex-wrap: nowrap;
  gap: 8px;
  align-items: center;
}
.operation-buttons {
  display: flex;
  flex-wrap: nowrap;
  gap: 4px;
  align-items: center;
}
.dialog-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
.form-dialog {
  :deep(.el-form-item__label) {
    padding-right: 12px;
    white-space: nowrap;
  }
  :deep(.el-form-item__content) {
    flex: 1;
  }
}
:deep(.el-button) {
  white-space: nowrap;
}
:deep(.el-table .cell) {
  white-space: nowrap;
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-date-editor) {
  width: 100%;
}
:deep(.el-input-number) {
  width: 100%;
}

/* 自定义树的样式 */
.custom-tree {
  width: 20%;
  padding: 10px;
  margin-right: 20px;
  overflow-y: auto;
  background-color: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}

/* 树节点样式 */
.custom-tree .el-tree-node__content {
  height: 32px;
  padding: 0 8px;
  font-size: 14px;
  line-height: 32px;
  color: #333333;
  border-radius: 4px;
  transition: background-color 0.3s;
}

/* 鼠标悬停节点样式 */
.custom-tree .el-tree-node__content:hover {
  background-color: #eef1f6;
}

/* 选中节点样式 */
.custom-tree .el-tree-node.is-current > .el-tree-node__content {
  color: #ffffff;
  background-color: #409eff;
}

/* 展开/收缩图标样式 */
.custom-tree .el-tree-node__expand-icon {
  font-size: 12px;
  color: #909399;
}

/* 展开图标旋转动画 */
.custom-tree .el-tree-node__expand-icon.expanded {
  transition: transform 0.3s;
  transform: rotate(90deg);
}
</style>
