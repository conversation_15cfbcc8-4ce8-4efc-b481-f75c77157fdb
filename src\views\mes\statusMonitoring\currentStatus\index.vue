<template>
  <div class="main-box" id="651507581636677">
    <div class="table-main">
      <SearchForm :columns="[]" :search-param="{}" :search-col="3" :search="handleSearch" :reset="handleReset">
        <template #any>
          <el-button @click="exportData" :icon="Document">{{ $t("statusMonitoring.currentStatus.exportTable") }}</el-button>
          <el-button @click="exportImgs" :icon="Picture">{{ $t("statusMonitoring.currentStatus.exportImage") }}</el-button>
        </template>
      </SearchForm>
      <div class="flex w-full flex-1 gap-[20px]">
        <RealTime ref="realTimeRef" id="chart1" :machine-data="machineData" />
        <StackChart ref="stackRef" id="chart2" :stack-data="stackData" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="当前状态">
import StackChart from "./Statck.vue";
import RealTime from "./RealTime.vue";
import { Document, Picture } from "@element-plus/icons-vue";
import { exportElementsAsImages, transformChart, exportMultipleTablesToExcel } from "@/utils";
import moment from "moment";
import { productionReportCapacityApi } from "@/api";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

onMounted(() => {
  handleSearch();
});
const realTimeRef = ref();
const stackRef = ref();

// 定义数据
const machineData = ref([]);
const stackData = ref({
  xAxisData: [],
  seriesData: []
});

// 查询
const handleSearch = async () => {
  try {
    // 左边图表数据（Type 为 12 ，不传时间）
    const { data: leftData } = await productionReportCapacityApi.getListMesReportData({ Type: 12 });
    const leftResponseData = leftData?.list || [];
    machineData.value = leftResponseData
      .filter(item => item.type === 12)
      .map(item => ({
        machine: item.machine,
        machinestatuss: item.machinestatuss
      }));

    // 右边图表数据（Type 为 12 ，传时间）
    const query = {
      StartDate: moment().startOf("day").format("YYYY-MM-DD HH:mm:ss").toString(),
      EndDate: moment().endOf("day").set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString(),
      Type: 12
    };
    const { data: rightData } = await productionReportCapacityApi.getListMesReportData(query);
    const rightResponseData = rightData?.list || [];

    const machineStatusMap = new Map();
    rightResponseData.forEach(item => {
      const machine = item.machine;
      const status = item.machinestatuss;
      const startTime = moment(item.start_time);
      const endTime = moment(item.end_time);
      const duration = endTime.diff(startTime, "minutes");

      if (!machineStatusMap.has(machine)) {
        machineStatusMap.set(machine, {});
      }
      const machineStatus = machineStatusMap.get(machine);
      if (!machineStatus[status]) {
        machineStatus[status] = 0;
      }
      machineStatus[status] += duration;
    });

    const xAxisData = Array.from(machineStatusMap.keys());
    const statusSet = new Set();
    rightResponseData.forEach(item => statusSet.add(item.machinestatuss));
    const seriesData = Array.from(statusSet).map(status => ({
      name: status,
      data: xAxisData.map(machine => machineStatusMap.get(machine)[status] || 0)
    }));

    stackData.value = {
      xAxisData,
      seriesData
    };
  } catch (error) {
    console.error("获取数据失败:", error);
  }
};

// 重置
const handleReset = () => {
  handleSearch();
};

const exportImgs = () => {
  const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");

  const exportItems = [
    { elementId: "chart2", fileName: `${t("statusMonitoring.currentStatus.machineStatus")}${currentTime}.png` },
    { elementId: "chart1", fileName: `${t("statusMonitoring.currentStatus.machineStatusDetail")}${currentTime}.png` }
  ];
  try {
    exportElementsAsImages(exportItems);
    ElMessage.success(t("statusMonitoring.currentStatus.exportSuccess"));
  } catch (error) {
    ElMessage.error(t("statusMonitoring.currentStatus.exportError"));
  }
};

// 导出数据
const exportData = () => {
  const arr1 = transformChart(realTimeRef.value.chartRef, t("statusMonitoring.currentStatus.machine"), "/分钟").map((item: any, index) => {
    if (index == 0) {
      return [t("statusMonitoring.currentStatus.machine"), t("statusMonitoring.currentStatus.currentStatus")];
    } else {
      return item;
    }
  });

  const arr2 = transformChart(stackRef.value.chartRef, t("statusMonitoring.currentStatus.machine"), "/分钟");

  // 获取当前时间并格式化
  const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");

  // 将当前时间拼接到文件名中
  const fileName = `${t("statusMonitoring.currentStatus.machineStatus")}_${currentTime}.xlsx`;

  exportMultipleTablesToExcel(
    [arr1, arr2],
    [t("statusMonitoring.currentStatus.machineStatus"), t("statusMonitoring.currentStatus.machineStatusDetail")],
    fileName
  );
};
</script>

<style lang="scss" scoped>
.chart {
  height: 100%; /* 设置图表高度 */
}
</style>
