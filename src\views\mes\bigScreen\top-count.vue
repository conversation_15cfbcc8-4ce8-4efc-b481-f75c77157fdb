<template>
  <div class="dashboard1">
    <div class="counter">
      <div class="icon">🏭</div>
      <div class="label">机台总数</div>
      <div class="value">{{ totalMachines }}</div>
    </div>
    <div class="counter">
      <div class="icon">🔢</div>
      <div class="label">生产计数</div>
      <div class="value">{{ productionCount }}</div>
    </div>
    <div class="counter">
      <div class="icon">🔢</div>
      <div class="label">不良计数</div>
      <div class="value">{{ defectCount }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";

const totalMachines = ref(197);
const productionCount = ref(17147);
const defectCount = ref(17);
</script>

<style scoped>
.dashboard1 {
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  padding: 7px;
  margin-bottom: 5px;
  color: #ffffff;
  background-color: #1e1e2f;
  border-radius: 10px;
}
.counter {
  display: flex;
  align-items: center;
  text-align: center;
}
.icon {
  margin-bottom: 5px;
  font-size: 24px;
}
.value {
  font-size: 24px;
  font-weight: bold;
}
.label {
  margin-right: 5px;
  font-size: 16px;
}
</style>
