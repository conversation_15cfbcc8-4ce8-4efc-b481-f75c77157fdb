html.dark {
  color-scheme: dark;
  --el-color-primary: #409eff;
  --el-color-primary-light-3: rgb(50.8, 116.6, 184.5);
  --el-color-primary-light-5: rgb(42, 89, 137.5);
  --el-color-primary-light-7: rgb(33.2, 61.4, 90.5);
  --el-color-primary-light-8: rgb(28.8, 47.6, 67);
  --el-color-primary-light-9: rgb(24.4, 33.8, 43.5);
  --el-color-primary-dark-2: rgb(102.2, 177.4, 255);
  --el-color-success: #67c23a;
  --el-color-success-light-3: rgb(78.1, 141.8, 46.6);
  --el-color-success-light-5: rgb(61.5, 107, 39);
  --el-color-success-light-7: rgb(44.9, 72.2, 31.4);
  --el-color-success-light-8: rgb(36.6, 54.8, 27.6);
  --el-color-success-light-9: rgb(28.3, 37.4, 23.8);
  --el-color-success-dark-2: rgb(133.4, 206.2, 97.4);
  --el-color-warning: #e6a23c;
  --el-color-warning-light-3: rgb(167, 119.4, 48);
  --el-color-warning-light-5: #7d5b28;
  --el-color-warning-light-7: rgb(83, 62.6, 32);
  --el-color-warning-light-8: rgb(62, 48.4, 28);
  --el-color-warning-light-9: rgb(41, 34.2, 24);
  --el-color-warning-dark-2: rgb(235, 180.6, 99);
  --el-color-danger: #f56c6c;
  --el-color-danger-light-3: rgb(177.5, 81.6, 81.6);
  --el-color-danger-light-5: rgb(132.5, 64, 64);
  --el-color-danger-light-7: rgb(87.5, 46.4, 46.4);
  --el-color-danger-light-8: rgb(65, 37.6, 37.6);
  --el-color-danger-light-9: rgb(42.5, 28.8, 28.8);
  --el-color-danger-dark-2: rgb(247, 137.4, 137.4);
  --el-color-error: #f56c6c;
  --el-color-error-light-3: rgb(177.5, 81.6, 81.6);
  --el-color-error-light-5: rgb(132.5, 64, 64);
  --el-color-error-light-7: rgb(87.5, 46.4, 46.4);
  --el-color-error-light-8: rgb(65, 37.6, 37.6);
  --el-color-error-light-9: rgb(42.5, 28.8, 28.8);
  --el-color-error-dark-2: rgb(247, 137.4, 137.4);
  --el-color-info: #909399;
  --el-color-info-light-3: rgb(106.8, 108.9, 113.1);
  --el-color-info-light-5: rgb(82, 83.5, 86.5);
  --el-color-info-light-7: rgb(57.2, 58.1, 59.9);
  --el-color-info-light-8: rgb(44.8, 45.4, 46.6);
  --el-color-info-light-9: rgb(32.4, 32.7, 33.3);
  --el-color-info-dark-2: rgb(166.2, 168.6, 173.4);
  --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.36),
    0px 8px 20px rgba(0, 0, 0, 0.72);
  --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.72);
  --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.72);
  --el-box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, 0.72),
    0px 12px 32px #000000, 0px 8px 16px -8px #000000;
  --el-bg-color-page: #0a0a0a;
  --el-bg-color: #141414;
  --el-bg-color-overlay: #1d1e1f;
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  --el-text-color-placeholder: #8d9095;
  --el-text-color-disabled: #6c6e72;
  --el-border-color-darker: #636466;
  --el-border-color-dark: #58585b;
  --el-border-color: #4c4d4f;
  --el-border-color-light: #414243;
  --el-border-color-lighter: #363637;
  --el-border-color-extra-light: #2b2b2c;
  --el-fill-color-darker: #424243;
  --el-fill-color-dark: #39393a;
  --el-fill-color: #303030;
  --el-fill-color-light: #262727;
  --el-fill-color-lighter: #1d1d1d;
  --el-fill-color-extra-light: #191919;
  --el-fill-color-blank: transparent;
  --el-mask-color: rgba(0, 0, 0, 0.8);
  --el-mask-color-extra-light: rgba(0, 0, 0, 0.3);
}
html.dark .el-button {
  --el-button-disabled-text-color: rgba(255, 255, 255, 0.5);
}
html.dark .el-card {
  --el-card-bg-color: var(--el-bg-color-overlay);
}
html.dark .el-empty {
  --el-empty-fill-color-0: var(--el-color-black);
  --el-empty-fill-color-1: #4b4b52;
  --el-empty-fill-color-2: #36383d;
  --el-empty-fill-color-3: #1e1e20;
  --el-empty-fill-color-4: #262629;
  --el-empty-fill-color-5: #202124;
  --el-empty-fill-color-6: #212224;
  --el-empty-fill-color-7: #1b1c1f;
  --el-empty-fill-color-8: #1c1d1f;
  --el-empty-fill-color-9: #18181a;
}
