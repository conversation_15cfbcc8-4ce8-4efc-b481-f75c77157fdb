<template>
  <div class="sensor-data">
    <div class="sensor-row">
      <div v-for="item in sensorLabels" :key="item.key" class="sensor-item">
        <span class="value">{{ sensorData[item.key] }}</span>
        <span class="label">{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from "vue";

// 定义传感器数据
const sensorData = reactive({
  "光照(Lux)": 123,
  "噪音(db)": 0,
  "温度(°C)": 0,
  "湿度(%)": 0,
  "PM2.5(μm³)": 0,
  "硫化氢(ppm)": 0,
  "氨气(ppm)": 0
});

// 定义传感器标签和键
const sensorLabels = [
  { key: "光照(Lux)", label: "光照(Lux)" },
  { key: "噪音(db)", label: "噪音(db)" },
  { key: "温度(°C)", label: "温度(°C)" },
  { key: "湿度(%)", label: "湿度(%)" },
  { key: "PM2.5(μm³)", label: "PM2.5(μm³)" },
  { key: "硫化氢(ppm)", label: "硫化氢(ppm)" },
  { key: "氨气(ppm)", label: "氨气(ppm)" }
];
</script>

<style scoped lang="scss">
.sensor-data {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
  height: 90%;
}
.sensor-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 调整为合适的间隙 */
}
.sensor-item {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(25% - 10px); /* 一行四个，每个占25%，减去间隙 */
  font-size: 16px;
}
.label {
  margin-bottom: 5px;
  font-weight: bold;
}
.value {
  color: $primary-color;
}
</style>
