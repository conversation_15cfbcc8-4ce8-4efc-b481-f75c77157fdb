<template>
  <div class="main-box">
    <div class="table-box">
      <div>
        <SearchForm :columns="searchColumns" :search-param="searchParam" :search-col="4" :search="handleSearch" :reset="handleReset">
          <template #any>
            <el-button @click="exportData" :icon="Document">{{ t("common.tableExport") }}</el-button>
            <el-button @click="exportImgs" :icon="Picture">{{ t("common.imageExport") }}</el-button>
          </template>
        </SearchForm>
      </div>
      <!-- 操作按钮 -->
      <!-- <div class="flex-1 flex overflow-scroll"> -->
      <el-scrollbar height="100%">
        <div class="charts flex-1 grid grid-cols-2 grid-rows-2 w-full h-full" id="651535946457157">
          <div class="chart-item min-h-0 h-[60%] overflow-hidden" id="chart4">
            <Capacity ref="capacityChart" @export-data="handleCapacityData"></Capacity>
          </div>
          <div class="chart-item min-h-0 h-[60%] overflow-hidden" id="chart2">
            <Quality ref="qualityChart" @export-data="handleQualityData"></Quality>
          </div>
          <div class="chart-item min-h-0 h-[60%] overflow-hidden" id="chart3">
            <AchievementRate ref="achievementRateChart" @export-data="handleAchievementRateData"></AchievementRate>
          </div>
          <div class="chart-item min-h-0 h-[60%] overflow-hidden" id="chart1">
            <NgCount ref="ngCountChart" @export-data="handleNgcountData"></NgCount>
          </div>

          <div class="chart-item min-h-0 h-[60%] overflow-hidden" id="chart5">
            <Energy ref="energyChart" @export-data="handleEnergyData"></Energy>
          </div>
          <div class="chart-item min-h-0 h-[60%] overflow-hidden" id="chart6">
            <airflowStatistics ref="airflowChart" @export-data="handleAirflowData"></airflowStatistics>
          </div>
          <div class="chart-item min-h-0 h-[60%] overflow-hidden" id="chart7">
            <Preventive ref="preventiveChart" @export-data="handlePreventiveData"></Preventive>
          </div>
          <div class="chart-item min-h-0 h-[60%] overflow-hidden" id="chart8">
            <RawMaterial ref="rawMaterialChart" @export-data="handleRawMaterialData"></RawMaterial>
          </div>
        </div>
      </el-scrollbar>
      <!-- </div> -->
    </div>
  </div>
</template>

<script lang="tsx" setup name="生产信息汇总">
import { Document, Picture } from "@element-plus/icons-vue";

// import { ref } from "vue";
import moment from "moment";
import type { ColumnProps } from "@/components/ProTable/interface";
import SearchForm from "@/components/SearchForm/index.vue"; // 引入 SearchForm 组件
// import { exportMultipleTablesToExcel } from "@/utils";
import AchievementRate from "./AchievementRate.vue";
import Capacity from "./Capacity.vue";
import NgCount from "./NgCount.vue";
import Quality from "./Quality.vue";
import Energy from "./Energy.vue";
import airflowStatistics from "./airflowStatistics.vue";
import Preventive from "./Preventive.vue";
import RawMaterial from "./RawMaterial.vue";
import * as XLSX from "xlsx";
import { exportElementsAsImages } from "@/utils";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const searchParam = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")] // 初始化time为当天日期范围
});
const capacityChart = ref();
const energyChart = ref();
const qualityChart = ref();
const ngCountChart = ref();
const exportDataObj = ref([]);
const airflowChart = ref();
const preventiveChart = ref();
const rawMaterialChart = ref();
const achievementRateChart = ref();
// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const getMachineOptions = computed(() => [
  // { label: "全部机台", value: "" }, // 添加空选项
  ...machineList.value.map(item => ({
    label: item.name,
    value: item.id
  }))
]);
// 搜索字段配置
const searchColumns = ref<ColumnProps[]>([
  {
    prop: "time",
    label: t("common.compareList.time"),
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": "Start date",
        "end-placeholder": "End date"
        // defaultValue: [todayStart, todayEnd]
      }
    },
    isShow: false
  },
  {
    prop: "machine",
    label: t("common.compareList.machine"),
    search: {
      el: "select",
      props: {
        placeholder: "请选择机台",
        clearable: true,
        // 绑定动态选项
        options: getMachineOptions
      }
      // options: machineOptions // 使用计算属性
    }
  }
]);
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineName || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // 添加：设置默认选中第一个机台
    if (machineList.value.length > 0) {
      searchParam.value.machine = machineList.value[0].id; // 设置默认机台 id
    }

    console.log("机台列表已更新:", machineList.value);
  } catch (error) {
    console.error("机台列表请求失败:", error);
    ElMessage.error("获取机台列表失败");
  }
};
// 查询
const handleSearch = () => {
  // 调用子组件方法
  // capacityChart.value.tableRef.handleSearch(searchParam.value);
  // console.log(searchParam.value, "searchParam.value");
  // qualityChart.value.tableRef.handleSearch(searchParam.value);
  // ngCountChart.value.tableRef.handleSearch(searchParam.value);
  // achievementRateChart.value.tableRef.handleSearch(searchParam.value);
  // energyChart.value.tableRef.handleSearch(searchParam.value);
  // airflowChart.value.tableRef.handleSearch(searchParam.value);
  // preventiveChart.value.tableRef.handleSearch(searchParam.value);
  // rawMaterialChart.value.tableRef.handleSearch(searchParam.value);
  // 使用可选链操作符 ?. 避免 undefined 错误，并添加防御性检查
  capacityChart.value?.tableRef?.handleSearch(searchParam.value);
  qualityChart.value?.tableRef?.handleSearch(searchParam.value);
  ngCountChart.value?.tableRef?.handleSearch(searchParam.value);
  achievementRateChart.value?.tableRef?.handleSearch(searchParam.value);
  energyChart.value?.tableRef?.handleSearch(searchParam.value);
  airflowChart.value?.tableRef?.handleSearch(searchParam.value);
  preventiveChart.value?.tableRef?.handleSearch(searchParam.value);
  rawMaterialChart.value?.tableRef?.handleSearch(searchParam.value);
};

const handleCapacityData = (data: any) => {
  const param = {
    name: "产能",
    data: data.data
  };
  addDataIfNotExists(param);

  // 处理子组件返回的数据
  // 这里可以做进一步的数据处理或传递给其他组件
};
const addDataIfNotExists = (newItem: any) => {
  const index = exportDataObj.value.findIndex(item => item.name === newItem.name);
  if (index !== -1) {
    // 如果找到相同 name 的项，替换旧的项
    exportDataObj.value[index] = newItem;
  } else {
    // 如果没找到，添加新项
    exportDataObj.value.push(newItem);
  }
};
const handleAchievementRateData = (data: any) => {
  const param = {
    name: "计划达成率",
    data: data.data
  };
  // 处理子组件返回的数据
  addDataIfNotExists(param);
  // 处理子组件返回的数据
  // 这里可以做进一步的数据处理或传递给其他组件
};
const handleQualityData = (data: any) => {
  const param = {
    name: "综合效率",
    data: data.data
  };
  // 处理子组件返回的数据
  addDataIfNotExists(param);
  // 处理子组件返回的数据
  // 这里可以做进一步的数据处理或传递给其他组件
};
const handleNgcountData = (data: any) => {
  const param = {
    name: "ng统计",
    data: data.data
  };
  // 处理子组件返回的数据
  addDataIfNotExists(param);
};
const handleEnergyData = (data: any) => {
  const param = {
    name: "易损件",
    data: data.data
  };
  addDataIfNotExists(param);
  // 处理子组件返回的数据
  // 这里可以做进一步的数据处理或传递给其他组件
};
const handleAirflowData = (data: any) => {
  const param = {
    name: "电能及气流量能耗统计",
    data: data.data
  };
  addDataIfNotExists(param);
  // 处理子组件返回的数据
  // 这里可以做进一步的数据处理或传递给其他组件
};
const handlePreventiveData = (data: any) => {
  const param = {
    name: "维护保养",
    data: data.data
  };
  addDataIfNotExists(param);

  // 处理子组件返回的数据
  // 这里可以做进一步的数据处理或传递给其他组件
};
const handleRawMaterialData = (data: any) => {
  const param = {
    name: "原材料预警",
    data: data.data
  };
  addDataIfNotExists(param);

  // 处理子组件返回的数据
  // 这里可以做进一步的数据处理或传递给其他组件
};
// 重置
const handleReset = () => {
  // 重置searchParam
  console.log("重置");

  searchParam.value = {
    time: [moment().startOf("day"), moment().endOf("day")] // 重置为当天日期范围
  };
};

const exportImgs = () => {
  console.log(searchParam, "searchParam");
  // 格式化开始时间和结束时间
  const startTime = moment(searchParam.value.time[0]).format("YYYY-MM-DD");
  const endTime = moment(searchParam.value.time[1]).format("YYYY-MM-DD");
  const exportItems = [
    { elementId: "chart4", fileName: `生产报表${startTime}-${endTime}.png` },
    { elementId: "chart3", fileName: `综合效率${startTime}-${endTime}.png` },
    { elementId: "chart2", fileName: `计划达成率${startTime}-${endTime}.png` },
    { elementId: "chart1", fileName: `ng统计${startTime}-${endTime}.png` },
    { elementId: "chart5", fileName: `易损件${startTime}-${endTime}.png` },
    { elementId: "chart6", fileName: `电能及气流量能耗统计${startTime}-${endTime}.png` },
    { elementId: "chart7", fileName: `维护保养${startTime}-${endTime}.png` },
    { elementId: "chart8", fileName: `原材料预警${startTime}-${endTime}.png` }
  ];
  try {
    exportElementsAsImages(exportItems);
    ElMessage.success("图片批量导出成功");
  } catch (error) {
    ElMessage.error("图片批量导出失败，请查看错误提示");
  }
};
// 导出数据
const exportData = () => {
  const wb = XLSX.utils.book_new();

  // 导出文件
  for (let index = 0; index < exportDataObj.value.length; index++) {
    const element = exportDataObj.value[index];
    const data = element.data.split("\n").map(row => row.split(","));
    const ws = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(wb, ws, element.name);
  }
  const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");

  XLSX.writeFile(wb, searchParam.value.machine + `生产数据总览${currentTime}.xlsx`);

  ElMessage.success("导出成功");
};
// 添加 onMounted 钩子，页面加载时自动触发搜索
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
  // 等待子组件挂载完成（关键修复点）
  await nextTick();
  handleSearch(); // 此时子组件 ref 已可用
});
</script>

<style lang="scss" scoped>
.charts {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;

  // 根据图表数量和布局，设置合适的高度，这里以适应两个图表高度为例
  // height: calc(50% - 10px);
}

// 修改chart - item样式
.chart-item {
  box-sizing: border-box;

  // 确保每个图表项占据一半宽度
  width: calc(50% - 10px);

  // 为了在一列中放置两个图表，设置合适的高度
  //height: calc(50% - 10px);
  margin-bottom: 10px;
}
.chart {
  width: 100%;
  height: 100%;
}

// 针对小屏幕的响应式设计
@media (width <= 768px) {
  .chart-item {
    // 小屏幕下单列显示，宽度为100%
    width: 100%;

    // 适当调整高度以适应小屏幕
    height: 100%;
  }
}
</style>
