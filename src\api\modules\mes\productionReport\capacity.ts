import { mesProductionReport } from "@/api";
import { moduleRequest } from "@/api/request";
const http = moduleRequest("/sys/mes/");
const productionReportCapacityApi = {
  /** 获取生产数据 */
  getListMesReportData(params: mesProductionReport.ReportQuery) {
    return http.get("getListMesReportData", params);
  },
  /** 查询 OEE 和计划达成率信息表数据 */
  mes_oeeandplanachievementGet(params?: object) {
    return http.get("mes_oeeandplanachievementGet", params);
  },

  /** 查询 NG 统计信息表数据 */
  mes_ngstatisticsGet(params?: object) {
    return http.get("mes_ngstatisticsGet", params);
  },

  /** 保存计划标准配置 */
  savePlanStandard(params: mesProductionReport.PlanStandardConfig) {
    return http.post("savePlanStandard", params);
  },

  /** 获取计划标准配置 */
  getPlanStandard(params?: object) {
    return http.get("getPlanStandard", params);
  }
};
export { productionReportCapacityApi };
