// import { ColumnProps } from "@/components/ProTable/interface";
// import { useI18n } from "vue-i18n";
// import { FormRules } from "element-plus";

// 表头配置
export const getTableColumns = (t: Function) => [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "id", label: "Id", isShow: false },
  {
    prop: "factory",
    label: t("rawMaterialWarning.form.factory"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "workshop",
    label: t("rawMaterialWarning.form.workshop"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "workline",
    label: t("rawMaterialWarning.form.workline"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "machineCode",
    label: t("rawMaterialWarning.form.machineCode"),
    search: {
      el: "select",
      props: {
        clearable: false,
        placeholder: "请选择机台编码"
      },
      on: {
        change: (val: string) => {
          const selectedMachine = machineList.value.find(machine => machine.id === val);
          // 更新ProTable的搜索参数
          proTableRef.value.searchParam.machineCode = val;
          proTableRef.value.searchParam.machineName = selectedMachine?.name || "";
          proTableRef.value.getTableList(); // 重新加载数据
        }
      }
    },
    enum: computed(() => machineList.value.map(m => ({ label: m.id, value: m.id })))
  },
  {
    prop: "deck",
    label: t("rawMaterialWarning.form.deck"),
    search: {
      el: "select",
      props: {
        clearable: false,
        placeholder: "请选择机台名称"
      },
      on: {
        change: (val: string) => {
          const selectedMachine = machineList.value.find(machine => machine.name === val);
          // 更新ProTable的搜索参数
          proTableRef.value.searchParam.machineName = val;
          proTableRef.value.searchParam.machineCode = selectedMachine?.id || "";
          proTableRef.value.getTableList(); // 重新加载数据
        }
      }
    },
    enum: computed(() => machineList.value.map(m => ({ label: m.name, value: m.name })))
  },
  // {
  //   prop: "machineCode",
  //   label: t("rawMaterialWarning.form.machineCode"),
  //   search: {
  //     el: "select",
  //     props: { clearable: false, placeholder: "请选择机台编码" }
  //   }
  // },
  // {
  //   prop: "machineName",
  //   label: t("rawMaterialWarning.form.machineName"),
  //   search: {
  //     el: "select",
  //     props: { clearable: false, placeholder: "请选择机台名称" }
  //   }
  // },
  { prop: "stationCode", label: t("rawMaterialWarning.form.stationCode"), width: 150, align: "left" },
  { prop: "station", label: t("rawMaterialWarning.form.station"), width: 150, align: "left", search: { el: "input" } },
  { prop: "materialType", label: t("rawMaterialWarning.form.materialType"), width: 150, align: "left" },
  { prop: "batch", label: t("rawMaterialWarning.form.batch"), width: 150, align: "left" },
  { prop: "materialCode", label: t("rawMaterialWarning.form.materialCode"), width: 150, align: "left" },
  { prop: "materialName", label: t("rawMaterialWarning.form.materialName"), width: 150, align: "left" },
  { prop: "unit", label: t("rawMaterialWarning.form.unit"), width: 100, align: "left" },
  { prop: "totalQuantity", label: t("rawMaterialWarning.form.totalQuantity"), width: 150, align: "left" },
  { prop: "usedQuantity", label: t("rawMaterialWarning.form.usedQuantity"), width: 150, align: "left" },
  { prop: "remainingQuantity", label: t("rawMaterialWarning.form.remainingQuantity"), width: 150, align: "left" },
  {
    prop: "isReplaced",
    label: t("rawMaterialWarning.form.isReplaced"),
    width: 120,
    formatter: row => (row.isReplaced ? t("rawMaterialWarning.status.yes") : t("rawMaterialWarning.status.no"))
  },
  {
    prop: "feedingTime",
    label: t("rawMaterialWarning.form.feedingTime"),
    width: 180,
    align: "center",
    formatter: row => (row.feedingTime ? new Date(row.feedingTime).toLocaleString() : "")
  },
  { prop: "operator", label: t("rawMaterialWarning.form.operator"), width: 150, align: "left" },
  {
    prop: "operationTime",
    label: t("rawMaterialWarning.form.operationTime"),
    width: 180,
    align: "center",
    formatter: row => (row.operationTime ? new Date(row.operationTime).toLocaleString() : "")
  },
  { prop: "operation", label: t("rawMaterialWarning.operation"), width: 230, fixed: "right" }
];
// 弹窗字段配置
export const getFormItems = (t: Function) => [
  {
    prop: "factory",
    label: t("rawMaterialWarning.form.factory"),
    component: "input",
    rules: [{ required: true, message: t("rawMaterialWarning.rules.factory"), trigger: "blur" }]
  },
  {
    prop: "workshop",
    label: t("rawMaterialWarning.form.workshop"),
    component: "input",
    rules: [{ required: true, message: t("rawMaterialWarning.rules.workshop"), trigger: "blur" }]
  },
  {
    prop: "workline",
    label: t("rawMaterialWarning.form.workline"),
    component: "input",
    rules: [{ required: true, message: t("rawMaterialWarning.rules.workline"), trigger: "blur" }]
  },
  {
    prop: "machineCode",
    label: t("rawMaterialWarning.form.machineCode"),
    component: "select",
    options: [], // 将在组件中动态填充
    event: "change", // 指定事件类型
    rules: [{ required: true, message: t("rawMaterialWarning.rules.machineCode"), trigger: "blur" }]
  },
  {
    prop: "deck",
    label: t("rawMaterialWarning.form.deck"),
    component: "input",
    disabled: true,
    rules: [{ required: true, message: t("rawMaterialWarning.rules.deck"), trigger: "blur" }]
  },
  {
    prop: "stationCode",
    label: t("rawMaterialWarning.form.stationCode"),
    component: "input",
    rules: [{ required: true, message: t("rawMaterialWarning.rules.stationCode"), trigger: "blur" }]
  },
  {
    prop: "station",
    label: t("rawMaterialWarning.form.station"),
    component: "input",
    rules: [{ required: true, message: t("rawMaterialWarning.rules.station"), trigger: "blur" }]
  },
  {
    prop: "materialType",
    label: t("rawMaterialWarning.form.materialType"),
    component: "input",
    rules: [{ required: true, message: t("rawMaterialWarning.rules.materialType"), trigger: "blur" }]
  },
  {
    prop: "batch",
    label: t("rawMaterialWarning.form.batch"),
    component: "input",
    rules: [{ required: true, message: t("rawMaterialWarning.rules.batch"), trigger: "blur" }]
  },
  {
    prop: "materialCode",
    label: t("rawMaterialWarning.form.materialCode"),
    component: "input",
    rules: [{ required: true, message: t("rawMaterialWarning.rules.materialCode"), trigger: "blur" }]
  },
  {
    prop: "materialName",
    label: t("rawMaterialWarning.form.materialName"),
    component: "input",
    rules: [{ required: true, message: t("rawMaterialWarning.rules.materialName"), trigger: "blur" }]
  },
  {
    prop: "unit",
    label: t("rawMaterialWarning.form.unit"),
    component: "input",
    rules: [{ required: true, message: t("rawMaterialWarning.rules.unit"), trigger: "blur" }]
  },
  {
    prop: "totalQuantity",
    label: t("rawMaterialWarning.form.totalQuantity"),
    component: "input",
    type: "number" // 指定输入类型为数字
  },
  {
    prop: "usedQuantity",
    label: t("rawMaterialWarning.form.usedQuantity"),
    component: "input",
    type: "number"
  },
  {
    prop: "remainingQuantity",
    label: t("rawMaterialWarning.form.remainingQuantity"),
    component: "input",
    type: "number"
  },
  {
    prop: "isReplaced",
    label: t("rawMaterialWarning.form.isReplaced"),
    component: "switch"
  },
  {
    prop: "feedingTime",
    label: t("rawMaterialWarning.form.feedingTime"),
    component: "date-picker",
    pickerType: "datetime" // 指定日期选择器类型
  },
  {
    prop: "operator",
    label: t("rawMaterialWarning.form.operator"),
    component: "input"
  },
  {
    prop: "operationTime",
    label: t("rawMaterialWarning.form.operationTime"),
    component: "date-picker",
    pickerType: "datetime"
  }
];

// 表单初始值
export const getInitialFormData = () => ({
  factory: "",
  workshop: "",
  workline: "",
  machineCode: "",
  deck: "",
  stationCode: "",
  station: "",
  materialType: "",
  batch: "",
  materialCode: "",
  materialName: "",
  unit: "",
  totalQuantity: 0,
  usedQuantity: 0,
  remainingQuantity: 0,
  isReplaced: false,
  feedingTime: null,
  operator: "",
  operationTime: null
});
