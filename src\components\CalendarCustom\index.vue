<template>
  <div class="calendar-container" ref="container" :data-theme="currentTheme">
    <!-- 日历头部 -->
    <div class="calendar-header p-3">
      <div class="title font-bold">{{ headerTitle }}</div>
      <div class="header-controls">
        <div class="color-legend max-w-[12vw] overflow-x-scroll whitespace-nowrap">
          <div v-for="(typeInfo, index) in uniqueTypeInfos" :key="index" class="legend-item">
            <div class="color-swatch" :style="{ background: typeInfo.color }"></div>
            <span>{{ typeInfo.name }}</span>
          </div>
        </div>
        <slot name="right-control"></slot>
      </div>
    </div>

    <!-- 星期栏 -->
    <div class="weekdays">
      <div v-for="day in weekdays" :key="day" class="weekday">
        <span>{{ day }}</span>
      </div>
    </div>

    <!-- 日期网格 -->
    <div class="dates-grid">
      <div
        v-for="(date, index) in visibleDates"
        :key="index"
        class="date-cell"
        :class="{
          'current-month': date.isCurrentMonth,
          'has-data': hasData(date.data),
          weekend: isWeekend(date.date)
        }"
      >
        <div class="date-number" :class="{ 'position-top-left small-bold-font': hasData(date.data) }">{{ date.day }}</div>

        <v-chart v-if="hasData(date.data)" :autoresize="true" :option="getEChartsOption(date.data)" class="pie-chart" />

        <!-- <div class="data-labels">
          <template v-for="(item, type) in date.data" :key="type">
            <div
              v-if="item.value > 0"
              class="data-item"
              :style="{
                color: item.color,
                '--item-bg': item.color + '20'
              }"
            >
              {{ item.value }}
            </div>
          </template>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { useI18n } from "vue-i18n";

const { t, locale } = useI18n();

const props = defineProps({
  headerTitle: { type: String, default: () => "" },
  dateData: { type: Object, required: true },
  currentTheme: { type: String, default: "dark" }
});

const uniqueTypeInfos = computed(() => {
  const nameSet = new Set();
  Object.values(props.dateData).forEach(dayItems => dayItems.forEach(item => nameSet.add(item.name)));
  return Array.from(nameSet).map(name => {
    let foundItem;
    Object.values(props.dateData).some(dayItems => {
      foundItem = dayItems.find(item => item.name === name);
      return foundItem;
    });
    return { type: foundItem?.type || name, name, color: foundItem?.color || "#666666" };
  });
});

// 使用多语言配置的星期
const weekdays = computed(() => {
  const defaultDays = ["一", "二", "三", "四", "五", "六", "日"];
  const defaultDaysEn = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];

  try {
    const locale1 = locale.value;
    return locale1 === "en" ? defaultDaysEn : defaultDays;
  } catch (error) {
    console.error("Error getting weekdays translation:", error);
    return defaultDays;
  }
});

const visibleDates = computed(() => {
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const prevDays = firstDay.getDay() ? firstDay.getDay() - 1 : 6;

  return Array.from({ length: prevDays + lastDay.getDate() }, (_, i) => {
    const day = i - prevDays + 1;
    const date = new Date(year, month, day);
    const dateKey = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}`;
    const dayItems = props.dateData[dateKey] || [];
    const dataObject = dayItems.reduce((acc, item) => {
      acc[item.type] = item;
      return acc;
    }, {});
    return { date: dateKey, day: date.getDate(), isCurrentMonth: day > 0 && day <= lastDay.getDate(), data: dataObject };
  });
});

const getEChartsOption = data => {
  const items = Object.values(data).filter(item => item.value > 0);
  const total = items.reduce((sum, item) => sum + item.value, 0);
  if (total === 0) return {};
  const formattedItems = items.map(item => ({
    ...item,
    percentage: ((item.value / total) * 100).toFixed(2) + "%"
  }));
  return {
    tooltip: {
      trigger: "item",
      appendToBody: true,
      position: ["5%", "50%"],
      formatter: () =>
        [
          `<div style="font-size:0.6rem; max-width:300px; background: rgba(255,255,255,0.7); border-radius: 4px; padding: 12px; box-shadow: 0 2px 12px rgba(0,0,0,0.15); border: 1px solid rgba(0,0,0,0.1);">`,
          `<div style="font-weight:bold; margin-bottom:8px;">${t("calendar.tooltip.title")}</div>`,
          ...formattedItems.map(
            i => `
      <div style="display:flex; align-items:center; margin:5px 0; line-height:1.5;">
        <span style="display:inline-block; width:8px; height:8px; border-radius:50%; background:${i.color}; margin-right:8px;"></span>
        <span style="font-weight:500; text-shadow: 0 1px 1px rgba(0,0,0,0.1);">${i.name}</span>
        <span style="margin-left:auto; color:#444;">${i.value} ${t("calendar.tooltip.count")}</span>
        <span style="color:#666; margin-left:4px;">(${i.percentage} ${t("calendar.tooltip.percentage")})</span>
      </div>`
          ),
          "</div>"
        ].join(""),
      borderWidth: 1,
      borderColor: "rgba(0,0,0,0.1)",
      extraCssText: "box-shadow: 0 3px 14px rgba(0,0,0,0.15); backdrop-filter: blur(2px);",
      axisPointer: {
        type: "none"
      }
    },
    series: [
      {
        type: "pie",
        radius: "100%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        hoverAnimation: false,

        label: {
          show: true,
          normal: {
            show: true,
            formatter: "{c}",

            position: "inner" // 数值显示在内部
          }
        },

        data: items.map(item => ({
          name: item.name,
          value: item.value,
          itemStyle: {
            color: item.color,
            shadowColor: "rgba(0, 0, 0, 0.3)",
            shadowBlur: 5
          }
        }))
      }
    ]
  };
};

const hasData = data => Object.values(data).some(item => item.value > 0);
const isWeekend = dateString => new Date(dateString).getDay() === 0 || new Date(dateString).getDay() === 6;

const container = ref(null);
const containerWidth = ref(0);
const resizeObserver = new ResizeObserver(entries => (containerWidth.value = entries[0].contentRect.width));

onMounted(() => container.value && resizeObserver.observe(container.value));
onBeforeUnmount(() => resizeObserver.disconnect());
</script>

<style scoped>
/* 保留原样式，新增图表样式 */
.pie-chart {
  z-index: 999;
  width: 100%;
  height: 100%;
}

/* 基础变量 */
.calendar-container {
  --cell-aspect-ratio: 2.3;
  --pie-stroke: #ffffff;

  display: grid;
  grid-template-rows: auto auto auto 1fr;
  height: 100%;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  transition: background 0.3s ease;
}

/* 亮色主题 */
.calendar-container[data-theme="light"] {
  --bg-color: transparent;
  --header-bg: transparent;

  /* 主要文本颜色，对应 ElementPlus 的主要文本颜色变量 */
  --text-primary: var(--el-text-color-primary);

  /* 次要文本颜色，对应 ElementPlus 的次要文本颜色变量 */
  --text-secondary: var(--el-text-color-secondary);

  /* 边框颜色，使用 ElementPlus 的边框颜色变量 */
  --border-color: var(--el-border-color);

  /* 悬停背景色，模拟悬停时的背景色变化 */
  --hover-bg: var(--el-fill-color-light);

  /* 工作日颜色，用次要文本颜色代替 */
  --weekday-color: var(--el-text-color-secondary);

  /* 周末颜色，这里用 ElementPlus 的危险色代替 */
  --weekend-color: var(--el-color-danger);

  /* 今天的背景色，用 ElementPlus 的危险色 */
  --today-bg: var(--el-color-danger);

  /* 今天的文本颜色，使用白色 */
  --today-color: white;

  /* 提示框背景色，使用 ElementPlus 的弹出层背景色 */
  --tooltip-bg: var(--el-bg-color-overlay);

  /* 提示框边框，使用 ElementPlus 的边框颜色 */
  --tooltip-border: var(--el-border-color);

  /* 提示框文本颜色，使用主要文本颜色 */
  --tooltip-text: var(--el-text-color-primary);

  /* 饼图描边颜色，模拟深色描边 */
  --pie-stroke: var(--el-border-color-dark);

  background: var(--bg-color);
  border-radius: 12px;
  box-shadow: 0 2px 12px rgb(0 0 0 / 8%);
}

/* 暗色主题 */
.calendar-container[data-theme="dark"] {
  /* 背景颜色，ElementPlus 里组件默认背景色通常依赖于主题，这里用背景色基础变量 */
  --bg-color: transparent;
  --header-bg: transparent;

  /* 主要文本颜色，对应 ElementPlus 的主要文本颜色变量 */
  --text-primary: var(--el-text-color-primary);

  /* 次要文本颜色，对应 ElementPlus 的次要文本颜色变量 */
  --text-secondary: var(--el-text-color-secondary);

  /* 边框颜色，使用 ElementPlus 的边框颜色变量 */
  --border-color: var(--el-border-color);

  /* 悬停背景色，模拟悬停时的背景色变化 */
  --hover-bg: var(--el-fill-color-light);

  /* 工作日颜色，用次要文本颜色代替 */
  --weekday-color: var(--el-text-color-secondary);

  /* 周末颜色，这里用 ElementPlus 的危险色代替 */
  --weekend-color: var(--el-color-danger);

  /* 今天的背景色，用 ElementPlus 的危险色 */
  --today-bg: var(--el-color-danger);

  /* 今天的文本颜色，使用白色 */
  --today-color: white;

  /* 提示框背景色，使用 ElementPlus 的弹出层背景色 */
  --tooltip-bg: var(--el-bg-color-overlay);

  /* 提示框边框，使用 ElementPlus 的边框颜色 */
  --tooltip-border: var(--el-border-color);

  /* 提示框文本颜色，使用主要文本颜色 */
  --tooltip-text: var(--el-text-color-primary);

  /* 饼图描边颜色，模拟深色描边 */
  --pie-stroke: var(--el-border-color-dark);

  background: var(--bg-color);
  border-radius: 0;
  box-shadow: none;
}

/* 当 hasData 为真时的样式 */
.position-top-left.small-bold-font {
  /* 定位到左上角 */
  position: absolute;
  top: 0;
  left: 0;

  /* 移除底部边距（按需可选） */
  margin-bottom: 0;

  /* 字体变小加粗（示例：12px + 700 粗细，可根据需求调整） */
  font-size: 12px;
  font-weight: 700;
}

/* 颜色图例样式 */
.color-legend {
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 0 24px;
}
.legend-item {
  display: flex;
  gap: 4px;
  align-items: center;
  font-size: 12px;
  color: var(--text-secondary);
}
.color-swatch {
  width: 10px;
  height: 10px;
  border-radius: 2px;
}

/* 日历头部 */
.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--header-bg);
  border-bottom: 1px solid var(--border-color);
}
.header-controls {
  display: flex;
  align-items: center;
}

/* 星期栏 */
.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  padding: 12px 0;
  font-weight: 500;
  background: var(--header-bg);
}
.weekday {
  color: var(--weekday-color);
  text-align: center;
}
.weekday.weekend {
  color: var(--weekend-color);
}

/* 日期单元格 */
.dates-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-auto-rows: minmax(0, 1fr);
  overflow-y: auto;
}
.date-cell {
  position: relative;
  display: flex;
  flex-direction: column;
  aspect-ratio: var(--cell-aspect-ratio);
  padding: 8px;
  background: var(--bg-color);
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}
.date-cell:hover {
  z-index: 10;
  background: var(--hover-bg) !important;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-2px);
}
.date-number {
  z-index: 1;
  margin-bottom: 4px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}
.current-month:not(.has-data) .date-number {
  color: var(--text-secondary);
}
.today .date-number {
  display: inline-block;
  width: 24px;
  height: 24px;
  font-weight: bold;
  line-height: 24px;
  color: var(--today-color);
  text-align: center;
  background: var(--today-bg);
  border-radius: 50%;
}

/* 饼图 */
.pie-chart {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80%;
  height: 80%;
  opacity: 0.9;
  transform: translate(-50%, -50%);
}
.has-data .pie-chart {
  opacity: 1;
}

/* 数据标签 */
.data-labels {
  position: absolute;
  right: 0;
  bottom: 8px;
  left: 0;
  z-index: 1;
  display: flex;
  justify-content: space-around;
  font-size: 12px;
  font-weight: bold;
}
.data-item {
  padding: 2px 6px;
  font-size: 0.75rem;
  background: var(--item-bg);
  backdrop-filter: blur(2px);
  border-radius: 10px;
}

/* Tooltip */
.calendar-tooltip {
  position: fixed;
  z-index: 100;
  min-width: 180px;
  padding: 12px;
  pointer-events: none;
  background: var(--tooltip-bg);
  backdrop-filter: blur(4px);
  border: 1px solid var(--tooltip-border);
  border-radius: 8px;
  box-shadow: 0 6px 16px rgb(0 0 0 / 12%);
}
.tooltip-date {
  padding-bottom: 4px;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--tooltip-text);
  border-bottom: 1px solid var(--border-color);
}
.tooltip-items {
  display: grid;
  gap: 6px;
}
.tooltip-item {
  display: grid;
  grid-template-columns: 12px 1fr auto;
  gap: 8px;
  align-items: center;
  font-size: 0.85rem;
}
.tooltip-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}
.tooltip-label {
  color: var(--text-secondary);
}
.tooltip-value {
  font-weight: 600;
  color: var(--tooltip-text);
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 其他样式与原代码相同，此处省略重复部分... */
</style>
