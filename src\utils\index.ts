/**
 * @description utils
 * @license Apache License Version 2.0
 * @Copyright (c) 2022-Now 少林寺驻北固山办事处大神父王喇嘛
 * @remarks
 * SimpleAdmin 基于 Apache License Version 2.0 协议发布，可用于商业项目，但必须遵守以下补充条款:
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改SimpleAdmin源码头部的版权声明。
 * 3.分发源码时候，请注明软件出处 https://gitee.com/dotnetmoyu/SimpleAdmin
 * 4.基于本软件的作品，只能使用 SimpleAdmin 作为后台服务，除外情况不可商用且不允许二次分发或开源。
 * 5.请不得将本软件应用于危害国家安全、荣誉和利益的行为，不能以任何形式用于非法为目的的行为不要删除和修改作者声明。
 * 6.任何基于本软件而产生的一切法律纠纷和责任，均于我司无关
 * @see https://gitee.com/dotnetmoyu/SimpleAdmin
 */
import { isArray } from "@/utils/is";
import { FieldNamesProps } from "@/components/ProTable/interface";
import * as XLSX from "xlsx";
import html2canvas from "html2canvas";
import { ElMessage } from "element-plus";
import * as htmlToImage from "html-to-image";
import router from "@/routers";

import { useAuthStore, useUserStore } from "@/stores/modules";
// import { menuApi } from "@/api";

function extractPaths(menus) {
  const paths = [];
  menus.forEach(menu => {
    if (menu.path && (!menu.children || menu.children.length === 0) && menu.warningInfos) {
      paths.push(menu);
    }
    if (menu.children && menu.children.length > 0) {
      paths.push(...extractPaths(menu.children));
    }
  });
  return paths;
}

export const ingoreUploadFeishuImage = async (shitid, shitname) => {
  const route = router.currentRoute.value;

  const node = document.getElementById(shitid);
  if (!node) return;

  try {
    const blob = await htmlToImage.toBlob(node, {
      quality: 0.95
      // pixelRatio: 2F
    });

    const formData = new FormData();
    formData.append("file", blob, `${route.meta.id || "unknow"}.png`);
    formData.append("type", shitname);
    await fetch("/api/sys/mes/uploadFeishuImage", {
      method: "POST",
      body: formData
    });
  } catch (e) {
    console.error("上传失败:", e);
  }
};
export async function uploadFeishuImage(shitid, test) {
  if (test) {
    // const route = router.currentRoute.value;

    const node = document.getElementById(shitid);
    if (!node) return;

    try {
      const blob = await htmlToImage.toBlob(node, {
        quality: 0.5
        // pixelRatio: 2F
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "screenshot.png"; // 设置文件名
      a.click();
      // const formData = new FormData();
      // formData.append("file", blob, `${route.meta.id || "unknow"}.png`);
      // formData.append("type", "状态监控");
      // await fetch("/api/sys/mes/uploadFeishuImage", {
      //   method: "POST",
      //   body: formData
      // });
    } catch (e) {
      console.error("上传失败:", e);
    }
    return;
  }
  const route = router.currentRoute.value;
  const authStore = useAuthStore();
  const userStore = useUserStore();

  const menuList = authStore.showMenuListGet;
  const account = userStore.userInfo?.account;
  const isopen = userStore.isAlarm;
  const menus = extractPaths(menuList);
  const paths = menus.map(menu => menu.path);

  if (!paths.includes(route.path)) return;
  if (account !== "superAdmin" || !isopen) return;

  const currentmenu = menus.find(menu => menu.path === route.path);
  if (!currentmenu?.warningInfos) return;

  const warningInfos = JSON.parse(currentmenu.warningInfos);
  if (!warningInfos.length) return;
  console.log("----------------------------------------------------------");

  console.log("当前路由", currentmenu.title);

  // 新增定时器管理逻辑
  let timerManager = {
    timers: new Set<number>(),
    addTimer: (timerId: number) => {
      timerManager.timers.add(timerId);
    },
    clearAll: () => {
      timerManager.timers.forEach(timerId => clearTimeout(timerId));
      timerManager.timers.clear();
    }
  };
  timerManager.clearAll();
  // 主监控定时器
  setInterval(() => {
    if (!userStore.isAlarm) {
      timerManager.clearAll();
      // clearInterval(monitorTimer);

      return;
    } else {
    }

    // 防止重复创建
    if (timerManager.timers.size > 0) return;
    console.log(warningInfos, "warningInfos");

    // 生成所有定时任务
    warningInfos.forEach(({ timePoints, frequencyType, frequencyValue }) => {
      timePoints.forEach(timePoint => {
        const timerId = window.setTimeout(
          async () => {
            if (!validateSchedule(new Date(), frequencyType, frequencyValue)) return;

            const node = document.getElementById(shitid);
            if (!node) return;

            try {
              const blob = await htmlToImage.toBlob(node, {
                quality: 0.95,
                pixelRatio: 2
              });

              const formData = new FormData();
              formData.append("file", blob, `${route.meta.id || "unknow"}.png`);

              await fetch("/api/sys/mes/uploadFeishuImage", {
                method: "POST",
                body: formData
              });
            } catch (e) {
              console.error("上传失败:", e);
            }
          },
          calculateDelay(timePoint, frequencyType, frequencyValue)
        );

        timerManager.addTimer(timerId);
      });
    });
  }, 2000);
  console.log("----------------------------------------------------------");
}

export function validateSchedule(currentPath: any, frequencyType: string, frequencyValue: number) {
  const now = currentPath;
  const now1 = new Date();
  // console.log(`验证计划执行时间，频率类型：${frequencyType}，频率值：${frequencyValue}`);

  switch (frequencyType.toLowerCase()) {
    case "day":
      // console.log("每日任务，验证通过");
      return true;
    case "week": {
      const isValid = now.getDay() === (frequencyValue - 1) % 7;
      console.log(`每周任务验证，当前星期${now1.getDay()}，目标星期${frequencyValue - 1}，结果：${isValid}`);
      return isValid;
    }
    case "month": {
      const maxDay = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
      const targetDay = Math.min(frequencyValue, maxDay);
      const isValid = now.getDate() === targetDay;
      console.log(`每月任务验证，当前日期${now1.getDate()}，目标日期${targetDay}，结果：${isValid}`);
      return isValid;
    }
    default:
      console.log("未知频率类型，验证失败");
      return false;
  }
}

// 计算延迟时间（修改后）
export function calculateDelay(timePoint: string, frequencyType: string, frequencyValue: number, set: Set<number>, baseTime: number) {
  const [baseHours, baseMinutes, basesecond] = timePoint.split(":").map(Number);
  let target = new Date(baseTime);

  // 设置基础时间（提前10分钟）
  target.setHours(baseHours, baseMinutes - 7, basesecond, 0);

  // 周期匹配逻辑
  while (true) {
    const valid = validateSchedule(target, frequencyType, frequencyValue);
    if (valid) break;
    target.setDate(target.getDate() + 1);
  }

  // 处理时间偏移
  let delayMs = target.getTime() - baseTime;
  if (delayMs < 0) {
    switch (frequencyType.toLowerCase()) {
      case "day":
        target.setDate(target.getDate() + 1);
        break;
      case "week": {
        const daysToAdd = (7 - (target.getDay() - (frequencyValue - 1))) % 7;
        if (daysToAdd === 0) {
          target.setDate(target.getDate() + 7);
        }
        target.setDate(target.getDate() + daysToAdd);
        break;
      }
      case "month": {
        do {
          target.setMonth(target.getMonth() + 1);
          const maxDay = new Date(target.getFullYear(), target.getMonth() + 1, 0).getDate();
          target.setDate(Math.min(frequencyValue, maxDay));
        } while (!validateSchedule(target, frequencyType, frequencyValue));
        break;
      }
    }
    delayMs = target.getTime() - baseTime;
  }

  // 防止重复时间戳
  // if (set) {
  //   while (set.has(delayMs)) delayMs += 40000;
  set.add(delayMs);
  // }

  // 调试日志
  // console.log(`下次执行时间：${new Date(baseTime + delayMs).toLocaleString()}，延迟：${delayMs}ms`);
  const totalSeconds = Math.floor(delayMs / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  // 格式化输出
  // 设置第一行输出为青色
  console.log("\x1b[36m任务将在 %s 小时 %s 分钟 %s 秒后执行\x1b[0m", hours, minutes, seconds);
  // 设置第二行输出为洋红色
  console.log("\x1b[35m总延迟时间：%sms (%s秒)\x1b[0m", delayMs, totalSeconds);
  console.log("----------------------------------------------------------");
  return delayMs;
}

// // 辅助函数：扁平化菜单路径
// function extractPaths(menuList) {
//   return menuList.flatMap(menu => (menu.children?.length ? extractPaths(menu.children) : menu));
// }
// //接口传送图片
// export async function uploadFeishuImage(shitid) {
//   const route = router.currentRoute.value;
//   const authStore = useAuthStore();
//   const userStore = useUserStore();

//   const menuList = authStore.showMenuListGet;
//   console.log(menuList, "menuList");

//   const account = userStore.userInfo?.account;
//   const isopen = userStore.isAlarm;

//   const node: any = document.getElementById(shitid);
//   const paths: any = extractPaths(menuList).map(menu => menu.path);
//   console.log(paths, "传送路径");
//   console.log(route.meta.id, "route.meta.id");

//   if (!paths.includes(route.path)) {
//     return;
//   }
//   if (account == "superAdmin" && isopen) {
//     return;
//   }
//   const currentmenu: any = menuList.find(menu => menu.path === route.path);
//   if (currentmenu.warningInfos && JSON.parse(currentmenu.warningInfos).length > 0) {
//     const filename = (route.meta.id || "shit") + ".png";
//     try {
//       // 1. 生成图片 Blob
//       const blob: any = await htmlToImage.toBlob(node, {
//         quality: 0.95, // 设置图片质量（仅对 JPEG 有效）
//         // backgroundColor: "#ffffff", // 设置背景色
//         pixelRatio: 2 // 提高分辨率
//       });

//       // 2. 创建 FormData
//       const formData = new FormData();

//       formData.append("file", blob, filename); // 第三个参数是文件名

//       // 3. 发送 Fetch 请求
//       const response = await fetch("/api/sys/mes/uploadFeishuImage", {
//         method: "POST",
//         body: formData
//       });

//       // 4. 处理响应
//       if (!response.ok) throw new Error("上传失败");
//       const result = await response.json();
//       console.log("上传成功:", result);
//     } catch (error) {
//       console.error("发生错误:", error);
//     }
//   } else {
//     return;
//   }
//   console.log(currentmenu, "currentmenu");
// }

//转换接口传的日历数据
export function convertCalendarData(data) {
  const result = {};
  data.forEach(item => {
    const date = item.time;
    if (!result[date]) {
      result[date] = [];
    }
    result[date].push({
      type: "fault",
      value: item.value,
      name: item.type,
      color: getRandomHexColor()
    });
  });
  return result;
}
//清空对象的value值
export function clearObjectValues<T extends Record<string, any>>(obj: T): T {
  const newObj: any = { ...obj };
  for (const key in newObj) {
    if (newObj.hasOwnProperty(key)) {
      const value = newObj[key];
      if (Array.isArray(value)) {
        newObj[key] = [];
      } else if (typeof value === "object" && value !== null) {
        newObj[key] = clearObjectValues(value);
      } else {
        newObj[key] = null;
      }
    }
  }
  return newObj;
}

// const initParam1: any = { a: 1, c: 2 };
// const clearedParam1 = clearObjectValues(initParam1);
// console.log(clearedParam1);
//生成随机颜色
export function getRandomHexColor(): string {
  const hexChars = "0123456789ABCDEF";
  let color = "#";
  for (let i = 0; i < 6; i++) {
    color += hexChars[Math.floor(Math.random() * hexChars.length)];
  }
  return color;
}
// 单个元素导出为图片的函数
const exportSingleElementAsImage = async (elementId: string, fileName: string): Promise<void> => {
  const element = document.getElementById(elementId);
  if (element) {
    try {
      const canvas = await html2canvas(element);
      const dataURL = canvas.toDataURL("image/png");
      const link = document.createElement("a");
      link.href = dataURL;
      link.download = fileName;
      link.click();
    } catch (error) {
      ElMessage.error(`导出元素 ${elementId} 为图片时出错: ${error}`);
    }
  } else {
    ElMessage.error(`未找到指定 ID ${elementId} 的元素`);
  }
};

// 批量导出元素为图片的函数，参数为数组对象
interface ExportItem {
  elementId: string;
  fileName: string;
}

const exportElementsAsImages = async (exportItems: ExportItem[]): Promise<void> => {
  for (const item of exportItems) {
    await exportSingleElementAsImage(item.elementId, item.fileName);
  }
};

export { exportSingleElementAsImage, exportElementsAsImages };

export const transformChart = (obj: { getOption: () => {} }, name?: string | Array<string>, unit?: string) => {
  console.log(obj, "chart实例");

  if (obj?.getOption) {
    const option = obj.getOption();
    const name1 = name ? name : "时间";
    const unit1 = unit ? unit : "";
    const headers = [...(Array.isArray(name1) ? name1 : [name1]), ...option.series.map(s => (s.name ? s.name + unit1 : unit1))];
    const rows = option.xAxis[0].data.map((date, i) => [
      date,
      ...option.series.map(s => {
        const dataItem = s.data[i];
        return typeof dataItem === "object" && dataItem !== null && "value" in dataItem ? dataItem.value : (dataItem ?? "");
      })
    ]);
    return [headers, ...rows];
  }
  return [];
};

/**
 * 导出多个表格数据到一个 Excel 文件
 * @param tableDataList - 表格数据列表，每个元素是一个二维数组表示一个表格的数据
 * @param sheetNames - 工作表名称列表，与表格数据列表对应
 * @param fileName - 导出的 Excel 文件名称
 */
export const exportMultipleTablesToExcel = (tableDataList: any[][][], sheetNames: string[], fileName: string) => {
  const wb = XLSX.utils.book_new();
  tableDataList.forEach((tableData, index) => {
    const ws = XLSX.utils.aoa_to_sheet(tableData);
    const sheetName = sheetNames[index] || `Sheet${index + 1}`;
    XLSX.utils.book_append_sheet(wb, ws, sheetName);
  });
  XLSX.writeFile(wb, fileName);
};
const mode = import.meta.env.VITE_ROUTER_MODE;

/**
 * @description 获取localStorage
 * @param {String} key Storage名称
 * @returns {String}
 */
export function localGet(key: string) {
  const value = window.localStorage.getItem(key);
  try {
    return JSON.parse(window.localStorage.getItem(key) as string);
  } catch (error) {
    return value;
  }
}

/**
 * @description 存储localStorage
 * @param {String} key Storage名称
 * @param {*} value Storage值
 * @returns {void}
 */
export function localSet(key: string, value: any) {
  window.localStorage.setItem(key, JSON.stringify(value));
}

/**
 * @description 清除localStorage
 * @param {String} key Storage名称
 * @returns {void}
 */
export function localRemove(key: string) {
  window.localStorage.removeItem(key);
}

/**
 * @description 清除所有localStorage
 * @returns {void}
 */
export function localClear() {
  window.localStorage.clear();
}

/**
 * @description 判断数据类型
 * @param {*} val 需要判断类型的数据
 * @returns {String}
 */
export function isType(val: any) {
  if (val === null) return "null";
  if (typeof val !== "object") return typeof val;
  else return Object.prototype.toString.call(val).slice(8, -1).toLocaleLowerCase();
}

/**
 * @description 生成唯一 uuid
 * @returns {String}
 */
export function generateUUID() {
  let uuid = "";
  for (let i = 0; i < 32; i++) {
    let random = (Math.random() * 16) | 0;
    if (i === 8 || i === 12 || i === 16 || i === 20) uuid += "-";
    uuid += (i === 12 ? 4 : i === 16 ? (random & 3) | 8 : random).toString(16);
  }
  return uuid;
}

/**
 * 判断两个对象是否相同
 * @param {Object} a 要比较的对象一
 * @param {Object} b 要比较的对象二
 * @returns {Boolean} 相同返回 true，反之 false
 */
export function isObjectValueEqual(a: { [key: string]: any }, b: { [key: string]: any }) {
  if (!a || !b) return false;
  let aProps = Object.getOwnPropertyNames(a);
  let bProps = Object.getOwnPropertyNames(b);
  if (aProps.length != bProps.length) return false;
  for (let i = 0; i < aProps.length; i++) {
    let propName = aProps[i];
    let propA = a[propName];
    let propB = b[propName];
    if (!b.hasOwnProperty(propName)) return false;
    if (propA instanceof Object) {
      if (!isObjectValueEqual(propA, propB)) return false;
    } else if (propA !== propB) {
      return false;
    }
  }
  return true;
}

/**
 * @description 生成随机数
 * @param {Number} min 最小值
 * @param {Number} max 最大值
 * @returns {Number}
 */
export function randomNum(min: number, max: number): number {
  let num = Math.floor(Math.random() * (min - max) + max);
  return num;
}

/**
 * @description 获取当前时间对应的提示语
 * @returns {String}
 */
export function getTimeState() {
  let timeNow = new Date();
  let hours = timeNow.getHours();
  if (hours >= 6 && hours <= 10) return `早上好 ⛅`;
  if (hours >= 10 && hours <= 14) return `中午好 🌞`;
  if (hours >= 14 && hours <= 18) return `下午好 🌞`;
  if (hours >= 18 && hours <= 24) return `晚上好 🌛`;
  if (hours >= 0 && hours <= 6) return `凌晨好 🌛`;
}

/**
 * @description 获取浏览器默认语言
 * @returns {String}
 */
export function getBrowserLang() {
  let browserLang = navigator.language ? navigator.language : navigator.browserLanguage;
  let defaultBrowserLang = "";
  if (["cn", "zh", "zh-cn"].includes(browserLang.toLowerCase())) {
    defaultBrowserLang = "zh";
  } else {
    defaultBrowserLang = "en";
  }
  return defaultBrowserLang;
}

/**
 * @description 获取不同路由模式所对应的 url + params
 * @returns {String}
 */
export function getUrlWithParams() {
  const url = {
    hash: location.hash.substring(1),
    history: location.pathname + location.search
  };
  return url[mode];
}

/**
 * @description 使用递归扁平化菜单，方便添加动态路由
 * @param {Array} menuList 菜单列表
 * @returns {Array}
 */
export function getFlatMenuList(menuList: Menu.MenuOptions[]): Menu.MenuOptions[] {
  let newMenuList: Menu.MenuOptions[] = JSON.parse(JSON.stringify(menuList));
  return newMenuList.flatMap(item => [item, ...(item.children ? getFlatMenuList(item.children) : [])]);
}

/**
 * @description 使用递归过滤出需要渲染在左侧菜单的列表 (需剔除 isHide == true 的菜单)
 * @param {Array} menuList 菜单列表
 * @returns {Array}
 * */
export function getShowMenuList(menuList: Menu.MenuOptions[]) {
  let newMenuList: Menu.MenuOptions[] = JSON.parse(JSON.stringify(menuList));
  return newMenuList.filter(item => {
    item.children?.length && (item.children = getShowMenuList(item.children));
    return !item.meta?.isHide;
  });
}

/**
 * @description 使用递归找出所有面包屑存储到 pinia/vuex 中
 * @param {Array} menuList 菜单列表
 * @param {Array} parent 父级菜单
 * @param {Object} result 处理后的结果
 * @returns {Object}
 */
export const getAllBreadcrumbList = (menuList: Menu.MenuOptions[], parent = [], result: { [key: string]: any } = {}) => {
  for (const item of menuList) {
    result[item.path] = [...parent, item];
    if (item.children) getAllBreadcrumbList(item.children, result[item.path], result);
  }
  return result;
};

/**
 * @description 使用递归处理路由菜单 path，生成一维数组 (第一版本地路由鉴权会用到，该函数暂未使用)
 * @param {Array} menuList 所有菜单列表
 * @param {Array} menuPathArr 菜单地址的一维数组 ['**','**']
 * @returns {Array}
 */
export function getMenuListPath(menuList: Menu.MenuOptions[], menuPathArr: string[] = []): string[] {
  for (const item of menuList) {
    if (typeof item === "object" && item.path) menuPathArr.push(item.path);
    if (item.children?.length) getMenuListPath(item.children, menuPathArr);
  }
  return menuPathArr;
}

/**
 * @description 递归查询当前 path 所对应的菜单对象 (该函数暂未使用)
 * @param {Array} menuList 菜单列表
 * @param {String} path 当前访问地址
 * @returns {Object | null}
 */
export function findMenuByPath(menuList: Menu.MenuOptions[], path: string): Menu.MenuOptions | null {
  for (const item of menuList) {
    if (item.path === path) return item;
    if (item.children) {
      const res = findMenuByPath(item.children, path);
      if (res) return res;
    }
  }
  return null;
}

/**
 * @description 使用递归过滤需要缓存的菜单 name (该函数暂未使用)
 * @param {Array} menuList 所有菜单列表
 * @param {Array} keepAliveNameArr 缓存的菜单 name ['**','**']
 * @returns {Array}
 * */
export function getKeepAliveRouterName(menuList: Menu.MenuOptions[], keepAliveNameArr: string[] = []) {
  menuList.forEach(item => {
    item.meta.isKeepAlive && item.name && keepAliveNameArr.push(item.name);
    item.children?.length && getKeepAliveRouterName(item.children, keepAliveNameArr);
  });
  return keepAliveNameArr;
}

/**
 * @description 格式化表格单元格默认值 (el-table-column)
 * @param {Number} row 行
 * @param {Number} col 列
 * @param {*} callValue 当前单元格值
 * @returns {String}
 * */
export function formatTableColumn(row: number, col: number, callValue: any) {
  // 如果当前值为数组，使用 / 拼接（根据需求自定义）
  if (isArray(callValue)) return callValue.length ? callValue.join(" / ") : "--";
  return callValue ?? "--";
}

/**
 * @description 处理 ProTable 值为数组 || 无数据
 * @param {*} callValue 需要处理的值
 * @returns {String}
 * */
export function formatValue(callValue: any) {
  // 如果当前值为数组，使用 / 拼接（根据需求自定义）
  if (isArray(callValue)) return callValue.length ? callValue.join(" / ") : "--";
  return callValue ?? "--";
}

/**
 * @description 处理 prop 为多级嵌套的情况，返回的数据 (列如: prop: user.name)
 * @param {Object} row 当前行数据
 * @param {String} prop 当前 prop
 * @returns {*}
 * */
export function handleRowAccordingToProp(row: { [key: string]: any }, prop: string) {
  if (!prop.includes(".")) return row[prop] ?? "--";
  prop.split(".").forEach(item => (row = row[item] ?? "--"));
  return row;
}

/**
 * @description 处理 prop，当 prop 为多级嵌套时 ==> 返回最后一级 prop
 * @param {String} prop 当前 prop
 * @returns {String}
 * */
export function handleProp(prop: string) {
  const propArr = prop.split(".");
  if (propArr.length == 1) return prop;
  return propArr[propArr.length - 1];
}

/**
 * @description 根据枚举列表查询当需要的数据（如果指定了 label 和 value 的 key值，会自动识别格式化）
 * @param {String} callValue 当前单元格值
 * @param {Array} enumData 字典列表
 * @param {Array} fieldNames label && value && children 的 key 值
 * @param {String} type 过滤类型（目前只有 tag）
 * @returns {String}
 * */
export function filterEnum(callValue: any, enumData?: any, fieldNames?: FieldNamesProps, type?: "tag") {
  const value = fieldNames?.value ?? "value";
  const label = fieldNames?.label ?? "label";
  const children = fieldNames?.children ?? "children";
  let filterData: { [key: string]: any } = {};
  // 判断 enumData 是否为数组
  if (Array.isArray(enumData)) filterData = findItemNested(enumData, callValue, value, children);
  // 判断是否输出的结果为 tag 类型
  if (type == "tag") {
    return filterData?.tagType ? filterData.tagType : "";
  } else {
    return filterData ? filterData[label] : "--";
  }
}

/**
 * @description 递归查找 callValue 对应的 enum 值
 * */
export function findItemNested(enumData: any, callValue: any, value: string, children: string) {
  return enumData.reduce((accumulator: any, current: any) => {
    if (accumulator) return accumulator;
    if (current[value] === callValue) return current;
    if (current[children]) return findItemNested(current[children], callValue, value, children);
  }, null);
}
