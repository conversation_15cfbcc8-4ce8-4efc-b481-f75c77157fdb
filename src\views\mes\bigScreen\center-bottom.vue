<template>
  <v-chart :option="chartOptions" class="chart" />
</template>

<script setup>
import { ref, onMounted } from "vue";
import { chartColor } from "@/assets/const/index.ts";

// 注册 VChart 组件
const chartOptions = ref({});

onMounted(() => {
  chartOptions.value = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "vertical",
      left: "left",
      data: ["压差", "光照", "压差", "压差", "压差", "压差", "压差", "压差", "压差", "压差", "压差", "振动", "压差"],
      textStyle: {
        color: chartColor.fontColor // 使用外部引入的 fontColor 变量
      },
      backgroundColor: "transparent" // 移除图例背景颜色
    },
    series: [
      {
        // name: '访问来源',
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        label: {
          show: true,
          formatter: "{b}: {d}%", // 直接显示百分比
          position: "outside", // 标签显示在扇形外部
          fontSize: 12,
          color: chartColor.fontColor // 使用外部引入的 fontColor 变量
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "16",
            fontWeight: "bold"
          }
        },
        labelLine: {
          show: true,
          length: 10,
          lineStyle: {
            width: 1,
            color: chartColor.fontColor // 使用外部引入的 fontColor 变量
          }
        },
        data: [
          { value: 20, name: "压差" },
          { value: 30, name: "光照" },
          { value: 10, name: "压差" },
          { value: 20, name: "压差" },
          { value: 20, name: "压差" },
          { value: 20, name: "压差" },
          { value: 20, name: "压差" },
          { value: 20, name: "压差" },
          { value: 20, name: "压差" },
          { value: 20, name: "压差" },
          { value: 40, name: "振动" },
          { value: 20, name: "压差" }
        ]
      }
    ]
  };
});
</script>

<style scoped lang="scss">
.chart {
  width: 100%;

  // height: 400px;
}
</style>
