<template>
  <div class="sensor-data">
    <div class="sensor-row">
      <div v-for="item in sensorLabels" :key="item.key" class="sensor-item">
        <span class="value">{{ sensorData[item.key] }}</span>
        <span class="label">{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from "vue";

// 定义传感器数据
const sensorData = reactive({
  "电能(Kw.h)": 311.2,
  "电压(V)": 311.2,
  "电流(A)": 311.2,
  "压差(kpa)": 311.2,
  "温度(°C)": 311.2,
  "振动(mm/s)": 311.2,
  "转速(r/min)": 311.2,
  "气压(kpa)": 311.2
});

// 定义传感器标签和键
const sensorLabels = [
  { key: "电能(Kw.h)", label: "电能(Kw.h)" },
  { key: "电压(V)", label: "电压(V)" },
  { key: "电流(A)", label: "电流(A)" },
  { key: "压差(kpa)", label: "压差(kpa)" },
  { key: "温度(°C)", label: "温度(°C)" },
  { key: "振动(mm/s)", label: "振动(mm/s)" },
  { key: "转速(r/min)", label: "转速(r/min)" },
  { key: "气压(kpa)", label: "气压(kpa)" }
];
</script>

<style scoped lang="scss">
.sensor-data {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
  justify-content: center;
  height: 90%;

  // background-color: #0a1f44; // 设置背景色
}
.sensor-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* 调整为合适的间隙 */
}
.sensor-item {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(25% - 10px); /* 一行四个，每个占25%，减去间隙 */
  font-size: 16px;
}
.label {
  margin-bottom: 5px;
  font-weight: bold;
}
.value {
  color: $primary-color;
}
</style>
