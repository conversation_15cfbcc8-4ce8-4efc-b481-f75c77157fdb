<template>
  <el-dialog v-model="dialogVisible" :title="title" @close="closeDialog" width="1000px" :key="formKey">
    <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
      <el-row :gutter="20">
        <template v-for="(item, index) in formItems" :key="index">
          <el-col :span="item.span || 8">
            <el-form-item :label="item.label" :prop="item.prop" :rules="item.rules">
              <!-- 输入框 -->
              <el-input
                v-if="item.component === 'input'"
                v-model="formData[item.prop]"
                :type="item.type || 'text'"
                :disabled="viewMode || item.disabled"
                :placeholder="item.placeholder || `请输入${item.label}`"
              />

              <!-- 下拉选择 -->
              <el-select
                v-else-if="item.component === 'select'"
                v-model="formData[item.prop]"
                :disabled="viewMode"
                :placeholder="item.placeholder || `请选择${item.label}`"
                clearable
                @change="handleSelectChange(item, $event)"
              >
                <el-option v-for="option in item.options" :key="option.value" :label="option.label" :value="option.value" />
              </el-select>

              <!-- 开关 -->
              <el-switch v-else-if="item.component === 'switch'" v-model="formData[item.prop]" :disabled="viewMode" />

              <!-- 日期选择器 -->
              <el-date-picker
                v-else-if="item.component === 'date-picker'"
                v-model="formData[item.prop]"
                :type="item.pickerType || 'datetime'"
                :placeholder="item.placeholder || `请选择${item.label}`"
                :disabled="viewMode"
                style="width: 100%"
              />

              <!-- 数字输入 -->
              <el-input-number
                v-else-if="item.component === 'number'"
                v-model="formData[item.prop]"
                :min="item.min || 0"
                :step="item.step || 1"
                :disabled="viewMode"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </template>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="closeDialog">{{ closeText }}</el-button>
      <el-button v-if="!viewMode" type="primary" @click="saveData">{{ saveText }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance } from "element-plus";

const props = defineProps({
  visible: Boolean,
  title: String,
  formItems: {
    type: Array,
    default: () => []
  },
  formRules: {
    type: Object,
    default: () => ({})
  },
  initialData: {
    type: Object,
    default: () => ({})
  },
  viewMode: Boolean,
  saveText: {
    type: String,
    default: "保存"
  },
  closeText: {
    type: String,
    default: "关闭"
  },
  machineList: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(["update:visible", "save", "selectChange"]);

// 使用计算属性处理 visible 的双向绑定
const dialogVisible = computed({
  get: () => props.visible,
  set: val => {
    emit("update:visible", val);
  }
});

const formRef = ref<FormInstance>();
const formData = ref({ ...props.initialData });
// 添加表单唯一标识
const formKey = ref(0);

// 深度监听 initialData 的变化
watch(
  () => props.initialData,
  newVal => {
    formData.value = { ...newVal };

    // 特殊处理日期字段
    if (formData.value.feedingTime && typeof formData.value.feedingTime === "string") {
      formData.value.feedingTime = new Date(formData.value.feedingTime);
    }
    if (formData.value.operationTime && typeof formData.value.operationTime === "string") {
      formData.value.operationTime = new Date(formData.value.operationTime);
    }
  },
  { deep: true, immediate: true }
);

// 关闭弹窗
const closeDialog = () => {
  emit("update:visible", false);
};

// 保存数据
const saveData = async () => {
  if (!formRef.value) return;

  try {
    const valid = await formRef.value.validate();
    if (valid) {
      emit("save", formData.value);
      closeDialog();
    }
  } catch (error) {
    ElMessage.error("表单验证失败，请检查输入");
  }
};

// 选择框变化事件
const handleSelectChange = (item: any, value: any) => {
  formData.value[item.prop] = value;
  // 联动机台名称
  if (item.prop === "machineCode" && props.machineList.length) {
    const selected = props.machineList.find((m: any) => m.id === value);
    formData.value.machineName = selected ? selected.name : "";
  }
  emit("selectChange", {
    prop: item.prop,
    value: value,
    formData: formData.value
  });
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
