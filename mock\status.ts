import Mock from "mockjs";

// 统一响应格式（只读模式）
function mockResponse(data) {
  return {
    code: 200,
    msg: "请求成功",
    data: data,
    extras: "UnifyContextTake()",
    time: new Date().toISOString()
  };
}

// 辅助函数，将数字转换为中文数字
Mock.Random.extend({
  toChineseNumber: function (num) {
    const chnNumChar = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
    return chnNumChar[num];
  }
});

export default [
  // 机台状态表
  {
    url: "/api/sys/mes/sm_machinestatus",
    method: "get",
    response: () => {
      const data = Mock.mock({
        "list|1-10": [
          {
            "id|+1": 1,
            factory: "@word(8)",
            workshop: "@word(4)",
            production_line: "@word(6)",
            machine: "@word(8)",
            machinestatuss: [
              {
                "@pick(['Running', 'Stopped', 'Idle'])": "@integer(1, 100)"
              },
              {
                "@pick(['Running', 'Stopped', 'Idle'])": "@integer(1, 100)"
              },
              {
                "@pick(['Running', 'Stopped', 'Idle'])": "@integer(1, 100)"
              }
            ],
            start_time: '@datetime("yyyy-MM-dd HH:mm:ss")',
            end_time: '@datetime("yyyy-MM-dd HH:mm:ss")'
          }
        ]
      }).list;
      return mockResponse(data);
    }
  },

  // 运行状况信息表
  {
    url: "/api/sys/mes/sm_productionreport",
    method: "get",
    response: () => {
      const data = Mock.mock({
        "list|1-10": [
          {
            "id|+1": 1,
            factory: "@word(8)",
            workshop: "@word(4)",
            production_line: "@word(6)",
            machine: "@word(8)",
            machinestatuss: "123",
            start_time: '@datetime("yyyy-MM-dd HH:mm:ss")',
            end_time: '@datetime("yyyy-MM-dd HH:mm:ss")'
          }
        ]
      }).list;
      return mockResponse(data);
    }
  }
];
