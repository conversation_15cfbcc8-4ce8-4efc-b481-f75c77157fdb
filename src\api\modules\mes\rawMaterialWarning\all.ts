import { moduleRequest } from "@/api/request";

const http = moduleRequest("/sys/mes/"); // 复用基础路径

const rawMaterialApi = {
  /** 新增原材料信息 */
  ProductionMaterialRecordAdd(data?: object) {
    return http.post("ProductionMaterialRecordAdd", data);
  },

  /** 修改原材料信息 */
  ProductionMaterialRecordEdit(data?: object) {
    return http.post("ProductionMaterialRecordEdit", data);
  },

  /** 删除原材料信息 */
  ProdProductionMaterialRecordDelete(params?: object) {
    return http.delete("ProdProductionMaterialRecordDelete", params);
  },

  /** 查询原材料信息 */
  ProductionMaterialRecordGet(params?: object) {
    return http.get("ProductionMaterialRecordGet", params);
  },
  getTree(params?: object) {
    return http.get("ProductionMaterialRecordGetTree", params);
  }
};

export { rawMaterialApi };
