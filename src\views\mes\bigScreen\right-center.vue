<template>
  <div class="chart-container">
    <!-- 筛选组件 -->
    <div class="machine-select-btn">
      <FilterDialog
        v-model="filters"
        :machine-options="machineOptions"
        :show-machine-filter="false"
        :show-time-filter="true"
        @apply-filters="handleApplyFilters"
        @clear-filters="handleClearFilters"
      />
    </div>

    <!-- 雷达图 -->
    <v-chart :option="chartOptions" class="chart" />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { chartColor } from "@/assets/const/index.ts";
import FilterDialog from "@/components/filter-dialog/index.vue"; // 引入通用筛选组件

// 机台选项
const machineOptions = ref([
  { value: "machine1", label: "机台一" },
  { value: "machine2", label: "机台二" },
  { value: "machine3", label: "机台三" }
]);

// 筛选条件
const filters = ref({ machine: "machine1", time: "" }); // 默认选择第一个机台

// 模拟机台数据
const machineData = {
  machine1: { name: "机台一", data: [60, 73, 85, 40, 60, 48, 70] },
  machine2: { name: "机台二", data: [50, 60, 70, 80, 90, 60, 50] },
  machine3: { name: "机台三", data: [70, 80, 90, 60, 50, 40, 30] }
};

// 雷达图配置
const chartOptions = ref({});

const indicator = [
  { name: "机台1", max: 100 },
  { name: "机台2", max: 100 },
  { name: "机台3", max: 100 },
  { name: "机台4", max: 100 },
  { name: "机台5", max: 100 },
  { name: "机台6", max: 100 },
  { name: "机台7", max: 100 }
];

// 更新雷达图数据
const updateChartOptions = () => {
  const selectedMachineData = filters.value.machine ? machineData[filters.value.machine] : { name: "总数据", data: [60, 70, 80, 70, 60, 50, 40] };

  chartOptions.value = {
    tooltip: {},
    radar: {
      shape: "polygon",
      indicator,
      name: {
        textStyle: {
          color: chartColor.fontColor
        }
      },
      lineStyle: {
        color: chartColor.fontColor + "44"
      }
    },
    series: [
      {
        name: "性能",
        type: "radar",
        data: [
          {
            value: selectedMachineData.data,
            name: selectedMachineData.name
          }
        ],
        areaStyle: {},
        linestyle: {
          color: "red",
          width: 2
        },
        label: {
          show: true,
          formatter: function (params) {
            return params.value; // 显示每个数据点的值
          },
          position: "inside" // 将标签显示在雷达图内部
        }
      }
    ]
  };
};

// 处理应用筛选
const handleApplyFilters = newFilters => {
  filters.value = newFilters;
  updateChartOptions();
};

// 处理清空筛选
const handleClearFilters = () => {
  filters.value = { machine: "", time: "" };
  updateChartOptions();
};

// 初始化图表
onMounted(() => {
  updateChartOptions();
});

// 监听筛选条件变化
watch(filters, updateChartOptions, { deep: true });
</script>

<style scoped lang="scss">
.chart-container {
  position: relative;
}
.chart {
  height: 210px;
}
</style>
