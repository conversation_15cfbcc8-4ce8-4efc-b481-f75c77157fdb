<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'hideMachineSwitcher']"
    @data-loaded="handleDataLoaded"
    default-compare-id="production"
    :machines="machines"
    :init-params="initParams"
    ref="userTable"
  >
    <template #toolbar-right>
      <el-tooltip content="切换表格" placement="top">
        <el-button :icon="Switch" circle @click="changeTable" />
      </el-tooltip>
    </template>
  </ProChart>
</template>

<script setup lang="tsx">
import moment from "moment";
import { Switch } from "@element-plus/icons-vue";
import { productionReportCapacityApi } from "@/api";

const userTable = ref<any>(null);

const props = defineProps({
  currentTreeType: {
    type: String,
    default: "1" // 默认机台模式
  },
  initParams: {
    type: Object,
    default: () => {
      return {
        Type: 9
      };
    }
  }
});
const machines = ref<any[]>([]);
const emit = defineEmits(["toggleView"]);
const changeTable = () => emit("toggleView");
// watch(
//   () => props.initParams,
//   newValue => {
//     console.log(newValue);

//     if (userTable.value) {
//       userTable.value.getTableList();
//     }
//   }
// );
// 颜色配置（调整为故障相关）
const COLORS = {
  duration: "#409EFF", // 故障持续时间颜色
  faultCount: "#FF6B6B", // 故障次数颜色
  font: "#666",
  splitLine: "#eee"
};

// const levelmap = {
//   1: "车间",
//   2: "线体",
//   3: "机台",
//   4: "工位"
// };

// 图表配置（核心调整部分）
const chartOptions = ref({
  title: {
    subtext: "总故障次数：0",
    left: "center",
    textStyle: { fontSize: 16, color: COLORS.font }
  },
  toolbox: {
    show: true,
    feature: {
      magicType: { type: ["bar", "line"], title: { bar: "柱状图", line: "折线图" } }
    }
  },
  tooltip: {
    trigger: "axis",
    formatter: (params: any[]) => {
      return params
        .map(
          p => `
          <div style="display: flex; align-items: center; gap: 8px;">
            <span style="width: 10px; height: 10px; background: ${p.color}; border-radius: 2px; display: inline-block;"></span>
            ${p.seriesName}: ${p.data}
          </div>
        `
        )
        .join("<br>");
    }
  },
  legend: {
    data: ["故障持续时间", "故障次数"],
    top: 30,
    textStyle: { color: COLORS.font }
  },
  xAxis: {
    type: "category",
    data: [],
    // name: levelmap[props.currentTreeType] || "工位",
    axisLabel: { color: COLORS.font, rotate: 45, interval: 0 }
  },
  yAxis: [
    {
      type: "value",
      name: "故障持续时间（分钟）",
      axisLabel: { color: COLORS.duration },
      splitLine: { lineStyle: { color: COLORS.splitLine } }
    },
    {
      type: "value",
      name: "故障次数",
      axisLabel: { color: COLORS.faultCount },
      position: "right"
    }
  ],
  grid: { left: "3%", right: "3%", bottom: "3%", containLabel: true },
  series: []
});

// 数据转换函数（核心逻辑重写）
function transformData(dataList: any[]) {
  let groupField = "workline";
  if (props.currentTreeType == "1") {
    groupField = "workshop";
  } else if (props.currentTreeType == "2") {
    groupField = "workline";
  } else if (props.currentTreeType == "3") {
    groupField = "deck";
  } else {
    groupField = "deck";
  }
  const groupMap = new Map<string, { duration: number; count: number }>();

  dataList.forEach(item => {
    const key = item[groupField];
    if (!groupMap.has(key)) {
      groupMap.set(key, { duration: 0, count: 0 });
    }
    const group = groupMap.get(key)!;
    group.duration += item.duration_minutes;
    group.count += item.duration_count;
  });

  const categories = Array.from(groupMap.keys()).sort();
  const durationData = categories.map(key => groupMap.get(key)!.duration);
  const countData = categories.map(key => groupMap.get(key)!.count);

  return {
    categories,
    durationData,
    countData,
    machines: categories.map(name => ({ id: name, name })) // 用于机台/产线列表
  };
}

// 数据获取函数
const fetchData = async (params: any) => {
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss"),
    EndDate: moment(params.time[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
  };
  const query = { ...params, ...time, Type: 29 };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);

  const { categories, durationData, countData, machines: machineList } = transformData(data.list);
  machines.value = machineList;

  return {
    data: {
      isCompare: false,
      categories,
      seriesData: [durationData, countData]
    }
  };
};

// 数据加载回调
const handleDataLoaded = (data: any) => {
  const [durationData, countData] = data.seriesData;

  chartOptions.value = {
    ...chartOptions.value,
    title: {
      ...chartOptions.value.title,
      subtext: `总故障次数：${countData.reduce((a, b) => a + b, 0)}`
    },
    xAxis: { ...chartOptions.value.xAxis, data: data.categories },
    series: [
      {
        name: "故障持续时间",
        type: "bar",
        data: durationData,
        itemStyle: { color: COLORS.duration },
        label: {
          show: true,
          position: "inside",
          formatter: "{c}" // 显示各部分数值
        }
      },
      {
        name: "故障次数",
        type: "bar",
        yAxisIndex: 1, // 使用第二个y轴
        data: countData,
        itemStyle: { color: COLORS.faultCount },
        label: {
          show: true,
          position: "inside",
          formatter: "{c}" // 显示各部分数值
        }
      }
    ]
  };
};
</script>
