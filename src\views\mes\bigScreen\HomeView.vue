<script setup lang="ts">
// import { RouterView } from "vue-router";
// import ScaleScreen from "@/components/scale-screen";
import Headers from "./header.vue";
import Index from "./index.vue";
// import autofit from "autofit.js";
// const wrapperStyle = {};
// onMounted(() => {
//   autofit.init({
//     dh: 1080,
//     dw: 1920,
//     el: ".content_wrap",
//     resize: true
//   });
// });
</script>

<template>
  <!-- <scale-screen
    width="1920"
    height="1080"
    :delay="500"
    :full-screen="true"
    :box-style="{
      background: '#03050C',
      overflow: 'hidden'
    }"
    :wrapper-style="wrapperStyle"
    :auto-scale="true"
  > -->
  <div class="content_wrap">
    <Headers />
    <Index></Index>
  </div>
  <!-- </scale-screen> -->
</template>
<style lang="scss" scoped>
.content_wrap {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 16px;
  background-image: url("@/assets/img/pageBg.png");
  background-position: center center;
  background-size: cover;
}

/* 路由切换动画 */

/* fade-transform */
.fade-leave-active,
.fade-enter-active {
  transition: all 0.3s;
}

/* 可能为enter失效，拆分为 enter-from和enter-to */
.fade-enter-from {
  opacity: 0;
  transform: translateY(-30px);
}
.fade-enter-to {
  opacity: 1;
  transform: translateY(0);
}
.fade-leave-to {
  opacity: 0;
  transform: translateY(30px);
}
</style>
