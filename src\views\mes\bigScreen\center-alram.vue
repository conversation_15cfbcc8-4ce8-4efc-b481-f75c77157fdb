<script setup lang="ts">
// import { rightBottom } from "@/api";
import SeamlessScroll from "@/components/seamless-scroll";
import { computed, onMounted, reactive, ref } from "vue";
import EmptyCom from "@/components/empty-com";
import ItemWrap from "@/components/item-wrap";
import devicepng from "./device.png";
import TopCount from "./top-count.vue";
// 图片URL
const imageUrl = ref(devicepng);

// 警报列表数据
const alerts = ref([
  {
    description: "设备温度过高",
    time: "2022-07-05 20:00:00",
    duration: "时长5秒"
  },
  {
    description: "车间温度过高",
    time: "2022-07-05 20:00:00",
    duration: "时长5秒"
  },
  {
    description: "设备转速过高",
    time: "2022-07-05 20:00:00",
    duration: "时长5秒"
  },
  {
    description: "设备转速过高",
    time: "2022-07-05 20:00:00",
    duration: "时长5秒"
  }
]);
const defaultOption = ref({
  step: 4, // 数值越大速度滚动越快
  hover: true, // 是否开启鼠标悬停stop
  wheel: false, //在开启鼠标悬停的情况下是否开启滚轮滚动，默认不开启
  openWatch: true, // 开启数据实时监控刷新dom
  direction: 1, // 0向下 1向上 2向左 3向右
  limitScrollNum: 4, // 开始无缝滚动的数据量 this.dataList.length
  singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
  singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
  singleWaitTime: 3000 // 单步运动停止的时间(默认值1000ms)
});

const indexConfig = ref({
  leftBottomSwiper: true, //左轮播
  rightBottomSwiper: true //右下轮播
});
const state = reactive<any>({
  list: [],
  defaultOption: {
    ...defaultOption.value,
    singleHeight: 252,
    limitScrollNum: 4,
    singleWaitTime: 1000,
    step: 1.5
  },
  scroll: true
});

const getData = () => {
  // rightBottom({ limitNum: 20 })
  //   .then(res => {
  //     console.log("右下", res);
  //     if (res.success) {
  //       state.list = res.data.list;
  //     } else {
  //       ElMessage({
  //         message: res.msg,
  //         type: "warning"
  //       });
  //     }
  //   })
  //   .catch(err => {
  //     ElMessage.error(err);
  //   });
};

const comName = computed(() => {
  if (indexConfig.value.rightBottomSwiper) {
    return SeamlessScroll;
  } else {
    return EmptyCom;
  }
});
// function montionFilter(val: any) {
//   // console.log(val);
//   return val ? Number(val).toFixed(2) : "--";
// }
// const handleAddress = (item: any) => {
//   return `${item.provinceName}/${item.cityName}/${item.countyName}`;
// };
onMounted(() => {
  getData();
});
</script>

<template>
  <div class="dashboard">
    <TopCount></TopCount>
    <div class="image-container">
      <img :src="imageUrl" alt="设备图像" />
    </div>
    <div style="margin-bottom: 5px">
      <ItemWrap title="报警记录"> </ItemWrap>
    </div>
    <div class="right_bottom_wrap beautify-scroll-def" :class="{ 'overflow-y-auto': !indexConfig.rightBottomSwiper }">
      <component
        :is="comName"
        :list="alerts"
        v-model="state.scroll"
        :single-height="state.defaultOption.singleHeight"
        :step="state.defaultOption.step"
        :limit-scroll-num="state.defaultOption.limitScrollNum"
        :hover="state.defaultOption.hover"
        :single-wait-time="state.defaultOption.singleWaitTime"
        :wheel="state.defaultOption.wheel"
      >
        <div class="alert-list">
          <ul>
            <li v-for="(alert, index) in alerts" :key="index" class="alert-item">
              <span class="alert-dot"></span>
              <span>{{ index + 1 }}. {{ alert.description }}</span>
              <span>{{ alert.time }}</span>
              <span>{{ alert.duration }}</span>
            </li>
          </ul>
        </div>
      </component>
    </div>
  </div>
</template>

<style scoped lang="scss">
.dashboard {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  color: white;
}
.image-container {
  margin-bottom: 20px;
}
.image-container img {
  max-width: 100%;
  height: auto;
}
.alert-list {
  width: 100%;
  max-width: 600px;
}
.alert-list ul {
  padding: 0;
  list-style: none;
}
.alert-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.alert-dot {
  width: 10px;
  height: 10px;
  margin-right: 10px;
  background-color: red;
  border-radius: 50%;
}
.right_bottom {
  width: 100%;
  height: 100%;
  .right_center_item {
    display: flex;
    align-items: center;
    justify-content: center;
    height: auto;
    padding: 10px;
    font-size: 14px;
    color: #ffffff;
    .orderNum {
      margin: 0 20px 0 -20px;
    }
    .inner_right {
      position: relative;
      flex-shrink: 0;
      width: 400px;
      height: 100%;
      line-height: 1.5;
      .dibu {
        position: absolute;
        bottom: -12px;
        left: -2%;
        width: 104%;
        height: 2px;
        background-image: url("@/assets/img/zuo_xuxian.png");
        background-size: cover;
      }
    }
    .info {
      display: flex;
      align-items: center;
      margin-right: 10px;
      .labels {
        flex-shrink: 0;
        font-size: 12px;
        color: rgb(255 255 255 / 60%);
      }
      .zhuyao {
        font-size: 15px;
        color: $primary-color;
      }
      .ciyao {
        color: rgb(255 255 255 / 80%);
      }
      .warning {
        font-size: 15px;
        color: #e6a23c;
      }
    }
  }
}
.right_bottom_wrap {
  width: 100%;
  height: 120px;
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
</style>
