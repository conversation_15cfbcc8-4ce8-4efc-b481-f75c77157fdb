<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'compare']"
    @data-loaded="handleDataLoaded"
    :compare-list="compareList"
    default-compare-id="OEE"
    :machines="machines"
    :init-params="initParams"
    id="648697115516997"
    chart-height="222px"
    @export-data="handleExportData"
  />
</template>

<script setup lang="tsx" name="综合效率">
import { ref } from "vue";
import moment from "moment";
import { productionReportCapacityApi } from "@/api";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const initParams = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
  type: 2
});
const machines = ref<any[]>([]);
const emits = defineEmits(["data-ready", "export-data"]);

const handleExportData = (csvContent: string) => {
  // 父组件可以在这里做一些处理，然后继续传递给爷组件

  // eslint-disable-next-line vue/custom-event-name-casing
  emits("export-data", {
    data: csvContent
  });
};
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");
// 颜色配置常量
const COLORS = {
  OEE: "#00bfff",
  timeUtilization: "#ff4d4f",
  performanceUtilization: "#fac858",
  ppm: "#52c41a",
  font: chartColor,
  splitLine: "#eee"
};

const compareList = [
  {
    label: t("common.compareList.OEE"),
    value: "OEE"
  },
  {
    label: t("common.compareList.timeUtilization"),
    value: "timeUtilization"
  },
  {
    label: t("common.compareList.performanceUtilization"),
    value: "performanceUtilization"
  },
  {
    label: t("dashboard.centerCenter.ppm"),
    value: "ppm"
  }
];

// 图表配置
const chartOptions = ref({
  title: {
    // text: "OEE统计看板",
    subtext: t("common.compareList.averageOEE") + ": 0", // 平均OEE：0
    left: "center",
    textStyle: {
      fontSize: 16,
      color: COLORS.font
    },
    subtextStyle: {
      color: COLORS.font
    }
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: t("productionReport.capacity.switchToLine"), // 切换为折线图
          bar: t("productionReport.capacity.switchToBar") // 切换为柱状图
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const OEE = params.find((p: any) => p.seriesName === t("common.compareList.OEE"));
      const timeUtilization = params.find((p: any) => p.seriesName === t("common.compareList.timeUtilization"));
      const performanceUtilization = params.find((p: any) => p.seriesName === t("common.compareList.performanceUtilization"));
      const ppm = params.find((p: any) => p.seriesName === t("dashboard.centerCenter.ppm"));
      return `
        <div style="padding:5px;min-width:120px">
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.OEE};border-radius:50%"></span>
            ${t("common.compareList.OEE")}: ${OEE?.data}%
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.timeUtilization};border-radius:50%"></span>
            ${t("common.compareList.timeUtilization")}: ${timeUtilization?.data}%
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.performanceUtilization};border-radius:50%"></span>
            ${t("common.compareList.performanceUtilization")}: ${performanceUtilization?.data}%
          </div>
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.ppm};border-radius:50%"></span>
            ${t("dashboard.centerCenter.ppm")}: ${ppm?.data}
          </div>
        </div>
      `;
    }
  },
  legend: {
    data: [
      t("common.compareList.OEE"), // OEE
      t("common.compareList.timeUtilization"), // 时间稼动率
      t("common.compareList.performanceUtilization"), // 性能稼动率
      t("dashboard.centerCenter.ppm") // PPM
    ],
    top: 30,
    textStyle: { color: COLORS.font }
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      color: COLORS.font,
      interval: 0, // 强制显示所有标签
      rotate: 45 // 旋转标签以避免重叠
    }
  },
  yAxis: [
    {
      type: "value",
      name: t("common.compareList.ratio"), //比率 (%)
      min: 0,
      max: 100,
      axisLabel: {
        color: COLORS.font,
        formatter: (value: number) => `${value}%`
      },
      splitLine: { lineStyle: { color: COLORS.splitLine } }
    },
    {
      type: "value",
      name: "PPM",
      min: 0,
      axisLabel: {
        color: COLORS.font,
        formatter: (value: number) => `${value}`
      },
      splitLine: { show: false }
    }
  ],
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    // top: 100,
    containLabel: true
  },
  series: []
});
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineName || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // // 添加：设置默认选中第一个机台
    // if (machineList.value.length > 0) {
    //   searchParam.value.machine = machineList.value[0].id; // 设置默认机台 id
    // }

    console.log("机台列表已更新:", machineList.value);
  } catch (error) {
    console.error("机台列表请求失败:", error);
    ElMessage.error("获取机台列表失败");
  }
};
function transformData(responseData, timeType) {
  const machineMap = new Map();
  const timeSet = new Set();
  const machineIdSet = new Set();

  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Mon":
      formatPattern = "MM-DD";
      break;
    case "Year":
      formatPattern = "YYYY-MM";
      break;
    default:
      formatPattern = "HH:00";
  }

  // 遍历响应数据，按机台分组并收集所有时间点和机台 ID
  responseData.forEach(item => {
    const machine = item.machine;
    // 在transformData函数的时间处理部分
    let startTime;
    startTime = moment(item.start_time).format(formatPattern);

    const OEE = item.oee;
    const timeUtilization = item.availability * 100;
    const performanceUtilization = item.performanceefficiency * 100;
    // 计算PPM：假设从数据中获取不良品数量和总产量
    const defectiveCount = item.defective_count || 0;
    const totalCount = item.total_count || item.prod_count || 1;
    const ppm = totalCount > 0 ? (defectiveCount / totalCount) * 1000000 : 0;

    if (!machineMap.has(machine)) {
      machineMap.set(machine, {
        machine,
        OEE: [],
        timeUtilization: [],
        performanceUtilization: [],
        ppm: [],
        totalOEE: 0,
        totalTimeUtilization: 0,
        totalPerformanceUtilization: 0,
        totalPpm: 0
      });
    }

    const machineData = machineMap.get(machine);
    machineData.OEE.push(OEE);
    machineData.timeUtilization.push(timeUtilization);
    machineData.performanceUtilization.push(performanceUtilization);
    machineData.ppm.push(ppm);
    machineData.totalOEE += OEE;
    machineData.totalTimeUtilization += timeUtilization;
    machineData.totalPerformanceUtilization += performanceUtilization;
    machineData.totalPpm += ppm;

    timeSet.add(startTime);
    machineIdSet.add(item.machine);
  });

  // 对时间点进行排序
  const categories = Array.from(timeSet).sort();

  // 计算总平均值
  machineMap.forEach(machineData => {
    machineData.totalOEE = (machineData.totalOEE / machineData.OEE.length).toFixed(1);
    machineData.totalTimeUtilization = (machineData.totalTimeUtilization / machineData.timeUtilization.length).toFixed(1);
    machineData.totalPerformanceUtilization = (machineData.totalPerformanceUtilization / machineData.performanceUtilization.length).toFixed(1);
    machineData.totalPpm = (machineData.totalPpm / machineData.ppm.length).toFixed(0);
  });

  const allmachine = Array.from(machineMap.values());

  const compare = [
    {
      OEE: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.OEE,
        total: parseFloat(machine.totalOEE)
      })),
      timeUtilization: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.timeUtilization,
        total: parseFloat(machine.totalTimeUtilization)
      })),
      performanceUtilization: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.performanceUtilization,
        total: parseFloat(machine.totalPerformanceUtilization)
      })),
      ppm: allmachine.map(machine => ({
        machine: machine.machine,
        data: machine.ppm,
        total: parseFloat(machine.totalPpm)
      }))
    }
  ];

  // 构建 machines 数组
  const machines = Array.from(machineIdSet).map(deck => ({
    id: deck,
    name: deck
  }));

  return {
    allmachine,
    compare,
    categories,
    machines
  };
}

// 修改数据获取函数
const fetchData = async (params: any) => {
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
    EndDate: moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString()
  };
  const query = {
    ...time,
    type: params.type
  };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);

  // 从数据中获取 mode 类型
  let mode = "Hour"; // 默认值
  if (data && data.list.length > 0) {
    mode = data.list[0].type;
  }

  const data1 = transformData(data.list, mode);
  machines.value = data1.machines;

  // 普通模式数据请求
  if (!params.compareMode) {
    const machine = params.machine;
    const machineInfo = data1.allmachine.find(item => item.machine === machine) || data1.allmachine[0];
    if (!machineInfo) {
      console.error(`未找到机台 ${machine} 的数据`);
      return {
        data: {
          categories: ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"],
          seriesData: [[], [], [], []],
          isCompare: false
        }
      };
    }
    const { OEE, timeUtilization, performanceUtilization, ppm } = machineInfo;
    return {
      data: {
        categories: data1.categories,
        seriesData: [OEE, timeUtilization, performanceUtilization, ppm],
        isCompare: false
      }
    };
  }

  // 对比模式数据请求
  return {
    data: {
      isCompare: true,
      categories: data1.categories,
      compare: data1.compare
    }
  };
};

// 修改数据加载回调
const handleDataLoaded = (data: any) => {
  // 普通模式数据处理
  if (!data.isCompare) {
    const [OEE, timeUtilization, performanceUtilization, ppm] = data.seriesData;

    chartOptions.value = {
      ...chartOptions.value,
      title: {
        ...chartOptions.value.title,
        subtext: `${t("common.compareList.averageOEE")}：${(OEE.reduce((a: number, b: number) => a + b, 0) / OEE.length).toFixed(1)}`
      },
      xAxis: {
        ...chartOptions.value.xAxis,
        data: data.categories
      },
      label: {
        show: true,
        position: "top",
        formatter: "{c}"
      },
      series: [
        {
          name: t("common.compareList.OEE"), // OEE
          type: "bar",
          data: OEE,
          itemStyle: { color: COLORS.OEE }
        },
        {
          name: t("common.compareList.timeUtilization"), // 时间稼动率
          type: "bar",
          data: timeUtilization,
          itemStyle: { color: COLORS.timeUtilization }
        },
        {
          name: t("common.compareList.performanceUtilization"), // 性能稼动率
          type: "bar",
          data: performanceUtilization,
          itemStyle: { color: COLORS.performanceUtilization }
        },
        {
          name: t("dashboard.centerCenter.ppm"), // PPM
          type: "line",
          yAxisIndex: 1,
          data: ppm,
          itemStyle: { color: COLORS.ppm },
          lineStyle: { color: COLORS.ppm },
          label: {
            show: true,
            position: "top",
            formatter: "{c}"
          }
        }
      ]
    };
  }
};
const userTable = ref<any>(null);

defineExpose({
  tableRef: userTable
});
// 添加 onMounted 钩子，页面加载时自动触发搜索
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
});
</script>
