<template>
  <div class="w-full h-[calc(100%-60px)] overflow-hidden">
    <div class="w-full h-[700px] flex overflow-hidden space-x-4">
      <!-- 第一列 -->
      <div class="w-1/3 flex flex-col space-y-4">
        <div class="h-[160px] rounded-lg shadow pt-0 pb-0">
          <item-wrap :title="$t('dashboard.overallEfficiency')">
            <left-top ref="overallEfficiency" :data="shitdata.综合效率[0]"></left-top>
          </item-wrap>
        </div>
        <div class="h-[340px] rounded-lg shadow pt-0 pb-0">
          <item-wrap :title="$t('dashboard.deviceStatus')">
            <left-center ref="deviceStatus" :data-source="shitdata.设备状态"></left-center>
          </item-wrap>
        </div>
        <div class="flex-1 rounded-lg shadow pt-0 pb-0">
          <item-wrap :title="$t('dashboard.energyConsumption')">
            <left-bottom ref="energyConsumption" :data="shitdata.能耗"></left-bottom>
          </item-wrap>
        </div>
      </div>

      <!-- 第二列 -->
      <div class="w-1/3 flex flex-col space-y-4">
        <div class="h-[160px] rounded-lg shadow pt-0 pb-0">
          <item-wrap :title="$t('dashboard.deviceEnvironmentalControl')">
            <center-top ref="deviceEnvironmentalControlInformation" :monitor-data="shitdata.设备环控信息"></center-top>
          </item-wrap>
        </div>
        <div class="flex-1 rounded-lg shadow pb-0 pt-0 flex justify-center">
          <center-center :production-data="shitdata.中间[0] || {}"></center-center>
        </div>
      </div>

      <!-- 第三列 -->
      <div class="w-1/3 flex flex-col space-y-4">
        <div class="h-[160px] rounded-lg shadow pb-0 pt-0">
          <item-wrap :title="$t('dashboard.rawMaterialWarning')">
            <right-top ref="rawMaterialWarning" :data="shitdata.原材料预警"></right-top>
          </item-wrap>
        </div>
        <div class="flex-1 rounded-lg shadow pb-0 pt-0">
          <item-wrap :title="$t('dashboard.preventiveMaintenanceWarning')">
            <right-center ref="preventiveMaintenanceWarning" :data="shitdata.预防性维护预警" :calendar-data="shitdata.预防性维护日历"></right-center>
          </item-wrap>
        </div>
      </div>
    </div>

    <!-- 下半部分 - 分成三列对齐 -->
    <div class="w-full h-[calc(100%-700px)] flex space-x-4">
      <div class="w-1/3 rounded-lg shadow pb-0 pt-0">
        <item-wrap :title="$t('dashboard.alarmInformation') + ' - 类型统计'">
          <right-bottom-left ref="alarmInformationLeft" :data="shitdata.告警信息"></right-bottom-left>
        </item-wrap>
      </div>
      <div class="w-1/3 rounded-lg shadow pb-0 pt-0">
        <item-wrap :title="$t('dashboard.alarmInformation') + ' - 次数统计'">
          <right-bottom-center ref="alarmInformationCenter" :data="shitdata.告警信息" :time-data="mockTimeData"></right-bottom-center>
        </item-wrap>
      </div>
      <div class="w-1/3 rounded-lg shadow pb-0 pt-0">
        <item-wrap :title="$t('dashboard.alarmInformation') + ' - 故障率'">
          <right-bottom-right ref="alarmInformationRight" :data="shitdata.告警信息" :alarm-rate-data="mockAlarmRateData"></right-bottom-right>
        </item-wrap>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from "vue";
import { productionReportCapacityApi } from "@/api";
import { exportMultipleTablesToExcel, transformChart } from "@/utils";
import moment from "moment";

// 导入组件
import LeftTop from "./LeftTop.vue";
import LeftCenter from "./LeftCenter.vue";
import LeftBottom from "./LeftBottom.vue";
import CenterTop from "./CenterTop.vue";
import CenterCenter from "./CenterCenter.vue";
import RightTop from "./RightTop.vue";
import RightCenter from "./RightCenter.vue";
import RightBottomLeft from "./RightBottomLeft.vue";
import RightBottomCenter from "./RightBottomCenter.vue";
import RightBottomRight from "./RightBottomRight.vue";
// import ItemWrap from "@/components/ItemWrap.vue";

const shitdata = reactive({
  综合效率: [],
  设备状态: [],
  能耗: [],
  告警信息: {},
  设备环控信息: [],
  中间: [],
  原材料预警: [
    {
      Control: "原材料预警",
      RawMaterialCount: 0,
      OverdueUnprocessedCount: 0,
      NearlyExpiredCount: 0
    }
  ],
  预防性维护预警: [
    {
      Control: "",
      Processed: 0,
      OverdueUnprocessed: 0,
      ExpiringSoon: 0,
      ProcessedCount: 0,
      OverdueUnprocessedCount: 0,
      DailyRequiredMaintenance: 0
    }
  ],
  预防性维护日历: []
});
// alarmRateData 模拟数据（包含 machine_name, fault_count, total_runs）
const mockAlarmRateData = [
  { machine_name: "机台A", fault_count: 5, total_runs: 100 },
  { machine_name: "机台B", fault_count: 12, total_runs: 200 },
  { machine_name: "机台C", fault_count: 8, total_runs: 150 },
  { machine_name: "机台D", fault_count: 3, total_runs: 50 },
  { machine_name: "机台E", fault_count: 20, total_runs: 300 }
];

// timeData 模拟数据（包含 machine_name, fault_count）
const mockTimeData = [
  { machine_name: "机台1", fault_count: 10, alarm_name: "气缸" },
  { machine_name: "机台2", fault_count: 8, alarm_name: "气缸" },
  { machine_name: "机台3", fault_count: 15, alarm_name: "气缸" },
  { machine_name: "机台4", fault_count: 3, alarm_name: "气缸" },
  { machine_name: "机台5", fault_count: 20, alarm_name: "气缸" }
];

// 定义一个变量来存储原始的 padding 值
let originalPadding: string;

const getListData = async () => {
  const query = {
    Type: -1
  };
  const { data }: any = await productionReportCapacityApi.getListMesReportData(query);
  shitdata.综合效率 = data.list[0];
  shitdata.能耗 = data.list[1];
  shitdata.设备状态 = data.list[2];
  shitdata.告警信息 = { 类型次数: data.list[3], 次数统计: data.list[4], 故障率: data.list[5] };
  shitdata.原材料预警 = data.list[6];
  shitdata.预防性维护预警 = data.list[7];
  shitdata.设备环控信息 = data.list[8];
  shitdata.中间 = data.list[9];
  shitdata.预防性维护日历 = data.list[10] || [];
};

const getListDataByFetch = async () => {
  try {
    const response = await fetch("/api/sys/mes/getListMesReportData?Type=-1", {
      method: "GET",
      headers: {
        "Content-Type": "application/json"
      }
    });

    if (!response.ok) {
      throw new Error("网络请求失败");
    }

    const { data } = await response.json();
    shitdata.综合效率 = data.list[0];
    shitdata.能耗 = data.list[1];
    shitdata.设备状态 = data.list[2];
    shitdata.告警信息 = { 类型次数: data.list[3], 次数统计: data.list[4], 故障率: data.list[5] };
    shitdata.原材料预警 = data.list[6];
    shitdata.预防性维护预警 = data.list[7];
    shitdata.设备环控信息 = data.list[8];
    shitdata.中间 = data.list[9];
    shitdata.预防性维护日历 = data.list[10] || [];
  } catch (error) {
    console.error("获取数据失败:", error);
  }
};

let timer: number | null = null;

onMounted(() => {
  const elMain = document.querySelector(".el-main") as HTMLElement;

  if (elMain) {
    const computedStyle = window.getComputedStyle(elMain);
    originalPadding = computedStyle.padding;
    elMain.style.padding = "0";
  }

  // 立即执行一次，使用原有API
  getListData();

  // 设置定时器，每10秒执行一次，使用fetch
  timer = window.setInterval(() => {
    getListDataByFetch();
  }, 15000);
});

onUnmounted(() => {
  const elMain = document.querySelector(".el-main") as HTMLElement;
  if (elMain && originalPadding) {
    elMain.style.padding = originalPadding;
  }

  // 清理定时器
  if (timer !== null) {
    clearInterval(timer);
    timer = null;
  }
});
const overallEfficiency = ref();
const deviceStatus = ref();
const energyConsumption = ref();
const alarmInformationLeft = ref();
const alarmInformationCenter = ref();
const alarmInformationRight = ref();
const deviceEnvironmentalControlInformation = ref();
const rawMaterialWarning = ref();
const preventiveMaintenanceWarning = ref();

const exportAll = () => {
  // console.log(transformChart(realTimeRef.value.chartRef, "机台"));
  // console.log(transformChart(stackRef.value.chartRef, "机台"));
  const 机台故障类型次数排序 = transformChart(alarmInformationLeft.value.typeChartRef, ["机台", "故障类型"], "次数");
  const 机台故障次数排序 = transformChart(alarmInformationCenter.value.countChartRef, "机台", "次数");
  const 机台故障率排序 = transformChart(alarmInformationRight.value.rateChartRef, "机台", "故障率");

  // 获取当前时间并格式化
  const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");

  // 将当前时间拼接到文件名中
  const fileName = `实时机台状态_${currentTime}.xlsx`;

  exportMultipleTablesToExcel(
    [机台故障类型次数排序, 机台故障次数排序, 机台故障率排序],
    ["机台故障类型次数排序", "机台故障次数排序", "机台故障率排序"],
    fileName
  );
  return "导出的数据";
};

defineExpose({
  exportAll
});
</script>

<style scoped>
.el-main {
  padding: 0;
}

/* 自定义样式 */
</style>
