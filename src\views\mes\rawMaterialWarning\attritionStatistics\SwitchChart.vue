<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'hideMachineSwitcher']"
    @data-loaded="handleDataLoaded"
    default-compare-id="production"
    :machines="machines"
    :init-params="initParams"
    ref="userTable"
  >
    <template #toolbar-right>
      <el-tooltip :content="$t('common.switchToTable')" placement="top">
        <el-button :icon="Switch" circle @click="changeTable" />
      </el-tooltip>
    </template>
  </ProChart>
</template>

<script setup lang="tsx">
// import moment from "moment";
import { productionReportCapacityApi } from "@/api";
import { Switch } from "@element-plus/icons-vue";
import moment from "moment";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const userTable = ref<any>(null);

const props = defineProps({
  currentTreeType: {
    type: String,
    default: "1" // 默认机台模式
  },
  initParams: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const machines = ref<any[]>([]);
const emit = defineEmits(["toggleView"]);
const changeTable = () => emit("toggleView");
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");
// 颜色配置（调整为处理状态相关）
const COLORS = {
  processed: "#409EFF", // 钢材颜色
  unprocessed: "#FF6B6B", // 木材颜色
  upcoming: "#67C23A", // 塑料颗粒颜色
  font: chartColor,
  splitLine: "#eee"
};

// 图表配置（核心调整部分）
const chartOptions = ref({
  title: {
    // subtext: "总木材数量：0",
    left: "center",
    textStyle: { fontSize: 16, color: COLORS.font }
  },
  toolbox: {
    show: true,
    feature: {
      magicType: { type: ["bar", "line"], title: { bar: "柱状图", line: "折线图" } }
    }
  },
  tooltip: {
    trigger: "axis",
    formatter: (params: any[]) => {
      return params
        .map(
          p => `
          <div style="display: flex; align-items: center; gap: 8px;">
            <span style="width: 10px; height: 10px; background: ${p.color}; border-radius: 2px; display: inline-block;"></span>
            ${p.seriesName}: ${p.data}
          </div>
        `
        )
        .join("<br>");
    }
  },
  legend: {
    data: [],
    top: 30
    // textStyle: { color: COLORS.font }
  },
  xAxis: {
    type: "category",
    data: []
    // name: props.currentTreeType === "0" ? "产线" : "机台"
    // axisLabel: { color: COLORS.font, rotate: 45, interval: 0 }
  },
  yAxis: [
    {
      type: "value",
      name: t("common.compareList.count"), //"数量",
      // axisLabel: { color: COLORS.unprocessed },
      splitLine: { lineStyle: { color: COLORS.splitLine } }
    }
  ],
  grid: { left: "3%", right: "3%", bottom: "3%", containLabel: true },
  series: []
});

// 数据转换函数（新增处理状态分组）
function transformData(dataList: any[]) {
  let groupField = "workline";
  if (props.currentTreeType == "1") {
    groupField = "workshop";
  } else if (props.currentTreeType == "2") {
    groupField = "workline";
  } else if (props.currentTreeType == "3") {
    groupField = "deck";
  } else {
    groupField = "deck";
  }

  const groupMap = new Map<string, Record<string, number>>();
  const materialNames = new Set<string>();

  dataList.forEach(item => {
    const key = item[groupField];
    const materialName = item.MaterialName;
    materialNames.add(materialName);

    if (!groupMap.has(key)) {
      groupMap.set(key, {});
    }
    const group = groupMap.get(key)!;
    group[materialName] = (group[materialName] || 0) + item.Count;
  });

  const categories = Array.from(groupMap.keys()).sort();
  const seriesData: number[][] = [];
  const legendData = Array.from(materialNames);

  legendData.forEach(materialName => {
    const data = categories.map(key => groupMap.get(key)?.[materialName] || 0);
    seriesData.push(data);
  });

  return {
    categories,
    seriesData,
    legendData,
    machines: categories.map(name => ({ id: name, name }))
  };
}

// 数据获取函数（调整模拟数据和状态映射）
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const fetchData = async (params: any) => {
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss"),
    EndDate: moment(params.time[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
  };
  const query = { ...params, ...time, Type: 19 };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);

  const { categories, seriesData, legendData, machines: machineList } = transformData(data.list);
  machines.value = machineList;

  return {
    data: {
      isCompare: false,
      categories,
      seriesData,
      legendData
    }
  };
};

// 数据加载回调（调整系列配置）
const handleDataLoaded = (data: any) => {
  const series = data.legendData.map((materialName: string, index: number) => ({
    name: materialName,
    type: "bar",
    data: data.seriesData[index],
    // itemStyle: { color: COLORS.processed },
    label: { show: true, position: "inside", formatter: "{c}" }
  }));

  chartOptions.value = {
    ...chartOptions.value,
    title: {
      ...chartOptions.value.title
      // subtext: `总木材数量：${unprocessedData.reduce((a, b) => a + b, 0)}`
    },
    xAxis: { ...chartOptions.value.xAxis, data: data.categories },
    legend: { ...chartOptions.value.legend, data: data.legendData },
    series
  };
};
</script>
