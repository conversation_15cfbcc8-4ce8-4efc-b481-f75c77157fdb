import moment from "moment";
// 表头配置
export const getTableColumns = (t: Function) => [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "id", label: t("deviceProtect.dailyProtect.table.id"), isShow: false },
  {
    prop: "time",
    label: t("deviceProtect.dailyProtect.table.time"),
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": t("deviceProtect.dailyProtect.table.startDate"),
        "end-placeholder": t("deviceProtect.dailyProtect.table.endDate")
      },
      defaultValue: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")]
    },
    isShow: false
  },
  {
    prop: "fact",
    label: t("deviceProtect.dailyProtect.form.factory"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "wshop",
    label: t("deviceProtect.dailyProtect.form.workshop"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "prodLine",
    label: t("deviceProtect.dailyProtect.form.productionLine"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "mach",
    label: t("deviceProtect.dailyProtect.form.machine"),
    search: {
      el: "select",
      props: {
        clearable: false,
        placeholder: t("deviceProtect.dailyProtect.form.selectMachine")
      },
      on: {
        change: (val: string) => {
          const selectedMachine = machineList.value.find(machine => machine.name === val);
          //   if (proTableRef.value) {
          proTableRef.value.searchParam.mach = val;
          proTableRef.value.searchParam.machCode = selectedMachine?.id || "";
          proTableRef.value.getTableList();
          //   }
        }
      }
    },
    enum: computed(() => machineList.value.map(m => ({ label: m.name, value: m.name })))
  },
  {
    prop: "machCode",
    label: t("deviceProtect.dailyProtect.form.machineCode"),
    search: {
      el: "select",
      props: {
        clearable: false,
        placeholder: t("deviceProtect.dailyProtect.form.selectMachineCode")
      },
      on: {
        change: (val: string) => {
          const selectedMachine = machineList.value.find(machine => machine.id === val);
          //   if (proTableRef.value) {
          proTableRef.value.searchParam.machCode = val;
          proTableRef.value.searchParam.mach = selectedMachine?.name || "";
          proTableRef.value.getTableList();
          //   }
        }
      }
    },
    enum: computed(() => machineList.value.map(m => ({ label: m.id, value: m.id })))
  },
  {
    prop: "wstation",
    label: t("deviceProtect.dailyProtect.form.workstation"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "wstationCode",
    label: t("deviceProtect.dailyProtect.form.workstationCode"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "mcOrgan",
    label: t("deviceProtect.dailyProtect.form.maintenanceOrg"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcItem",
    label: t("deviceProtect.dailyProtect.form.maintenanceItem"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcStandard",
    label: t("deviceProtect.dailyProtect.form.maintenanceStandard"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcMethod",
    label: t("deviceProtect.dailyProtect.form.maintenanceMethod"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcFrequency",
    label: t("deviceProtect.dailyProtect.form.maintenanceFrequency"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcStatus",
    label: t("deviceProtect.dailyProtect.form.maintenanceStatus"),
    // enum: mcStatusOptions,
    width: 120
  },
  {
    prop: "mcPerson",
    label: t("deviceProtect.dailyProtect.form.maintenancePerson"),
    width: 150,
    align: "left"
  },
  {
    prop: "mcTime",
    label: t("deviceProtect.dailyProtect.form.maintenanceTime"),
    width: 180,
    align: "center"
  },
  {
    prop: "remarks",
    label: t("deviceProtect.dailyProtect.form.remarks"),
    width: 200,
    align: "left"
  },
  { prop: "operation", label: t("deviceProtect.dailyProtect.table.operation"), width: 230, fixed: "right" }
];
// 弹窗字段配置
export const getFormItems = (t: Function) => [
  {
    prop: "fact",
    label: t("deviceProtect.dailyProtect.form.factory"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.factory"), trigger: "blur" }]
  },
  {
    prop: "wshop",
    label: t("deviceProtect.dailyProtect.form.workshop"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.workshop"), trigger: "blur" }]
  },
  {
    prop: "prodLine",
    label: t("deviceProtect.dailyProtect.form.productionLine"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.productionLine"), trigger: "blur" }]
  },
  {
    prop: "mach",
    label: t("deviceProtect.dailyProtect.form.machine"),
    component: "select",
    options: [],
    event: "change",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.machine"), trigger: "blur" }]
  },
  {
    prop: "machCode",
    label: t("deviceProtect.dailyProtect.form.machineCode"),
    component: "select",
    options: [],
    event: "change",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.machineCode"), trigger: "blur" }]
  },
  {
    prop: "wstation",
    label: t("deviceProtect.dailyProtect.form.workstation"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.workstation"), trigger: "blur" }]
  },
  {
    prop: "wstationCode",
    label: t("deviceProtect.dailyProtect.form.workstationCode"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.workstationCode"), trigger: "blur" }]
  },
  {
    prop: "mcOrgan",
    label: t("deviceProtect.dailyProtect.form.maintenanceOrg"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.maintenanceOrg"), trigger: "blur" }]
  },
  {
    prop: "mcItem",
    label: t("deviceProtect.dailyProtect.form.maintenanceItem"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.maintenanceItem"), trigger: "blur" }]
  },
  {
    prop: "mcStandard",
    label: t("deviceProtect.dailyProtect.form.maintenanceStandard"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.maintenanceStandard"), trigger: "blur" }]
  },
  {
    prop: "mcMethod",
    label: t("deviceProtect.dailyProtect.form.maintenanceMethod"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.maintenanceMethod"), trigger: "blur" }]
  },
  {
    prop: "mcFrequency",
    label: t("deviceProtect.dailyProtect.form.maintenanceFrequency"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.maintenanceFrequency"), trigger: "blur" }]
  },
  {
    prop: "mcStatus",
    label: t("deviceProtect.dailyProtect.form.maintenanceStatus"),
    component: "select",
    options: [],
    event: "change",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.maintenanceStatus"), trigger: "blur" }]
  },
  {
    prop: "mcPerson",
    label: t("deviceProtect.dailyProtect.form.maintenancePerson"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.maintenancePerson"), trigger: "blur" }]
  },
  {
    prop: "mcTime",
    label: t("deviceProtect.dailyProtect.form.maintenanceTime"),
    component: "date-picker",
    pickerType: "datetime",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.maintenanceTime"), trigger: "blur" }]
  },
  {
    prop: "remarks",
    label: t("deviceProtect.dailyProtect.form.remarks"),
    component: "input",
    rules: [{ required: true, message: t("deviceProtect.dailyProtect.rules.remarks"), trigger: "blur" }]
  }
];

// 表单初始值
export const getInitialFormData = () => ({
  number: 0,
  fact: "",
  wshop: "",
  prodLine: "",
  mach: "",
  machCode: "",
  wstation: "",
  wstationCode: "",
  mcOrgan: "",
  mcItem: "",
  mcStandard: "",
  mcMethod: "",
  mcFrequency: "",
  mcStatus: "",
  mcPerson: "",
  mcTime: null,
  remarks: ""
});
