<template>
  <div class="table-box">
    <ProTable ref="proTable" title="电池电芯列表" :columns="columns" :request-api="batteryCellApi.getList">
      <template #toolButton>
        <el-button type="primary" @click="exportData">导出数据</el-button>
        <el-button type="primary" @click="showBatchQueryDialog">批量查询</el-button>
      </template>

      <!-- 状态列 -->
      <template #status="scope">
        <el-tag
          :type="
            scope.row.status === '在线'
              ? 'success'
              : scope.row.status === '完成'
                ? 'success'
                : scope.row.status === '已过站'
                  ? 'info'
                  : scope.row.status === 'NG'
                    ? 'danger'
                    : 'info'
          "
        >
          {{ scope.row.status }}
        </el-tag>
      </template>

      <!-- 操作列 -->
      <template #operation="scope">
        <el-space>
          <s-button :opt="FormOptEnum.VIEW" type="primary" link @click="onDetail(scope.row)">详情</s-button>
        </el-space>
      </template>
    </ProTable>

    <!-- 批量查询对话框 -->
    <el-dialog v-model="batchQueryVisible" title="批量查询电芯码" width="50%" destroy-on-close>
      <el-form :model="batchQueryForm">
        <el-form-item label="电芯码列表" label-width="100px">
          <el-input v-model="batchQueryForm.batteryCodes" type="textarea" :rows="10" placeholder="请输入电芯码，每行一个，或用逗号分隔"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchQueryVisible = false">取消</el-button>
          <el-button type="primary" @click="doBatchQuery">查询</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 工序详情弹窗 -->
    <el-dialog v-model="detailVisible" title="工序详情" width="80%" destroy-on-close>
      <el-tabs v-model="activeTab" type="card">
        <!-- 批次详情标签页 -->
        <el-tab-pane label="批次详情" name="batchInfo">
          <div class="detail-info">
            <el-form :model="batchInfo" label-width="100px" label-position="right" class="detail-form">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="批次号">
                    <span>{{ batchInfo.batchNo }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="产品名称">
                    <span>{{ batchInfo.productName }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="工单号">
                    <span>{{ batchInfo.workOrderNo }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="源工单号">
                    <span>{{ batchInfo.sourceWorkOrderNo }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备编码">
                    <span>{{ batchInfo.deviceCode }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备名称">
                    <span>{{ batchInfo.deviceName }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

            <!-- 物料清单表格 -->
            <div class="detail-table">
              <div class="tab-header">
                <div class="tab-title">物料清单</div>
              </div>
              <el-table :data="materialList" border style="width: 100%" max-height="400">
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="materialBatchNo" label="物料批次号" width="150" />
                <el-table-column prop="materialCode" label="物料编码" width="150" />
                <el-table-column prop="materialName" label="物料名称" width="200" />
                <el-table-column prop="actualUsage" label="实际用量" width="120" />
                <el-table-column prop="unit" label="单位" width="80" />
                <el-table-column prop="supplierCode" label="供应商编码" width="150" />
                <el-table-column prop="supplierName" label="供应商名称" width="200" />
                <el-table-column prop="uploader" label="上传人" width="120" />
                <el-table-column prop="creator" label="创建人" width="120" />
                <el-table-column prop="createTime" label="创建时间" width="180" />
              </el-table>

              <!-- 分页 -->
              <div class="pagination-container">
                <span>共 {{ materialList.length }} 条</span>
                <el-pagination background layout="prev, pager, next" :total="materialList.length" :page-size="10" />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 过程参数标签页 -->
        <el-tab-pane label="过程参数" name="processParams">
          <div class="process-params">
            <el-form :inline="true" class="search-form">
              <el-form-item label="采集时间">
                <el-date-picker
                  v-model="collectTimeRange"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  @change="onDateRangeChange"
                />
              </el-form-item>
              <el-form-item label="参数">
                <el-select v-model="selectedParam" placeholder="请选择" clearable style="width: 200px">
                  <el-option label="请选择" value="" />
                  <el-option label="模切速度" value="dieCuttingSpeedA" />
                  <el-option label="激光频率" value="laserFrequencyA" />
                  <el-option label="激光功率" value="polePlateDefect" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="onQueryProcessParams">查询</el-button>
                <el-button @click="onResetProcessParams">重置</el-button>
                <el-button type="primary" @click="exportProcessParams">导出参数</el-button>
              </el-form-item>
            </el-form>

            <div class="params-table">
              <el-table :data="processList" border style="width: 100%" max-height="500" @row-click="onRowClick">
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="collectTime" label="采集时间" width="180" />
                <el-table-column prop="paramCode" label="参数编码" width="150" />
                <el-table-column prop="paramName" label="参数名称" width="200" />
                <el-table-column prop="paramValue" label="参数值" />
              </el-table>

              <!-- 分页 -->
              <div class="pagination-container">
                <span>共 {{ processList.length }} 条</span>
                <el-pagination background layout="prev, pager, next" :total="processList.length" :page-size="10" />
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 参数图表弹窗 -->
    <el-dialog v-model="chartVisible" :title="`${currentParam.paramName || '参数'}趋势图`" width="70%" destroy-on-close>
      <div class="chart-container">
        <v-chart :autoresize="true" :option="chartOptions" class="param-chart" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx" name="batteryCell">
import { ref } from "vue";
import { ColumnProps, ProTableInstance } from "@/components/ProTable/interface";
import { FormOptEnum } from "@/enums";
import * as XLSX from "xlsx";
import { ElMessage } from "element-plus";
// 导入 v-chart 组件，具体路径可能需要根据实际项目结构调整
// import VChart from 'vue-echarts';

// 定义历史数据项的接口
interface HistoryItem {
  date: string;
  value: number;
}

// 详情弹窗相关数据
const detailVisible = ref(false);
const currentRow = ref<any>(null);
const activeTab = ref("batchInfo");
const batchInfo = ref<any>({});
const materialList = ref<any[]>([]);
const processList = ref<any[]>([]);
const collectTimeRange = ref<string[]>([]);
const selectedParam = ref("");

// 批量查询相关数据
const batchQueryVisible = ref(false);
const batchQueryForm = ref({
  batteryCodes: ""
});

// 导出相关数据
const exportParam = ref({
  pageSize: 10000 // 导出最大数量
});

// 过程参数相关函数
const onDateRangeChange = () => {
  // 日期范围变化时的处理逻辑
};

const onQueryProcessParams = () => {
  // 查询过程参数
  fetchProcessParams();
};

const onResetProcessParams = () => {
  // 重置过程参数查询条件
  collectTimeRange.value = [];
  selectedParam.value = "";
};

// API接口，需要根据实际情况创建
const batteryCellApi = {
  getList: () => {
    // 这里应该调用真实API，这里暂时模拟返回数据
    return Promise.resolve({
      code: 200,
      data: {
        list: [
          {
            id: "1",
            batteryCode: "0TICBCTDFLB121FA00020061",
            modelBatteryCode: "710003102525012120851111",
            productCode: "2001100001",
            productName: "一次容量平板型电池-162Ah",
            workOrderNumber: "12000000821",
            flowWorkOrderNumber: "12000000821-20240507-L1-1-1-62-Z1Z1",
            status: "完成",
            nextProcessCode: "Z121",
            nextProcessName: "X-ray",
            currentProcessName: "OCV3 工序",
            productionLineCode: "YIN-JH-M01-L2-Z01",
            productionLineName: "一次容量线-hipo3",
            workStationCode: "YIN-JH-M01-L2-Z01-DQZ1-E01",
            workStationName: "喂料L2",
            deviceCode: "YIN-JH-M01-L2-Z01-DQZ1-E01"
          }
        ],
        total: 1
      },
      msg: "操作成功"
    });
  },

  // 获取批次详情
  getBatchInfo: () => {
    // 实际应调用真实API，这里模拟返回数据
    return Promise.resolve({
      code: 200,
      data: {
        batchNo: "C05025051721121R",
        productName: "正极模切后极卷-162Ah",
        workOrderNo: "120000000847",
        sourceWorkOrderNo: "120000000847-20250517-L2-C050",
        deviceCode: "JHM01E08104-00",
        deviceName: "正极激光模切机-L2-2",
        materialList: [
          {
            materialBatchNo: "C04025051721212R",
            materialCode: "20010700001",
            materialName: "正极银压后极卷-162Ah",
            actualUsage: 1274.64,
            unit: "M",
            supplierCode: "-",
            supplierName: "-",
            uploader: "admin[admin]",
            creator: "[15601548725]陈一龙",
            createTime: "2025-05-19 03:36:34"
          }
        ]
      },
      msg: "操作成功"
    });
  },

  // 获取过程参数
  getProcessParams: () => {
    // 实际应调用真实API，这里模拟返回数据
    return Promise.resolve({
      code: 200,
      data: [
        {
          collectTime: "2025-05-19 03:36:23",
          paramCode: "dieCuttingSpeedA",
          paramName: "模切速度(m/min)",
          paramValue: 40.03056
        },
        {
          collectTime: "2025-05-19 03:36:23",
          paramCode: "laserFrequencyA",
          paramName: "激光频率(HZ)",
          paramValue: 0
        },
        {
          collectTime: "2025-05-19 03:36:23",
          paramCode: "polePlateDefect",
          paramName: "激光功率",
          paramValue: 0
        },
        {
          collectTime: "2025-05-19 03:36:23",
          paramCode: "laserFrequency",
          paramName: "2号硒吸负压管道风速",
          paramValue: 16.87315
        },
        {
          collectTime: "2025-05-19 03:36:23",
          paramCode: "laserEnergy",
          paramName: "1号硒吸负压管道风速",
          paramValue: 17.2578
        },
        {
          collectTime: "2025-05-19 03:36:23",
          paramCode: "bladeSpeed",
          paramName: "工业尘机给合主管道风速",
          paramValue: 20.6045
        },
        {
          collectTime: "2025-05-19 03:36:23",
          paramCode: "stiffenerPressure",
          paramName: "收卷张力(N)",
          paramValue: 159.6891
        }
      ],
      msg: "操作成功"
    });
  },

  // 获取参数历史数据
  getParamHistory: paramCode => {
    // 实际应调用真实API，这里模拟返回数据
    // 生成随机历史数据
    const standardValue = {
      dieCuttingSpeedA: 40,
      laserFrequencyA: 5,
      polePlateDefect: 0,
      laserFrequency: 17,
      laserEnergy: 18,
      bladeSpeed: 20,
      stiffenerPressure: 160
    };

    const generateData = (standard, count = 30) => {
      const now = new Date();
      const data: HistoryItem[] = [];
      for (let i = 0; i < count; i++) {
        const date = new Date(now.getTime() - (count - i) * 60 * 60 * 1000);
        const dateStr = date.toISOString().split("T")[0] + " " + date.toTimeString().split(" ")[0].substr(0, 5);
        // 值围绕标准值上下浮动10%
        const value = standard + (Math.random() * 2 - 1) * standard * 0.1;
        data.push({
          date: dateStr,
          value: parseFloat(value.toFixed(4))
        });
      }
      return data;
    };

    const standard = standardValue[paramCode] || 0;
    const historyData = generateData(standard);

    return Promise.resolve({
      code: 200,
      data: {
        standardValue: standard,
        history: historyData
      },
      msg: "操作成功"
    });
  }
};

// 获取过程参数
const fetchProcessParams = () => {
  // const params = {
  //   batchNo: currentRow.value?.batchNo || "",
  //   startDate: collectTimeRange.value?.[0] || "",
  //   endDate: collectTimeRange.value?.[1] || "",
  //   paramCode: selectedParam.value || ""
  // };

  batteryCellApi.getProcessParams().then(res => {
    if (res.code === 200) {
      processList.value = res.data || [];
    }
  });
};

// 状态选项
const statusOptions = [
  { label: "NG", value: "NG" },
  { label: "完成", value: "完成" },
  { label: "已过站", value: "已过站" }
];

// 工序选项
const processTypeOptions = [
  { label: "X-RAY 工序", value: "X-RAY 工序" },
  { label: "超声波焊接工序", value: "超声波焊接工序" },
  { label: "负极极耳激光焊接工序", value: "负极极耳激光焊接工序" },
  { label: "包 mylar - 入壳工序", value: "包 mylar - 入壳工序" },
  { label: "P-A10", value: "P-A10" },
  { label: "入壳预煌工序", value: "入壳预煌工序" },
  { label: "正极极耳激光焊接工序", value: "正极极耳激光焊接工序" },
  { label: "正极压装预焊工序", value: "正极压装预焊工序" },
  { label: "周边焊接工序", value: "周边焊接工序" },
  { label: "一次氦检 & hipot3 工序", value: "一次氦检 & hipot3 工序" },
  { label: "烘烤工序", value: "烘烤工序" },
  { label: "一次注液 & hipot4 工序", value: "一次注液 & hipot4 工序" },
  { label: "高温漫润工序", value: "高温漫润工序" },
  { label: "OCVO 工序", value: "OCVO 工序" },
  { label: "化成工序", value: "化成工序" },
  { label: "二次注液工序", value: "二次注液工序" },
  { label: "高温老化工序", value: "高温老化工序" },
  { label: "常温老化", value: "常温老化" },
  { label: "回氮插钉 & 厚度测量", value: "回氮插钉 & 厚度测量" },
  { label: "密封钉焊接工序", value: "密封钉焊接工序" },
  { label: "気检工序", value: "気检工序" },
  { label: "分容工序", value: "分容工序" },
  { label: "常温静量 1 工序", value: "常温静量 1 工序" },
  { label: "OCV1 工序", value: "OCV1 工序" },
  { label: "常温静量 2 工序", value: "常温静量 2 工序" },
  { label: "(组...) JHICP-A10", value: "(组...) JHICP-A10" },
  { label: "OCV2 工序", value: "OCV2 工序" },
  { label: "DCIR 工序", value: "DCIR 工序" },
  { label: "分档工序", value: "分档工序" },
  { label: "OCV3 工序", value: "OCV3 工序" },
  { label: "入库工序", value: "入库工序" },
  { label: "(组...) JHICP", value: "(组...) JHICP" },
  { label: "OCV4 工序", value: "OCV4 工序" },
  { label: "包膜工序", value: "包膜工序" },
  { label: "切叠工序", value: "切叠工序" }
];

// 产品选项（模拟数据，实际应从API获取）
const productOptions = [{ label: "配送锂电芯-16DAh", value: "2001140001" }];

// 流程选项（模拟数据，实际应从API获取）
const processOptions = [
  { label: "流程1", value: "process1" },
  { label: "流程2", value: "process2" }
];

// 工单号选项（模拟数据）
const workOrderOptions = [{ label: "12000000821", value: "12000000821" }];

// 流水工单号选项（模拟数据）
const flowWorkOrderOptions = [{ label: "12000000821-20240507-L1-1-1-62-Z1Z1", value: "12000000821-20240507-L1-1-1-62-Z1Z1" }];

// 产线选项（模拟数据）
const productionLineOptions = [{ label: "一次容量线-hipo3", value: "YIN-JH-M01-L2-Z01" }];

// 批次选项（模拟数据）
const batchOptions = [
  { label: "批次1", value: "batch1" },
  { label: "批次2", value: "batch2" }
];

// 设备选项（模拟数据）
const deviceOptions = [
  { label: "设备1", value: "device1" },
  { label: "设备2", value: "device2" }
];

// 获取 ProTable 元素，调用其获取刷新数据方法
const proTable = ref<ProTableInstance>();

// 表格配置项
const columns: ColumnProps[] = [
  { type: "index", label: "序号", width: 60, fixed: "left" },
  {
    prop: "batteryCode",
    label: "电芯码",
    width: 180,
    search: {
      el: "input",
      props: { placeholder: "请输入" }
    }
  },
  {
    prop: "modelBatteryCode",
    label: "裸电芯码",
    width: 180,
    search: {
      el: "input",
      props: { placeholder: "请输入" }
    }
  },
  { prop: "productCode", label: "产品编码", width: 120 },
  { prop: "productName", label: "产品名称", width: 150 },
  {
    prop: "currentProcessName",
    label: "当前工序名称",
    width: 150,
    search: {
      el: "select",
      props: {
        placeholder: "请选择工序",
        options: processTypeOptions,
        filterable: true,
        clearable: true
      }
    }
  },
  {
    prop: "workOrderNumber",
    label: "工单号",
    width: 150,
    search: {
      el: "select",
      props: {
        placeholder: "请选择",
        options: workOrderOptions
      }
    }
  },
  {
    prop: "flowWorkOrderNumber",
    label: "流水工单号",
    width: 280,
    search: {
      el: "select",
      props: {
        placeholder: "请选择",
        options: flowWorkOrderOptions
      }
    }
  },
  {
    prop: "product",
    label: "产品",
    width: 150,
    search: {
      el: "select",
      props: {
        placeholder: "请选择",
        options: productOptions
      }
    },
    isShow: false // 仅用于搜索，表格中不显示
  },
  {
    prop: "productionLine",
    label: "产线",
    width: 150,
    search: {
      el: "select",
      props: {
        placeholder: "请选择",
        options: productionLineOptions
      }
    },
    isShow: false // 仅用于搜索，表格中不显示
  },
  {
    prop: "batch",
    label: "批次",
    width: 150,
    search: {
      el: "select",
      props: {
        placeholder: "请选择",
        options: batchOptions
      }
    },
    isShow: false // 仅用于搜索，表格中不显示
  },
  {
    prop: "process",
    label: "流程",
    width: 150,
    search: {
      el: "select",
      props: {
        placeholder: "请选择",
        options: processOptions
      }
    },
    isShow: false // 仅用于搜索，表格中不显示
  },
  {
    prop: "device",
    label: "设备",
    width: 150,
    search: {
      el: "select",
      props: {
        placeholder: "请选择",
        options: deviceOptions
      }
    },
    isShow: false // 仅用于搜索，表格中不显示
  },
  {
    prop: "checkInTime",
    label: "进站时间",
    width: 180,
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        rangeSeparator: "至",
        startPlaceholder: "开始日期",
        endPlaceholder: "结束日期"
      }
    },
    isShow: false // 仅用于搜索，表格中不显示
  },
  {
    prop: "checkOutTime",
    label: "出站时间",
    width: 180,
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        rangeSeparator: "至",
        startPlaceholder: "开始日期",
        endPlaceholder: "结束日期"
      }
    },
    isShow: false // 仅用于搜索，表格中不显示
  },
  {
    prop: "employeeCode",
    label: "老员编码",
    width: 150,
    search: {
      el: "input",
      props: { placeholder: "请输入" }
    },
    isShow: false // 仅用于搜索，表格中不显示
  },
  {
    prop: "status",
    label: "状态",
    width: 100,
    search: {
      el: "select",
      props: {
        placeholder: "请选择",
        options: statusOptions
      }
    }
  },
  { prop: "nextProcessCode", label: "下一工序编码", width: 120 },
  { prop: "nextProcessName", label: "下一工序名称", width: 150 },
  { prop: "productionLineCode", label: "产线编码", width: 180 },
  { prop: "productionLineName", label: "产线名称", width: 100 },
  { prop: "workStationCode", label: "工位编码", width: 180 },
  { prop: "workStationName", label: "工位名称", width: 100 },
  { prop: "deviceCode", label: "设备编码", width: 180 },
  { prop: "operation", label: "操作", width: 120, fixed: "right" }
];

/**
 * 查看详情
 * @param row 行数据
 */
function onDetail(row: any) {
  console.log("查看详情", row);
  currentRow.value = row;
  detailVisible.value = true;

  // 获取批次详情
  batteryCellApi.getBatchInfo().then(res => {
    if (res.code === 200) {
      const data = res.data || {};
      batchInfo.value = {
        batchNo: data.batchNo || row.batteryCode,
        productName: data.productName || row.productName,
        workOrderNo: data.workOrderNo || row.workOrderNumber,
        sourceWorkOrderNo: data.sourceWorkOrderNo || row.flowWorkOrderNumber,
        deviceCode: data.deviceCode || row.deviceCode,
        deviceName: data.deviceName || row.deviceName
      };
      materialList.value = data.materialList || [];
    }
  });

  // 获取过程参数
  fetchProcessParams();
}

// 参数图表相关数据
const chartVisible = ref(false);
const currentParam = ref<any>({});
const chartOptions = ref<any>({});

// 参数图表相关函数
const onRowClick = (row: any) => {
  currentParam.value = row;
  chartVisible.value = true;

  // 获取参数历史数据
  batteryCellApi.getParamHistory(row.paramCode).then(res => {
    if (res.code === 200) {
      const { standardValue, history } = res.data;

      // 生成时间和值数组
      const dates = history.map((item: HistoryItem) => item.date);
      const values = history.map((item: HistoryItem) => item.value);

      // 生成图表选项
      chartOptions.value = {
        title: {
          text: `${row.paramName || "参数"}趋势图`,
          left: "center"
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            const valueItem = params[0];
            const standardItem = params[1];
            return `
              <div>时间: ${valueItem.axisValue}</div>
              <div>${row.paramName}: ${valueItem.value}</div>
              <div>标准值: ${standardItem.value}</div>
              <div>偏差: ${(valueItem.value - standardItem.value).toFixed(4)}</div>
              <div>偏差率: ${(((valueItem.value - standardItem.value) / standardItem.value) * 100).toFixed(2)}%</div>
            `;
          }
        },
        legend: {
          data: ["参数值", "标准值"],
          top: "bottom"
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "10%",
          top: "15%",
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: dates,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: "value",
          name: row.paramName,
          axisLine: {
            show: true
          },
          splitLine: {
            show: true
          }
        },
        series: [
          {
            name: "参数值",
            type: "line",
            data: values,
            smooth: true,
            markLine: {
              silent: true,
              symbol: "none",
              data: [
                {
                  name: "标准线",
                  yAxis: standardValue,
                  lineStyle: {
                    color: "#FF0000",
                    type: "dashed"
                  },
                  label: {
                    show: true,
                    formatter: "标准值: {c}"
                  }
                }
              ]
            },
            lineStyle: {
              color: "#409EFF"
            },
            itemStyle: {
              color: "#409EFF"
            }
          },
          {
            name: "标准值",
            type: "line",
            data: dates.map(() => standardValue),
            lineStyle: {
              color: "#FF0000",
              type: "dashed"
            },
            itemStyle: {
              color: "#FF0000"
            }
          }
        ],

        toolbox: {
          feature: {
            dataZoom: {},
            restore: {},
            saveAsImage: {}
          }
        }
      };
    }
  });
};

// 批量查询相关函数
const showBatchQueryDialog = () => {
  batchQueryVisible.value = true;
  batchQueryForm.value.batteryCodes = "";
};

const doBatchQuery = () => {
  // 处理输入的电芯码，支持换行和逗号分隔
  const batteryCodes = batchQueryForm.value.batteryCodes
    .replace(/\n/g, ",") // 将换行替换为逗号
    .split(",") // 按逗号分割
    .map(code => code.trim()) // 去除空格
    .filter(code => code); // 过滤空值

  if (batteryCodes.length === 0) {
    ElMessage({
      message: "请输入至少一个电芯码",
      type: "warning"
    });
    return;
  }

  // 关闭对话框
  batchQueryVisible.value = false;

  // 设置查询参数并刷新表格
  if (proTable.value) {
    proTable.value.searchParam.batteryCode = batteryCodes.join(",");
    proTable.value.search();
    ElMessage({
      message: `批量查询 ${batteryCodes.length} 个电芯码`,
      type: "success"
    });
  }
};

// 导出数据函数
const exportData = async () => {
  try {
    // 获取当前查询参数
    const params = {
      ...proTable.value?.searchParam,
      page: 1,
      pageSize: exportParam.value.pageSize
    };

    // 调用API获取数据
    const response = await batteryCellApi.getList(params);
    if (response.code !== 200) {
      ElMessage({
        message: response.msg || "导出失败",
        type: "error"
      });
      return;
    }

    const tableData = response.data.list || [];
    if (tableData.length === 0) {
      ElMessage({
        message: "没有数据可导出",
        type: "warning"
      });
      return;
    }

    // 过滤要导出的列（排除选择框、操作列等）
    const exportColumns = columns.filter(col => !["selection", "operation", "id"].includes(col.type || col.prop || ""));

    // 准备表头
    const headers = exportColumns.map(col => col.label || "");

    // 准备数据行
    const dataRows = tableData.map(item => {
      return exportColumns.map(col => {
        const prop = col.prop || "";
        // 处理特殊字段
        if (prop === "status") {
          return item[prop] || "";
        }
        return item[prop] || "";
      });
    });

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...dataRows]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "电池电芯数据");

    // 生成文件名（包含当前日期）
    const exportDate = new Date().toISOString().slice(0, 10);
    XLSX.writeFile(workbook, `电池电芯数据_${exportDate}.xlsx`);

    ElMessage({
      message: "导出成功",
      type: "success"
    });
  } catch (err) {
    const errorMsg = err instanceof Error ? err.message : "未知错误";
    ElMessage({
      message: `导出失败: ${errorMsg}`,
      type: "error"
    });
  }
};

// 导出过程参数函数
const exportProcessParams = () => {
  try {
    if (processList.value.length === 0) {
      ElMessage({
        message: "没有数据可导出",
        type: "warning"
      });
      return;
    }

    // 准备表头
    const headers = ["序号", "采集时间", "参数编码", "参数名称", "参数值"];

    // 准备数据行
    const dataRows = processList.value.map((item, index) => {
      return [index + 1, item.collectTime || "", item.paramCode || "", item.paramName || "", item.paramValue || ""];
    });

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...dataRows]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "过程参数数据");

    // 获取当前电芯码
    const batteryCode = currentRow.value?.batteryCode || "未知电芯";

    // 生成文件名（包含当前日期和电芯码）
    const exportDate = new Date().toISOString().slice(0, 10);
    XLSX.writeFile(workbook, `${batteryCode}_过程参数_${exportDate}.xlsx`);

    ElMessage({
      message: "导出成功",
      type: "success"
    });
  } catch (err) {
    const errorMsg = err instanceof Error ? err.message : "未知错误";
    ElMessage({
      message: `导出失败: ${errorMsg}`,
      type: "error"
    });
  }
};
</script>

<style lang="scss" scoped>
.table-box {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

// 详情弹窗样式
.detail-info {
  padding: 16px;
  .detail-form {
    padding: 16px;
    margin-bottom: 24px;
    background-color: #f9f9f9;
    border-radius: 4px;
    :deep(.el-form-item__label) {
      font-weight: bold;
      color: #606266;
    }
    :deep(.el-form-item__content) {
      color: #303133;
    }
  }
  .detail-table {
    .tab-header {
      padding-bottom: 12px;
      margin-bottom: 16px;
      border-bottom: 1px solid #ebeef5;
      .tab-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
      }
    }
    .pagination-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 16px;
    }
  }
}

// 过程参数样式
.process-params {
  padding: 16px;
  .search-form {
    padding: 16px;
    margin-bottom: 20px;
    background-color: #f9f9f9;
    border-radius: 4px;
    :deep(.el-form-item) {
      margin-right: 16px;
      margin-bottom: 0;
    }
    :deep(.el-form-item__label) {
      font-weight: bold;
      color: #606266;
    }
  }
  .params-table {
    .pagination-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 16px;
    }
  }
}

// 参数图表样式
.chart-container {
  height: 400px;
  padding: 10px;
  background-color: #ffffff;
  border-radius: 4px;
}
.param-chart {
  width: 100%;
  height: 100%;
}
</style>
