<template>
  <v-chart :autoresize="true" :option="options" class="chart card" ref="chartRef" />
</template>

<script setup lang="ts">
import { ref, defineProps, computed } from "vue";
import { transformChart, exportMultipleTablesToExcel } from "@/utils";
import moment from "moment";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
  stackData: {
    type: Object,
    default: () => ({
      xAxisData: [],
      seriesData: [
        { name: "生产中", data: [320, 302, 301] },
        { name: "待料中", data: [120, 132, 101] },
        { name: "故障修机", data: [220, 182, 191] }
      ]
    })
  }
});

const chartRef = ref();

const exportExcel = () => {
  const arr2 = transformChart(chartRef.value, t("statusMonitoring.currentStatus.machine"), "/分钟");
  const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");
  const fileName = `${t("statusMonitoring.currentStatus.machineStatus")}_${currentTime}.xlsx`;
  exportMultipleTablesToExcel([arr2], [t("statusMonitoring.currentStatus.machineStatus")], fileName);
};

const generateOptions = (stackData: any) => {
  // 获取全局字体颜色
  const root = document.documentElement;
  const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular").trim();

  const totals = stackData.xAxisData.map((_, index) => stackData.seriesData.reduce((sum, series) => sum + series.data[index], 0));

  return {
    title: {
      text: t("statusMonitoring.currentStatus.stack.title"),
      left: "center",
      textStyle: { color: chartColor } // 标题颜色
    },
    toolbox: {
      show: true,
      feature: {
        magicType: {
          type: ["line", "bar"],
          title: {
            line: t("statusMonitoring.currentStatus.stack.switchToLine"),
            bar: t("statusMonitoring.currentStatus.stack.switchToBar")
          }
        },
        saveAsImage: { show: true },
        myExcelExport: {
          show: true,
          title: t("statusMonitoring.currentStatus.stack.exportExcel"),
          icon: "path://M665.38 65.024c11.48 0 22.53 4.46 30.72 12.58l0.44 0.37 152.65 153.6c8.05 8.12 12.65 19.02 12.8 30.43v250.51a13.75 13.75 0 0 1-13.68 13.75h-28.6a13.75 13.75 0 0 1-13.75-13.75v-245.03L660.48 121.05H216.94v199.02h269.24c7.61 0 13.75 6.14 13.75 13.75v420.57a13.68 13.68 0 0 1-13.75 13.68H217.01v136.05h589.02v-24.72c0-7.61 6.14-13.75 13.68-13.75h28.53c7.61 0 13.75 6.14 13.75 13.75v44.62a35.99 35.99 0 0 1-35.33 36.06h-629.76a35.99 35.99 0 0 1-35.84-35.4V768h-83.38a13.68 13.68 0 0 1-13.68-13.75v-420.57c0-7.53 6.14-13.68 13.75-13.68H160.91V101.01c0-19.68 15.73-35.69 35.33-35.99h469.07zM361.33 437.98a54.86 54.86 0 0 0-42.13 19.53l-37.3 44.47-37.3-44.4a54.86 54.86 0 0 0-41.98-19.6h-30.13a6.88 6.88 0 0 0-5.27 11.26l79.51 94.72-79.51 94.72a6.88 6.88 0 0 0 5.27 11.26h30.28a54.86 54.86 0 0 0 41.98-19.6l37.16-44.32 37.3 44.32a54.86 54.86 0 0 0 41.98 19.6h30.21c5.85 0 9-6.8 5.27-11.26L317.22 543.96l79.51-94.72a6.88 6.88 0 0 0-5.19-11.26zm214.6-104.3c0-7.53 6.14-13.68 13.75-13.68h164.57c7.53 0 13.68 6.14 13.68 13.75v28.53a13.68 13.68 0 0 1-13.75 13.75h-164.57a13.68 13.68 0 0 1-13.68-13.75v-28.53zm13.75 102.33a13.68 13.68 0 0 0-13.75 13.68v28.6c0 7.61 6.14 13.75 13.75 13.75h164.57a13.68 13.68 0 0 0 13.68-13.75v-28.53a13.68 13.68 0 0 0-13.75-13.75h-164.57zm192 348.23a21.21 21.21 0 0 0-8.19 16.82v48.64c0 4.39 5.12 6.88 8.56 4.17l168.67-130.78a24.5 24.5 0 0 0 0-38.62L782.19 553.69a5.27 5.27 0 0 0-8.56 4.24v48.71c0 6.58 3 12.73 8.19 16.82l68.53 53.03H617.25a10.61 10.61 0 0 0-10.53 10.61v33.94c0 5.85 4.68 10.61 10.53 10.61h232.45l-67.95 52.66z",
          onclick: exportExcel
        }
      }
    },
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "cross", crossStyle: { color: "#999" } },
      textStyle: { color: chartColor } // 提示框文本颜色
    },
    legend: {
      data: [...stackData.seriesData.map(s => s.name), t("statusMonitoring.currentStatus.stack.totalDuration")],
      bottom: 10,
      textStyle: { color: chartColor } // 图例文本颜色
    },
    xAxis: {
      type: "category",
      data: stackData.xAxisData,
      axisPointer: { type: "shadow" },
      axisLabel: { color: chartColor } // x轴标签颜色
    },
    yAxis: {
      type: "value",
      name: t("statusMonitoring.currentStatus.stack.unit"),
      nameTextStyle: { color: chartColor }, // y轴名称颜色
      axisLabel: { color: chartColor } // y轴标签颜色（数值）
    },
    series: [
      // 原始堆叠系列（内部标签）
      ...stackData.seriesData.map(item => ({
        ...item,
        type: "bar",
        stack: "总量",
        label: {
          show: true,
          position: "inside",
          formatter: "{c}",
          color: chartColor // 内部标签颜色
        }
      })),
      {
        name: t("statusMonitoring.currentStatus.stack.totalDuration"),
        type: "bar",
        barGap: "-100%",
        label: {
          normal: {
            show: true,
            position: "top",
            formatter: "{c}",
            textStyle: { color: chartColor } // 总时长标签颜色
          }
        },
        itemStyle: { normal: { color: "rgba(128, 128, 128, 0)" } },
        data: totals
      }
    ]
  };
};

const options = computed(() => generateOptions(props.stackData));

defineExpose({ chartRef });
</script>

<style scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
