<script setup lang="ts">
import ItemWrap from "@/components/item-wrap";
import LeftTop from "./left-top.vue";
import LeftCenter from "./left-center.vue";
import LeftBottom from "./left-bottom.vue";
// import CenterMap from "./center-map.vue";
import CenterBottom from "./center-bottom.vue";
import RightTop from "./right-top.vue";
import RightCenter from "./right-center.vue";
import RightBottom from "./right-bottom.vue";
import BoxBottom from "./box-bottom.vue";
import CenterAlarm from "./center-alram.vue";
</script>

<template>
  <div>
    <div class="index-box">
      <div class="contetn_left">
        <!-- <div class="pagetab">
        <div class="item">实时监测</div>
        <div class="item">统计分析</div>
      </div> -->
        <ItemWrap class="contetn_left-top contetn_lr-item" style="height: 225px" title="环境">
          <LeftTop />
        </ItemWrap>
        <ItemWrap class="contetn_left-center contetn_lr-item" title="产能">
          <LeftCenter />
        </ItemWrap>
        <ItemWrap class="contetn_left-bottom contetn_lr-item" title="质量" style="padding: 0 10px 16px">
          <LeftBottom />
        </ItemWrap>
      </div>
      <div class="contetn_center">
        <!-- <CenterMap class="contetn_center_top" title="设备分布图" /> -->
        <CenterAlarm></CenterAlarm>
        <ItemWrap class="contetn_center-bottom" title="数据异常报警比率">
          <CenterBottom />
        </ItemWrap>
      </div>
      <div class="contetn_right">
        <ItemWrap class="contetn_left-bottom contetn_lr-item" style="height: 225px" title="设备">
          <RightTop />
        </ItemWrap>
        <ItemWrap class="contetn_left-bottom contetn_lr-item" title="能耗" style="padding: 0 10px 16px">
          <RightCenter />
        </ItemWrap>
        <ItemWrap class="contetn_left-bottom contetn_lr-item" title="人力 ">
          <RightBottom />
        </ItemWrap>
      </div>
    </div>
    <div class="index-bottom">
      <BoxBottom></BoxBottom>
    </div>
  </div>
</template>

<style scoped lang="scss">
.index-box {
  display: flex;
  justify-content: space-between;
  width: 100%;
  min-height: calc(100% - 264px);
}

//左边 右边 结构一样
.contetn_left,
.contetn_right {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  justify-content: space-around;
  width: 540px;
}
.contetn_center {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: space-around;
  margin: 0 54px;
  .contetn_center-bottom {
    height: 355px;
  }
}
.contetn_lr-item {
  height: 260px;
}
</style>
