<template>
  <v-chart :option="chartOptions" class="chart" />
</template>

<script setup>
import { ref, onMounted } from "vue";
import { chartColor } from "@/assets/const/index.ts";

// 注册 VChart 组件
const chartOptions = ref({});

onMounted(() => {
  chartOptions.value = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      }
    },
    legend: {
      data: ["在岗", "缺岗"],
      textStyle: {
        color: chartColor.fontColor // 使用外部引入的 fontColor 变量
      }
    },
    xAxis: {
      type: "category",
      data: ["技术员", "操作员", "技术员", "技术员", "技术员"],
      axisLabel: {
        color: chartColor.fontColor // 使用外部引入的 fontColor 变量
      }
    },
    yAxis: [
      {
        type: "value",
        name: "人数",
        min: 0,
        max: 2000,
        position: "left",
        axisLabel: {
          color: chartColor.fontColor // 使用外部引入的 fontColor 变量
        }
      },
      {
        type: "value",
        name: "缺岗率 (%)",
        min: 0,
        max: 100,
        position: "right",
        axisLabel: {
          formatter: "{value} %"
        },
        axisLine: {
          show: false
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: "在岗",
        type: "bar",
        yAxisIndex: 0,
        data: [1600, 0, 1600, 1600, 1600],
        itemStyle: {
          color: chartColor.mainColor // 使用外部引入的 mainColor 变量
        },
        label: {
          show: true,
          position: "insideRight",
          formatter: "{c}"
        }
      },
      {
        name: "缺岗",
        type: "bar",
        yAxisIndex: 0,
        data: [0, 123, 0, 0, 0],
        itemStyle: {
          color: chartColor.mainColor + "44" // 设置缺岗颜色为更浅的颜色
        },
        label: {
          show: true,
          position: "insideRight",
          formatter: "{c}"
        }
      }
    ]
  };
});
</script>

<style scoped lang="scss">
.job-status-chart {
  width: 100%;
  height: 400px;
}
.chart {
  width: 100%;
  height: 100%;
}
</style>
