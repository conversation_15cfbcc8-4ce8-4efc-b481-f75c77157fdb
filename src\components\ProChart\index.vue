<template>
  <div class="table-box">
    <!-- 搜索表单 -->
    <SearchForm
      v-if="showSearchForm && searchFields.length > 0"
      :columns="searchColumns"
      :search-param="searchParams"
      :search="handleSearch"
      :reset="handleReset"
      :search-col="searchCol"
      @machine-change="handleMachineChange"
    />
    <div class="card table-main">
      <div class="toolbar">
        <!-- 左侧插槽 -->
        <div class="toolbar-left">
          <MachineSwitcher
            v-if="!showToolButton('hideMachineSwitcher') && !compareMode"
            :machine-list="machines"
            :current-machine-index="currentMachineIndex"
            @change="handleMachineChange"
          />
          <el-radio-group v-model="selectedTimeType" @change="handleTimeTypeChange">
            <el-radio-button label="day">{{ t("common.today") }}</el-radio-button>
            <el-radio-button label="month">{{ t("common.thisMonth") }}</el-radio-button>
            <el-radio-button label="year">{{ t("common.thisYear") }}</el-radio-button>
          </el-radio-group>

          <!-- 快捷翻页按钮 -->
          <div class="time-navigation" v-if="!compareMode">
            <el-button-group>
              <el-button size="small" @click="navigateTime('prev')">
                <el-icon><ArrowLeft /></el-icon>
                {{ getNavigationTitle("prev") }}
              </el-button>
              <el-button size="small" @click="navigateTime('next')">
                {{ getNavigationTitle("next") }}
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </el-button-group>
          </div>
          <el-button v-if="showToolButton('compare')" @click="toggleCompareMode" :type="compareMode ? 'primary' : ''">
            {{ compareMode ? t("common.exitCompare") : t("common.compare") }}
          </el-button>
          <!-- 新增日月年单选框 -->

          <!-- 新增多选选择器 -->
          <transition name="slide-fade">
            <div v-if="compareMode" class="compare-options">
              <el-radio-group v-model="selectedItem" @change="handleItemChange">
                <el-radio v-for="item in availableItems" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
              <el-button v-if="!isTotalMode" @click="toggleCompareModeType" type="success" size="small" :title="t('common.switchToOverview')">
                {{ t("common.overviewCompare") }}
              </el-button>
              <el-button v-if="isTotalMode" @click="toggleCompareModeType" type="info" size="small" :title="t('common.switchToTime')">
                {{ t("common.timeCompare") }}
              </el-button>
            </div>
          </transition>

          <slot name="toolbar-left" />
        </div>

        <!-- 右侧按钮 -->
        <div class="toolbar-right">
          <template v-if="showToolButton('table')">
            <el-button
              :icon="isTableMode ? Histogram : Grid"
              circle
              @click="isTableMode = !isTableMode"
              :title="isTableMode ? t('common.switchToChart') : t('common.switchToTable')"
            />
          </template>

          <template v-if="showToolButton('search')">
            <el-button :icon="Search" circle @click="toggleSearchForm" />
          </template>

          <template v-if="showToolButton('refresh')">
            <el-button :icon="Refresh" circle @click="fetchData" />
          </template>
          <template v-if="showToolButton('download')">
            <el-button :icon="Download" circle @click="exportData" />
          </template>

          <slot name="toolbar-right" />
        </div>
      </div>

      <!-- 图表主体 -->
      <v-chart
        :key="chartKey"
        v-if="!isTableMode"
        ref="chartRef"
        class="chart"
        :option="mergedOptions"
        :autoresize="true"
        @finished="handleChartReady"
      />

      <!-- 表格展示 -->
      <div class="chart" v-else>
        <el-table class="chart" :data="tableData" :height="props.chartHeight" style="width: 100%" border stripe>
          <el-table-column v-for="col in tableColumns" :key="col.prop" :prop="col.prop" :label="col.label" :min-width="col.width || 120" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch, provide } from "vue";
import type { ECharts } from "echarts";
import { BreakPoint } from "@/components/Grid/interface";
import { Refresh, Search, Download, Histogram, Grid, ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
import moment from "moment";
import { useRoute } from "vue-router";
import { ingoreUploadFeishuImage } from "@/utils";
import { useUserStore } from "@/stores/modules/user";
import { useI18n } from "vue-i18n";
// import { useTabsStore } from "@/stores/modules/tabs";

const { t, locale } = useI18n();
const userStore = useUserStore();
const route = useRoute();
const uploadsign = route.query.sign; // "123"（注意：query 参数是字符串）
const currentnodeid = route.meta.id;
// const tabStore = useTabsStore();

interface ProChartProps {
  options: any;
  fetchApi?: (params: Record<string, any>) => Promise<any>;
  autoFetch?: boolean;
  initParams?: Record<string, any>;
  searchFields?: any[];
  chartHeight?: string;
  transformData?: (data: any) => any;
  searchCol?: number | Record<BreakPoint, number>;
  toolButtons?: boolean | string[];
  tableColumns?: { prop: string; label: string; width?: number }[]; // 新增表格列配置
  machines: {
    id: number | string;
    name: string;
    options?: any; // 每个机台可单独配置
  }[];
  compareList?: any[];
  defaultCompareId?: string;
}
const delay = ms => new Promise(resolve => setTimeout(resolve, ms));
// const shitset = new Set();
// onUpdated(() => {
//   console.log(selectedItem.value, "selectedItem.value");
//   if (!shitset.has(selectedItem.value.label)) {
//     shitset.add(selectedItem.value.label);
//     // ingoreUploadFeishuImage(currentnodeid, selectedItem.value.label);
//   }
// });
const loopPush = async () => {
  // console.log(uploadsign, route, "uploadsign");
  if (uploadsign) {
    await delay(1600);
    if (compareMode.value) {
      for (const item of availableItems.value) {
        selectedItem.value = item.value;

        await delay(1600); // 暂停 2 秒
      }
    } else {
      toggleCompareMode();
      await delay(1600);

      for (const item of availableItems.value) {
        selectedItem.value = item.value;
        await delay(1600);
      }

      toggleCompareMode();
    }
  }
};

const currentMachineIndex = ref(0);
const chartKey = ref(0); // 强制刷新图表
const props = withDefaults(defineProps<ProChartProps>(), {
  autoFetch: true,
  initParams: () => ({
    time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")]
  }),
  searchFields: () => [],
  chartHeight: "100%",
  transformData: (data: any) => data,
  toolButtons: true,
  searchCol: () => ({ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }),
  tableColumns: () => [],
  defaultCompareId: "",
  compareList: () => []
});

// 新增表格模式状态
const compareMode = ref(false);
const isTableMode = ref(false);
// 新增单选状态
const selectedItem = ref<string | any>(props.defaultCompareId);
// 新增总览模式状态
const isTotalMode = ref(true);
// 新增时间类型选择状态，默认是日
const selectedTimeType = ref("day");

// 可选择的项目
const availableItems: any = computed(() => {
  const series = props.compareList || [];
  return series.map(item => ({
    ...item,
    label: t(`common.compareList.${item.value}`).includes("common.compareList") ? item.label : t(`common.compareList.${item.value}`)
  }));
});

// 机台切换处理
const handleMachineChange = (index: number) => {
  currentMachineIndex.value = index;
  const selectedMachine = props.machines[index];
  searchParams.value.machine = selectedMachine.id;
  //console.log("selectedMachine.id", selectedMachine.id);

  // emit("machine-change", selectedMachS
  chartKey.value++; // 强制图表重新加载
  fetchData();
};
provide("handleMachineChange", handleMachineChange);
// 生成表格数据
const tableColumns = computed(() => {
  if (props.tableColumns.length > 0) return props.tableColumns;

  // 自动从图表配置中生成列
  const xAxis = mergedOptions.value.xAxis?.[0] ? mergedOptions.value.xAxis?.[0] : mergedOptions.value.xAxis;
  const series = mergedOptions.value.series || [];
  const columns: any = [];
  //console.log("xais");

  // 在对比模式下（总览和时间对比都需要），添加时间段列
  if (compareMode.value) {
    columns.push({
      prop: "time",
      label: "时间段",
      width: 150
    });
  }
  // 在非对比模式下，当x轴不是时间时（如NG类型），添加时间列
  if (!compareMode.value && xAxis.name && xAxis.name !== "时间") {
    columns.push({
      prop: "time",
      label: "时间",
      width: 150
    });
  }
  if (xAxis?.type === "category" && xAxis.data) {
    let label = "时间";
    if (compareMode.value && isTotalMode.value) {
      label = "机台";
    } else if (compareMode.value && !isTotalMode.value) {
      label = "机台"; // 在时间对比模式下，xAxis 显示的是机台信息
    } else {
      label = xAxis.name || "时间";
    }

    columns.push({
      prop: "xAxis",
      label: label,
      width: 120
    });
  }

  series.forEach(s => {
    columns.push({
      prop: s.name,
      label: s.name,
      width: 120
    });
  });
  console.log(columns, "columns");

  return columns;
});

const tableData = computed(() => {
  const xAxis = Array.isArray(mergedOptions.value.xAxis) ? mergedOptions.value.xAxis?.[0] : mergedOptions.value.xAxis;
  const series = mergedOptions.value.series || [];
  //console.log("xAxis:", xAxis);

  if (!xAxis?.data) return [];

  return xAxis.data.map((x, index) => {
    const row: any = { xAxis: x };

    // 添加时间段信息（对比模式和非对比模式都需要）
    let timeRange = "--";

    if (searchParams.value.time && searchParams.value.time.length >= 2) {
      const startTime = searchParams.value.time[0];
      const endTime = searchParams.value.time[1];

      // 计算时间跨度
      const startMoment = moment(startTime);
      const endMoment = moment(endTime);
      const daysDiff = endMoment.diff(startMoment, "days");
      const monthsDiff = endMoment.diff(startMoment, "months");

      // 根据时间跨度自动决定显示格式
      if (daysDiff <= 1) {
        // 一天内：hour模式 - 显示具体日期
        timeRange = startMoment.format("YYYY-MM-DD");
      } else if (daysDiff > 1 && monthsDiff <= 3) {
        // 超过一天但小于等于3个月：month模式 - 显示日期范围
        timeRange = `${startMoment.format("YYYY-MM-DD")} ~ ${endMoment.format("YYYY-MM-DD")}`;
      } else {
        // 超过3个月：year模式 - 显示月份范围
        timeRange = `${startMoment.format("YYYY-MM")} ~ ${endMoment.format("YYYY-MM")}`;
      }
    } else if (searchParams.value.time && searchParams.value.time.length === 1) {
      // 如果只有一个时间，显示单个时间
      const singleTime = searchParams.value.time[0];
      timeRange = moment(singleTime).format("YYYY-MM-DD");
    }

    // 在对比模式下，添加时间段列
    if (compareMode.value) {
      row.time = timeRange;
    } else {
      // 在非对比模式下，当x轴不是时间时（如NG类型），添加时间信息到time字段
      if (xAxis.name && xAxis.name !== "时间") {
        // 为非时间轴添加时间信息，使用与对比模式相同的timeRange逻辑
        row.time = timeRange;
        // x轴显示原值（如NG类型）
        row.xAxis = x;
      } else {
        // 时间轴的处理：为时间值添加年月日信息
        let formattedTime = x;
        if (searchParams.value.time && searchParams.value.time.length >= 2) {
          const startTime = searchParams.value.time[0];
          const endTime = searchParams.value.time[1];
          const startMoment = moment(startTime);
          const endMoment = moment(endTime);
          const daysDiff = endMoment.diff(startMoment, "days");
          const monthsDiff = endMoment.diff(startMoment, "months");

          // 根据时间跨度自动决定时间格式
          if (daysDiff <= 1) {
            // 一天内：hour模式 - 添加年月日，如 "09:00 2024-01-15"
            formattedTime = `${startMoment.format("YYYY-MM-DD")} ${x} `;
          } else if (daysDiff > 1 && monthsDiff <= 3) {
            // 超过一天但≤3个月：month模式 - 添加年份，如 "01-15 2024"
            formattedTime = `${startMoment.format("YYYY")}-${x} `;
          } else {
            // 超过3个月：year模式 - 只显示原值，如 "01月"
            formattedTime = `${x}`;
          }
        } else if (searchParams.value.time && searchParams.value.time.length === 1) {
          // 单个时间的处理
          const queryDate = moment(searchParams.value.time[0]);
          formattedTime = `${queryDate.format("YYYY-MM-DD")} ${x} `;
        }
        row.xAxis = formattedTime;
      }
    }

    series.forEach(s => {
      row[s.name] = s.data?.[index] ?? "--";
    });
    return row;
  });
});

// 修改导出逻辑
const exportData = () => {
  if (isTableMode.value) {
    exportTableData();
  } else {
    exportChartData();
  }
};

// const exportTableData = () => {
//   try {
//     const headers = tableColumns.value.map(col => col.label);
//     const rows = tableData.value.map(row => {
//       return tableColumns.value.map(col => row[col.prop]);
//     });

//     const filename = `数据表格_${new Date().toLocaleDateString().replace(/\//g, "-")}.csv`;
//     downloadCSV([headers, ...rows], filename);
//   } catch (error) {
//     console.error("表格导出失败:", error);
//     ElMessage.error("表格数据导出失败，请稍后重试");
//   }
// };
const exportTableData = () => {
  try {
    const headers = tableColumns.value.map(col => col.label);
    const rows = tableData.value.map(row => {
      return tableColumns.value.map(col => {
        // 直接返回行数据，时间格式化已在 tableData 中处理
        return row[col.prop] || "--";
      });
    });

    // 根据对比模式确定文件名
    let filename;
    if (compareMode.value) {
      const selectedLabel = availableItems.value.find(item => item.value === selectedItem.value)?.label || "对比数据";
      const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");
      if (isTotalMode.value) {
        filename = `${selectedLabel}_总览对比_${currentTime}.csv`;
      } else {
        filename = `${selectedLabel}_时间对比_${currentTime}.csv`;
      }
    } else {
      const machine = props.machines[currentMachineIndex.value]?.name || "--";
      const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");
      filename = `${machine}生产数据_${currentTime}.csv`;
    }

    downloadCSV([headers, ...rows], filename);
  } catch (error) {
    console.error("表格导出失败:", error);
    ElMessage.error("表格数据导出失败，请稍后重试");
  }
};
const chartInstance = ref<any>(null);

// 获取图表实例
const handleChartReady = () => {
  nextTick(() => {
    if (compareMode.value && userStore.isAlarm && uploadsign) {
      console.log(selectedItem.value, "selectedItem.value");
      const label = availableItems.value.find(item => item.value === selectedItem.value).label;
      ingoreUploadFeishuImage(currentnodeid, label);
    }
    if (chartRef.value?.chart) {
      //console.log("图表准备就绪");

      chartInstance.value = chartRef.value?.chart;
      chartInstance.value.off("magictypechanged"); // 移除旧监听器，避免重复
      chartInstance.value.on("magictypechanged", (params: any) => {
        if (params.currentType === "bar") {
          // 调用fetchData，使用当前保存的参数
          fetchData();
          //console.log("切换为柱状图");
        }
      });
      emitDWdata();
      // eslint-disable-next-line vue/custom-event-name-casing
      emit("chart-ready", chartRef.value.chart as ECharts);
    }
  });
};

// 导出数据
// const exportChartData = () => {
//   if (!chartInstance.value) return;

//   try {
//     // 获取图表数据
//     const option = chartInstance.value.getOption();

//     // 处理表头
//     const headers = ["时间", ...option.series.map((s: any) => s.name)];

//     // 处理数据行
//     const rows = option.xAxis[0].data.map((date: string, i: number) => [date, ...option.series.map((s: any) => s.data[i] ?? "")]);

//     // 生成文件名
//     const filename = `生产数据_${new Date().toLocaleDateString().replace(/\//g, "-")}.csv`;

//     // 下载CSV
//     downloadCSV([headers, ...rows], filename);
//   } catch (error) {
//     console.error("导出失败:", error);
//     ElMessage.error("数据导出失败，请稍后重试");
//   }
// };
const exportChartData = () => {
  if (!chartInstance.value) return;
  try {
    // 获取图表数据
    const option = chartInstance.value.getOption();

    // 根据对比模式确定表头
    let headers;
    if (compareMode.value) {
      // 对比模式下（总览和时间对比都需要）：时间段 + 机台 + 数据列
      headers = ["时间段", "机台", ...option.series.map((s: any) => s.name)];
    } else {
      // 普通模式：根据x轴类型确定表头
      const xAxis = Array.isArray(option.xAxis) ? option.xAxis[0] : option.xAxis;

      if (xAxis.name && xAxis.name !== "时间") {
        // 非时间轴（如NG类型），包含时间列和x轴列
        headers = ["时间", xAxis.name || "分类", ...option.series.map((s: any) => s.name)];
      } else {
        // 时间轴，只有时间列
        headers = ["时间", ...option.series.map((s: any) => s.name)];
      }
    }

    // 处理数据行
    const rows = option.xAxis[0].data.map((xValue: string, i: number) => {
      const row = [];

      if (compareMode.value) {
        // 对比模式：添加时间段列
        let timeRange = "--";

        if (searchParams.value.time && searchParams.value.time.length >= 2) {
          const startTime = searchParams.value.time[0];
          const endTime = searchParams.value.time[1];

          // 计算时间跨度
          const startMoment = moment(startTime);
          const endMoment = moment(endTime);
          const daysDiff = endMoment.diff(startMoment, "days");
          const monthsDiff = endMoment.diff(startMoment, "months");
          const yearsDiff = endMoment.diff(startMoment, "years");

          // 根据时间跨度自动决定显示格式
          if (daysDiff <= 1) {
            // 小于等于一天：显示具体日期
            timeRange = startMoment.format("YYYY-MM-DD");
          } else if (daysDiff > 1 && monthsDiff <= 3) {
            // 大于一天但小于等于3个月：显示日期范围
            timeRange = `${startMoment.format("YYYY-MM-DD")} ~ ${endMoment.format("YYYY-MM-DD")}`;
          } else if (monthsDiff > 3 && yearsDiff <= 1) {
            // 超过3个月但小于等于一年：显示月份范围
            timeRange = `${startMoment.format("YYYY-MM")} ~ ${endMoment.format("YYYY-MM")}`;
          } else {
            // 超过一年：显示年份范围
            timeRange = `${startMoment.format("YYYY")} ~ ${endMoment.format("YYYY")}`;
          }
        } else if (searchParams.value.time && searchParams.value.time.length === 1) {
          // 如果只有一个时间，显示单个时间
          const singleTime = searchParams.value.time[0];
          timeRange = moment(singleTime).format("YYYY-MM-DD");
        }

        row.push(timeRange);

        // 添加机台列（xValue 是机台名称）
        row.push(xValue);
      } else {
        // 普通模式：检查x轴是否为时间类型
        const xAxis = Array.isArray(option.xAxis) ? option.xAxis[0] : option.xAxis;

        if (xAxis.name && xAxis.name !== "时间") {
          // 非时间轴（如NG类型），先添加时间信息，再添加x轴值
          // 使用与表格相同的timeRange逻辑
          let timeRange = "--";
          if (searchParams.value.time && searchParams.value.time.length >= 2) {
            const startTime = searchParams.value.time[0];
            const endTime = searchParams.value.time[1];

            // 计算时间跨度
            const startMoment = moment(startTime);
            const endMoment = moment(endTime);
            const daysDiff = endMoment.diff(startMoment, "days");
            const monthsDiff = endMoment.diff(startMoment, "months");

            // 根据时间跨度自动决定显示格式
            if (daysDiff <= 1) {
              // 一天内：hour模式 - 显示具体日期
              timeRange = startMoment.format("YYYY-MM-DD");
            } else if (daysDiff > 1 && monthsDiff <= 3) {
              // 超过一天但小于等于3个月：month模式 - 显示日期范围
              timeRange = `${startMoment.format("YYYY-MM-DD")} ~ ${endMoment.format("YYYY-MM-DD")}`;
            } else {
              // 超过3个月：year模式 - 显示月份范围
              timeRange = `${startMoment.format("YYYY-MM")} ~ ${endMoment.format("YYYY-MM")}`;
            }
          } else if (searchParams.value.time && searchParams.value.time.length === 1) {
            // 如果只有一个时间，显示单个时间
            const singleTime = searchParams.value.time[0];
            timeRange = moment(singleTime).format("YYYY-MM-DD");
          }
          row.push(timeRange);
          // x轴显示原值（如NG类型）
          row.push(xValue);
        } else {
          // 时间轴的处理：为时间值添加年月日信息
          let formattedTime = xValue;
          if (searchParams.value.time && searchParams.value.time.length >= 2) {
            const startTime = searchParams.value.time[0];
            const endTime = searchParams.value.time[1];
            const startMoment = moment(startTime);
            const endMoment = moment(endTime);
            const daysDiff = endMoment.diff(startMoment, "days");
            const monthsDiff = endMoment.diff(startMoment, "months");

            // 根据时间跨度自动决定时间格式
            if (daysDiff <= 1) {
              // 一天内：hour模式 - 添加年月日，如 "09:00 2024-01-15"
              formattedTime = `${xValue} ${startMoment.format("YYYY-MM-DD")}`;
            } else if (daysDiff > 1 && monthsDiff <= 3) {
              // 超过一天但≤3个月：month模式 - 添加年份，如 "01-15 2024"
              formattedTime = `${xValue} ${startMoment.format("YYYY")}`;
            } else {
              // 超过3个月：year模式 - 只显示原值，如 "01月"
              formattedTime = `${xValue}`;
            }
          } else if (searchParams.value.time && searchParams.value.time.length === 1) {
            // 单个时间的处理
            const queryDate = moment(searchParams.value.time[0]);
            formattedTime = `${xValue} ${queryDate.format("YYYY-MM-DD")}`;
          }
          row.push(formattedTime);
        }
      }

      // 添加数据列
      row.push(...option.series.map((s: any) => s.data[i] ?? ""));
      return row;
    });

    // 根据对比模式确定文件名
    let filename;
    if (compareMode.value) {
      const selectedLabel = availableItems.value.find(item => item.value === selectedItem.value)?.label || "对比数据";
      const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");
      if (isTotalMode.value) {
        filename = `${selectedLabel}_总览对比_${currentTime}.csv`;
      } else {
        filename = `${selectedLabel}_时间对比_${currentTime}.csv`;
      }
    } else {
      const machine = props.machines[currentMachineIndex.value]?.name || "--";
      const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");
      filename = `${machine}生产数据_${currentTime}.csv`;
    }

    // 下载CSV
    downloadCSV([headers, ...rows], filename);
  } catch (error) {
    console.error("导出失败:", error);
    ElMessage.error("数据导出失败，请稍后重试");
  }
};
// CSV工具函数
const formatCSVCell = (value: any) => {
  if (value == null) return "";
  if (typeof value === "string") {
    return `"${value.replace(/"/g, '""')}"`;
  }
  return value;
};

const generateCSV = (data: any[][]) => {
  return data.map(row => row.map(cell => formatCSVCell(cell)).join(",")).join("\n");
};

const downloadCSV = (data: any[][], filename: string) => {
  const csvContent = generateCSV(data);
  const blob = new Blob([`\uFEFF${csvContent}`], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
};

const emit = defineEmits([
  "search",
  "reset",
  "data-loaded",
  "export-data",
  "error",
  "chart-ready"
  // "machine-change" // 新增事件
]);

// 图表实例引用
const chartRef = ref<InstanceType<typeof VChart>>();

// 响应式状态
const showSearchForm = ref(false);
const searchParams = ref({ time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")] });
const chartData = ref<any>(null);
const isLoading = ref(false);

// 切换对比模式
const toggleCompareMode = () => {
  compareMode.value = !compareMode.value;
  searchParams.value.compareMode = compareMode.value;
  isTotalMode.value = true; // 进入对比模式默认总览模式

  if (compareMode.value) {
    // 点击对比时，设置 machineIds 为所有机台的 id
    searchParams.value.machineIds = props.machines.map(machine => machine.id);
    if (!selectedItem.value && props.compareList[0]) {
      selectedItem.value = props.compareList[0].label;
    }
    fetchData();
  } else {
    searchParams.value.machineIds = [];
    selectedItem.value = props.defaultCompareId; // 退出对比模式时重置选择
  }
  // chartKey.value++; // 强制图表重新加载
};

// 处理选择项变化
const handleItemChange = () => {
  // chartKey.value++; // 强制图表重新加载
  // fetchData();
};

// 切换对比模式类型
const toggleCompareModeType = () => {
  isTotalMode.value = !isTotalMode.value;
  chartKey.value++; // 强制图表重新加载
  fetchData();
};

// 处理时间类型选择变化
const handleTimeTypeChange = () => {
  const now = moment();
  let startTime;
  let endTime;

  switch (selectedTimeType.value) {
    case "day":
      startTime = now.clone().startOf("day");
      endTime = now.clone().endOf("day");
      break;
    case "month":
      startTime = now.clone().startOf("month");
      endTime = now.clone().endOf("month");
      break;
    case "year":
      startTime = now.clone().startOf("year");
      endTime = now.clone().endOf("year");
      break;
    default:
      return;
  }

  // 转换为正常的北京时间格式字符串
  searchParams.value.time = [startTime.format("YYYY-MM-DD HH:mm:ss"), endTime.format("YYYY-MM-DD HH:mm:ss")];
  searchParams.value.mode = selectedTimeType.value;
  chartKey.value++; // 强制图表重新加载
  fetchData();
};

// 时间导航功能
const navigateTime = (direction: "prev" | "next") => {
  if (!searchParams.value.time || searchParams.value.time.length < 2) return;

  const currentStart = moment(searchParams.value.time[0]);
  const currentEnd = moment(searchParams.value.time[1]);
  let newStart: moment.Moment;
  let newEnd: moment.Moment;

  const offset = direction === "prev" ? -1 : 1;

  switch (selectedTimeType.value) {
    case "day":
      newStart = currentStart.clone().add(offset, "day").startOf("day");
      newEnd = currentEnd.clone().add(offset, "day").endOf("day");
      break;
    case "month":
      newStart = currentStart.clone().add(offset, "month").startOf("month");
      newEnd = currentEnd.clone().add(offset, "month").endOf("month");
      break;
    case "year":
      newStart = currentStart.clone().add(offset, "year").startOf("year");
      newEnd = currentEnd.clone().add(offset, "year").endOf("year");
      break;
    default:
      return;
  }

  // 更新时间参数
  searchParams.value.time = [newStart.format("YYYY-MM-DD HH:mm:ss"), newEnd.format("YYYY-MM-DD HH:mm:ss")];

  chartKey.value++; // 强制图表重新加载
  fetchData();
};

// 获取导航按钮的提示文本
const getNavigationTitle = (direction: "prev" | "next") => {
  const prefix = direction === "prev" ? "上一" : "下一";

  switch (selectedTimeType.value) {
    case "day":
      return `${prefix}天`;
    case "month":
      return `${prefix}月`;
    case "year":
      return `${prefix}年`;
    default:
      return "";
  }
};
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");
// 颜色配置常量
const COLORS = {
  production: "#00bfff",
  defective: "#ff4d4f",
  rate: "#fac858",
  font: chartColor,
  splitLine: "#eee"
};

// 在 mergedOptions 之前添加
const legendFormatter = computed(() => {
  return (name: string) => {
    const compareItem = props.compareList?.find(item => item.label === name);
    return compareItem
      ? t(`common.compareList.${compareItem.value}`).includes("common.compareList")
        ? compareItem.label
        : t(`common.compareList.${compareItem.value}`)
      : name;
  };
});

// 监听语言变化
watch(
  () => locale.value,
  () => {
    // chartKey.value++; // 强制刷新图表

    // eslint-disable-next-line vue/custom-event-name-casing
    emit("data-loaded", chartData.value);
  }
);

// 修改 mergedOptions
const mergedOptions = computed(() => {
  const baseOption = props.options;

  if (compareMode.value) {
    const seriesData = [];
    const data = chartData.value;

    if (data && data.isCompare) {
      if (isTotalMode.value) {
        // 总览模式（机台为X轴）
        let selectedData;
        if (data.compare[0].hasOwnProperty(selectedItem.value)) {
          selectedData = data.compare[0][selectedItem.value];
        } else {
          const firstKey = Object.keys(data.compare[0])[0];
          selectedData = data.compare[0][firstKey];
        }
        const xAxisData = [];
        const seriesDataItem = {
          name: availableItems.value.find(item => item.value === selectedItem.value)?.label,
          type: "bar",
          data: [],
          itemStyle: {
            color: COLORS[selectedItem.value]
          },
          label: {
            show: true,
            position: "top",
            formatter: "{c}"
          }
        };

        props.machines.forEach(machine => {
          const item = selectedData.find(item => item.machine === machine.id);
          if (item) {
            xAxisData.push(machine.name);
            seriesDataItem.data.push(item.total);
          }
        });

        seriesData.push(seriesDataItem);

        return {
          ...baseOption,
          title: null,
          legend: null,
          xAxis: {
            type: "category",
            data: xAxisData,
            axisLabel: { color: COLORS.font }
          },
          tooltip: null,
          yAxis: [
            {
              type: "value",
              name: null,
              axisLabel: { color: COLORS.font }
            }
          ],
          series: seriesData
        };
      } else {
        // 原时间对比模式
        const selectedLabel = availableItems.value.find(item => item.value === selectedItem.value)?.label;
        props.machines.forEach(machine => {
          //console.log(1232113);

          const itemData = data.compare[0][selectedItem.value]?.find(item => item.machine === machine.id);
          if (itemData) {
            seriesData.push({
              name: `${machine.name} ${selectedLabel}`,
              type: "bar",
              data: itemData.data,
              label: {
                show: true,
                position: "top",
                formatter: "{c}"
              }
            });
          }
        });
        // 定义 tooltip 配置
        const tooltipConfig = {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999"
            }
          },
          formatter: (params: any) => {
            let tooltipHtml = `<div style="padding: 5px;">`;
            params.forEach((param: any) => {
              tooltipHtml += `
        <div>
          <span style="display:inline-block;width:10px;height:10px;background:${param.color};border-radius:50%"></span>
          ${param.seriesName}: ${param.data}
        </div>
      `;
            });
            tooltipHtml += `</div>`;
            return tooltipHtml;
          }
        };
        return {
          ...baseOption,
          xAxis: {
            ...chartData.value.xAxis,
            data: data.categories,
            axisLabel: { color: COLORS.font }
          },
          legend: {
            data: seriesData.map(s => s.name),
            textStyle: { color: COLORS.font }
          },
          tooltip: tooltipConfig,
          title: null,
          series: seriesData
        };
      }
    }
  }

  // 非对比模式下的配置
  const baseOptionWithLegend = {
    ...baseOption,
    legend: baseOption.legend
      ? {
          ...baseOption.legend,
          formatter: legendFormatter.value,
          textStyle: { color: COLORS.font }
        }
      : baseOption.legend
  };

  return baseOptionWithLegend;
});

// 扁平化 columns
const flatColumns = computed(() => flatColumnsFunc(props.searchFields));

const enumMap = ref(new Map<string, { [key: string]: any }[]>());
const setEnumMap = async ({ prop, enum: enumValue }: any) => {
  if (!enumValue) return;

  // 如果当前 enumMap 存在相同的值 return
  if (enumMap.value.has(prop!) && (typeof enumValue === "function" || enumMap.value.get(prop!) === enumValue)) return;

  // 当前 enum 为静态数据，则直接存储到 enumMap
  if (typeof enumValue !== "function") return enumMap.value.set(prop!, enumValue);
  // 为了防止接口执行慢，而存储慢，导致重复请求，所以预先存储为[],接口返回后再二次存储
  enumMap.value.set(prop!, []);
  // 当前 enum 为后台数据需要请求数据，则调用该请求接口，并存储到 enumMap
  const { data } = await enumValue();
  enumMap.value.set(prop!, data);
};

// 注入 enumMap
// import { provide } from "vue";
provide("enumMap", enumMap);

// 扁平化 columns 的方法
const flatColumnsFunc = (columns: any[], flatArr: any[] = []) => {
  columns.forEach(async col => {
    if (col._children?.length) flatArr.push(...flatColumnsFunc(col._children));
    flatArr.push(col);

    // column 添加默认 isShow && isSetting && isFilterEnum 属性值
    col.isShow = col.isShow ?? true;
    col.isSetting = col.isSetting ?? true;
    col.isFilterEnum = col.isFilterEnum ?? true;

    // 设置 enumMap
    await setEnumMap(col);
  });
  return flatArr.filter(item => !item._children?.length);
};

// 过滤需要搜索的配置项
const searchColumns = computed(() => {
  return flatColumns.value?.filter(item => item.search?.el || item.search?.render).sort((a, b) => a.search!.order! - b.search!.order!);
});

// 按钮显示逻辑
const showToolButton = (buttonType: string) => {
  if (typeof props.toolButtons === "boolean") return props.toolButtons;
  return props.toolButtons?.includes(buttonType);
};

// 切换搜索表单
const toggleSearchForm = () => {
  showSearchForm.value = !showSearchForm.value;
  if (!showSearchForm.value) {
    handleReset();
  }
};

// 数据获取
// const fetchData = async () => {
//   if (!props.fetchApi) return;

//   try {
//     isLoading.value = true;
//     const response = await props.fetchApi(searchParams.value);
//     const transformed = props.transformData(response.data);
//     chartData.value = transformed;
//     // eslint-disable-next-line vue/custom-event-name-casing
//     emit("data-loaded", transformed);
//   } catch (error) {
//     emit("error", error);
//     console.error("Chart data fetch failed:", error);
//   } finally {
//     isLoading.value = false;
//   }
// };
// 数据获取
const fetchData = async () => {
  if (!props.fetchApi) return;
  try {
    isLoading.value = true;
    const response = await props.fetchApi(searchParams.value);
    const transformed = props.transformData(response.data);
    chartData.value = transformed;
    // eslint-disable-next-line vue/custom-event-name-casing
    emit("data-loaded", transformed);
  } catch (error) {
    emit("error", error);
    console.error("Chart data fetch failed:", error);
    ElMessage.error("数据获取失败，请稍后重试");
  } finally {
    isLoading.value = false;
  }
};
// 搜索处理
const handleSearch = (params: Record<string, any>) => {
  searchParams.value = { ...searchParams.value, ...params };
  fetchData();
  emit("search", searchParams.value);
};

// 重置处理
const handleReset = () => {
  const now = moment();
  let startTime;
  let endTime;

  startTime = now.clone().startOf("day");
  endTime = now.clone().endOf("day");

  // 转换为正常的北京时间格式字符串
  // searchParams.value.time = [startTime.format("YYYY-MM-DD HH:mm:ss"), endTime.format("YYYY-MM-DD HH:mm:ss")];
  selectedTimeType.value = "day";
  searchParams.value = { ...props.initParams, time: [startTime.format("YYYY-MM-DD HH:mm:ss"), endTime.format("YYYY-MM-DD HH:mm:ss")] };
  fetchData();
  emit("reset");
};
watch(
  () => userStore.isAlarm,
  newValue => {
    if (newValue) {
      loopPush();
    }
  },
  { immediate: true }
);
// 初始化
onMounted(() => {
  if (props.autoFetch && props.fetchApi) {
    // 初始化时间类型
    // handleTimeTypeChange();
    //console.log("初始化", searchParams.value);
    searchParams.value = { ...props.initParams, ...searchParams.value };
    fetchData();
  }
  if (props.searchFields.length > 0) {
    showSearchForm.value = props.autoFetch;
  }
  // loopPush();
});

const emitDWdata = () => {
  let csvContent: string | null = null;
  if (isTableMode.value) {
    const headers = tableColumns.value.map(col => col.label);
    const rows = tableData.value.map(row => {
      return tableColumns.value.map(col => row[col.prop]);
    });
    csvContent = generateCSV([headers, ...rows]);
  } else if (chartInstance.value) {
    const option = chartInstance.value.getOption();
    const headers = ["时间", ...option.series.map((s: any) => s.name)];
    const rows = option.xAxis[0].data.map((date: string, i: number) => [date, ...option.series.map((s: any) => s.data[i] ?? "")]);
    csvContent = generateCSV([headers, ...rows]);
  }
  if (csvContent) {
    // eslint-disable-next-line vue/custom-event-name-casing
    emit("export-data", csvContent);
  }
};
// 监听初始参数变化
watch(
  () => props.initParams,
  newParams => {
    searchParams.value = { ...newParams, time: searchParams.value.time };
    fetchData();
  },
  { deep: true }
);
// // 监听 chartData 变化，当数据更新时生成并传递 CSV 内容
watch(
  tableData,
  () => {
    let csvContent: string | null = null;
    if (isTableMode.value) {
      const headers = tableColumns.value.map(col => col.label);
      const rows = tableData.value.map(row => {
        return tableColumns.value.map(col => row[col.prop]);
      });
      csvContent = generateCSV([headers, ...rows]);
    }
    if (csvContent) {
      // eslint-disable-next-line vue/custom-event-name-casing
      emit("export-data", csvContent);
    }
  },
  { deep: true }
);
// ProChart.vue 新增 watch
watch(
  () => searchParams.value.machine, // 监听机台 ID 变化
  newMachineId => {
    if (!newMachineId) return; // 处理未选择或清空的情况

    // 根据机台 ID 查找索引
    const machineIndex = props.machines.findIndex(machine => machine.id === newMachineId);

    // 索引存在且与当前索引不同时更新
    if (machineIndex !== -1 && currentMachineIndex.value !== machineIndex) {
      currentMachineIndex.value = machineIndex; // 更新当前索引
      handleMachineChange(machineIndex); // 触发机台切换逻辑（包括图表刷新）
    }
  },
  { immediate: true } // 初始化时根据默认参数匹配索引
);

defineExpose({
  refresh: fetchData,
  getTableList: fetchData,
  handleSearch,
  getParams: () => searchParams.value,
  updateParams: (params: Record<string, any>) => {
    searchParams.value = { ...searchParams.value, ...params };
  },
  getChartInstance: () => chartRef.value?.chart, // 暴露图表实例
  // handleMachineChange // 暴露handleMachineChange方法
  // 新增：暴露机台切换方法
  handleMachineChange,
  // 确保暴露 fetchData 方法
  fetchData
});
</script>

<style scoped>
.table-box {
  width: 100%;
  height: 100%;
}

/* .card.table-main {
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgb(0 0 0 / 10%);
} */
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
.toolbar-right,
.toolbar-left {
  display: flex;
  gap: 8px;
  align-items: center;
}
.chart {
  width: 100%;
  height: v-bind("props.chartHeight");
  min-height: 200px;
}
.compare-options {
  display: flex;
  gap: 8px;
  align-items: center;
  overflow: hidden;
}
.time-navigation {
  display: flex;
  align-items: center;
  margin-left: 8px;
}
.time-navigation .el-button {
  display: flex;
  gap: 4px;
  align-items: center;
}

/* 定义动画 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}
.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
