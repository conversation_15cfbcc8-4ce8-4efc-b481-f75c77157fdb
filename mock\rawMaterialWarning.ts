import Mock from "mockjs";

// 统一响应格式（只读模式）
function mockResponse(data) {
  return {
    code: 200,
    msg: "请求成功",
    data: data,
    extras: "UnifyContextTake()",
    time: new Date().toISOString()
  };
}

// 辅助函数，将数字转换为中文数字
Mock.Random.extend({
  toChineseNumber: function (num) {
    const chnNumChar = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
    return chnNumChar[num];
  }
});

export default [
  // 新增原材料信息
  // {
  //   url: "/api/sys/mes/ProductionMaterialRecordAdd",
  //   method: "post",
  //   response: () => {
  //     return mockResponse({ message: "新增原材料信息成功" });
  //   }
  // },
  // // 修改原材料信息
  // {
  //   url: "/api/sys/mes/ProductionMaterialRecordEdit",
  //   method: "post",
  //   response: () => {
  //     return mockResponse({ message: "修改原材料信息成功" });
  //   }
  // },
  // // 删除原材料信息
  // {
  //   url: "/api/sys/mes/ProdProductionMaterialRecordDelete",
  //   method: "delete",
  //   response: () => {
  //     return mockResponse({ message: "删除原材料信息成功" });
  //   }
  // }
  // 查询原材料信息
  // {
  //   url: "/api/sys/mes/ProductionMaterialRecordGet",
  //   method: "get",
  //   response: () => {
  //     const list = Mock.mock({
  //       "list|1-10": [
  //         {
  //           "ID|+1": 1,
  //           Number: "@word(10)",
  //           Factory: "@word(8)",
  //           Workshop: "@word(4)",
  //           ProductionLine: "@word(6)",
  //           MachineCode: "@word(20)",
  //           MachineName: "@word(50)",
  //           StationCode: "@word(20)",
  //           StationName: "@word(50)",
  //           MaterialType: "@word(50)",
  //           Batch: "@word(20)",
  //           MaterialCode: "@word(20)",
  //           MaterialName: "@word(50)",
  //           Unit: "@word(10)",
  //           TotalQuantity: "@float(0, 1000, 0, 2)",
  //           UsedQuantity: "@float(0, 1000, 0, 2)",
  //           RemainingQuantity: "@float(0, 1000, 0, 2)",
  //           "IsReplaced|1": [true, false],
  //           FeedingTime: '@datetime("yyyy-MM-dd HH:mm:ss")',
  //           Operator: "@word(50)",
  //           OperationTime: '@datetime("yyyy-MM-dd HH:mm:ss")'
  //         }
  //       ]
  //     }).list;
  //     const pageNum = 1;
  //     const pageSize = 10;
  //     const total = list.length;
  //     const pages = Math.ceil(total / pageSize);
  //     const hasPrevPages = pageNum > 1;
  //     const hasNextPages = pageNum < pages;
  //     const responseData = {
  //       pageNum,
  //       pageSize,
  //       total,
  //       pages,
  //       list,
  //       hasPrevPages,
  //       hasNextPages
  //     };
  //     return mockResponse(responseData);
  //   }
  // }
];
