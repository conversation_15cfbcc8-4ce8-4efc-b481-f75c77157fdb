import { moduleRequest } from "@/api/request";

const http = moduleRequest("/sys/mes/"); // 复用基础路径

const deviceProtectdailyProtectApi = {
  /** 新增日常维护信息 */
  ProdMaintenanceCheckAdd(params: object) {
    return http.post("ProdMaintenanceCheckAdd", params);
  },

  /** 修改日常维护信息 */
  ProdMaintenanceCheckEdit(params: object) {
    return http.post("ProdMaintenanceCheckEdit", params);
  },

  /** 删除日常维护信息 */
  ProdMaintenanceCheckDelete(params: object) {
    return http.delete("ProdMaintenanceCheckDelete", params);
  },

  /** 查询日常维护信息 */
  ProdMaintenanceCheckGet(params?: object) {
    return http.get("ProdMaintenanceCheckGet", params);
  },
  getTree(params?: object) {
    return http.get("ProdMaintenanceCheckGetTree", params);
  },
  getProdEquipmentMaterialInfoGetTree(params?: object) {
    return http.get("ProdEquipmentMaterialInfoGetTree", params);
  },
  getProd_MaterialInformationGetTree(params?: object) {
    return http.get("Prod_MaterialInformationGetTree", params);
  }
};

export { deviceProtectdailyProtectApi };
