<template>
  <div class="main-box" id="648362183041093">
    <TreeFilter
      :default-expand-all="false"
      ref="treeFilter"
      label="name"
      :title="t('deviceProtect.organizationList')"
      :request-api="deviceProtectdailyProtectApi.getProd_MaterialInformationGetTree"
      @change1="handleNodeClick"
    />
    <div class="table-main">
      <SearchForm class="min-h-70px" :columns="searchColumns" :search-param="{}" :search-col="3" :search="handleSearch" :reset="handleReset">
        <template #any>
          <el-button @click="importData" :icon="Document">{{ t("deviceProtect.import") }}</el-button>
        </template>
      </SearchForm>
      <div class="device-container card">
        <!-- 设备分组循环渲染 -->
        <div v-for="deviceType in deviceTypes" :key="deviceType.type" class="device-section">
          <h3 class="section-title">{{ deviceType.name }}</h3>
          <div class="device-row" v-if="deviceType.data.length > 0">
            <div v-for="(item, index) in deviceType.data" :key="`${deviceType.type}-${index}`" class="device-item">
              <el-tooltip placement="right" effect="light" :manual="false" class="device-tooltip">
                <template #default>
                  <div class="device-card">
                    <div class="device-wrapper">
                      <img :src="deviceType.icon" alt="device" class="device-icon" />
                      <img :src="getStatusIcon(item.status)" :alt="item.status" class="status-icon" />
                      <!-- 气缸特有的路由链接 -->
                      <router-link v-if="deviceType.hasLink" to="/mes/device-protect/qigang-jiankong"></router-link>
                    </div>
                    <div class="device-label">{{ item.MatName }}</div>
                    <div class="status-grid">
                      <div class="status-cell" :style="{ backgroundColor: statusColors.alert }">
                        <div>{{ t("deviceProtect.deviceInfo.theoreticalLife") }}</div>
                        <div>{{ item.TheoLife }}{{ item.RunUnit }}</div>
                      </div>
                      <div class="status-cell" :style="{ backgroundColor: statusColors.degradation }">
                        <div>{{ t("deviceProtect.deviceInfo.usedLife") }}</div>
                        <div>{{ item.UsedLife }}{{ item.RunUnit }}</div>
                      </div>
                      <div class="status-cell" :style="{ backgroundColor: statusColors.designLife }">
                        <div>{{ t("deviceProtect.deviceInfo.remainingLife") }}</div>
                        <div>{{ item.RemainingLife }}{{ item.RunUnit }}</div>
                      </div>
                    </div>
                  </div>
                </template>
                <template #content>
                  <div class="tooltip-content">
                    <p>{{ t("deviceProtect.deviceInfo.factory") }}: {{ item.Factory }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.workshop") }}: {{ item.Workshop }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.productionLine") }}: {{ item.ProductionLine }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.materialCode") }}: {{ item.MatCode }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.materialName") }}: {{ item.MatName }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.specModel") }}: {{ item.SpecModel }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.brand") }}: {{ item.Brand }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.singlePartQty") }}: {{ item.SinglePartQty }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.unit") }}: {{ item.Unit }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.parentMaterialCode") }}: {{ item.ParentMatCode }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.parentMaterialName") }}: {{ item.ParentMatName }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.workstation") }}: {{ item.Workstation }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.installTime") }}: {{ item.InstallTime }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.prodRunCountOrDur") }}: {{ item.ProdRunCountOrDur }}{{ item.RunUnit }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.theoreticalLife") }}: {{ item.TheoLife }}{{ item.RunUnit }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.usedLife") }}: {{ item.UsedLife }}{{ item.RunUnit }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.remainingLife") }}: {{ item.RemainingLife }}{{ item.RunUnit }}</p>
                    <p>{{ t("deviceProtect.deviceInfo.remarks") }}: {{ item.Remarks || t("deviceProtect.deviceInfo.noRemarks") }}</p>
                  </div>
                </template>
              </el-tooltip>
            </div>
          </div>
        </div>

        <!-- 预兆告警表格 -->
        <div class="alert-table-section">
          <h3 class="alert-table-header">{{ t("deviceProtect.alertTable.title") }}</h3>
          <el-table :data="alerts" stripe>
            <el-table-column prop="time" :label="t('deviceProtect.alertTable.time')"></el-table-column>
            <el-table-column prop="message" :label="t('deviceProtect.alertTable.message')"></el-table-column>
            <el-table-column :label="t('deviceProtect.alertTable.operation')">
              <template #default="scope">
                <el-button size="small" @click="handleAlert(scope.row)">{{ t("deviceProtect.alertTable.handle") }}</el-button>
                <el-button size="small" @click="ignoreAlert(scope.row)">{{ t("deviceProtect.alertTable.ignore") }}</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="status" :label="t('deviceProtect.alertTable.status')">
              <template #default="scope">
                <div :class="['status-cell', getStatusClass(scope.row.status)]">
                  {{ t(`deviceProtect.alertTable.statusTypes.${getStatusKey(scope.row.status)}`) }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <el-dialog :width="1200" v-model="importDialogVisible" :title="t('deviceProtect.import')">
        <el-upload ref="uploadRef" action="/api/sys/upload/UploadExcelwlxx" :auto-upload="false" :before-upload="beforeUpload">
          <el-button type="primary">{{ t("deviceProtect.selectFile") }}</el-button>
        </el-upload>
        <template #footer>
          <el-button @click="importDialogVisible = false">{{ t("deviceProtect.cancel") }}</el-button>
          <el-button type="primary" @click="startImport">{{ t("deviceProtect.startImport") }}</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { deviceProtectdailyProtectApi, productionReportCapacityApi } from "@/api";
import { Document } from "@element-plus/icons-vue";
import { ref, computed, onMounted } from "vue";
import { useI18n } from "vue-i18n";
import motorIcon from "@/assets/images/motor-icon.jpg";
import cylinderIcon from "@/assets/images/cylinder-icon.jpg";
import mozuIcon from "@/assets/images/模组.jpg";
import qiedaoIcon from "@/assets/images/切刀.jpg";
import errorIcon from "@/assets/images/error-icon.png";
import warningIcon from "@/assets/images/warning-icon.png";
import checkIcon from "@/assets/images/check-icon.png";
import { clearObjectValues } from "@/utils";

const { t } = useI18n();

const result = ref([]);

const getData = async (obj = initParam.value) => {
  try {
    const { data } = await productionReportCapacityApi.getListMesReportData(obj);
    result.value = data.list.map(item => {
      if (item.RemainingLife <= 0) {
        item.status = "error";
      } else if (item.RemainingLife < item.TheoLife * 0.1) {
        item.status = "warning";
      } else {
        item.status = "success";
      }
      return item;
    });
    console.log(result.value, "res");
  } catch (error) {
    console.error("获取数据失败:", error);
  }
};

const handleSearch = () => {
  getData({ ...initParam.value, deck: initParam.value.staion });
};

onMounted(async () => {
  getData();
});

// 设备类型配置
const deviceTypes = computed(() => [
  { type: 1, name: t("deviceProtect.deviceTypes.motor"), icon: motorIcon, hasLink: false, data: result.value.filter(item => item.Type === 1) },
  { type: 2, name: t("deviceProtect.deviceTypes.cylinder"), icon: cylinderIcon, hasLink: true, data: result.value.filter(item => item.Type === 2) },
  { type: 3, name: t("deviceProtect.deviceTypes.module"), icon: mozuIcon, hasLink: false, data: result.value.filter(item => item.Type === 3) },
  { type: 4, name: t("deviceProtect.deviceTypes.cutter"), icon: qiedaoIcon, hasLink: false, data: result.value.filter(item => item.Type === 4) }
]);

const importDialogVisible = ref(false);
const uploadRef = ref(null);

const startImport = () => {
  uploadRef.value!.submit();
  importDialogVisible.value = false;
  treeFilter.value?.refresh();
  getData();
  ElMessage.success(t("deviceProtect.importSuccess"));
};

const treeFilter = ref();
const importData = () => {
  importDialogVisible.value = true;
};

const alerts = ref([
  { time: "2022-01-20 13:30", message: "告警：气缸3动作超时", status: "待处理" },
  { time: "2023-10-25 01:13", message: "告警：气缸4超过使用次数", status: "待处理" },
  { time: "2023-11-05 09:01", message: "告警：极端3动作超时", status: "待处理" },
  { time: "2022-09-09 20:47", message: "告警：极端4真空值过低", status: "待处理" },
  { time: "2023-10-25 16:49", message: "注意：气缸1退化", status: "待处理" },
  { time: "2022-10-06 04:04", message: "注意：极端2真空值退化阈值", status: "处理中" },
  { time: "2023-02-16 22:26", message: "注意：吸嘴电机特征3超出阈值", status: "处理中" },
  { time: "2023-07-04 09:20", message: "注意：吸嘴电机特征3超出阈值", status: "处理中" },
  { time: "2023-11-22 19:23", message: "注意：极片裁切电机特征1超出阈值", status: "已完成" },
  { time: "2022-11-19 16:27", message: "注意：极片裁切电机特征1超出阈值", status: "已完成" }
]);

const initParam = ref({
  Type: 441
});

const handleNodeClick = async (nodeObj: any) => {
  const initParam1: any = {};
  console.log(nodeObj, "nodeObj");

  let currentNode = nodeObj;
  while (currentNode) {
    const nodeData = currentNode.data;
    if (nodeData && nodeData.type && nodeData.name) {
      initParam1[nodeData.type] = nodeData.name;
    }
    currentNode = currentNode.parent;
  }

  const obj = clearObjectValues(initParam.value);
  initParam.value = { ...obj, ...initParam1, Type: initParam.value.Type };
  getData({ ...initParam.value, deck: initParam.value.staion });
};

// 状态颜色映射
const statusColors = {
  alert: "green",
  degradation: "#0000ff",
  designLife: "red"
};

// 获取状态图标
const getStatusIcon = (status: string) => {
  switch (status) {
    case "success":
      return checkIcon;
    case "warning":
      return warningIcon;
    case "error":
      return errorIcon;
    default:
      return "";
  }
};

// 处理告警
const handleAlert = (item: any) => {
  console.log("处理告警:", item);
};

// 忽略告警
const ignoreAlert = (item: any) => {
  console.log("忽略告警:", item);
};

// 获取状态类名
const getStatusClass = (status: string) => {
  switch (status) {
    case "待处理":
      return "pending";
    case "处理中":
      return "processing";
    case "已完成":
      return "completed";
    default:
      return "";
  }
};

// 获取状态键值
const getStatusKey = (status: string) => {
  switch (status) {
    case "待处理":
      return "pending";
    case "处理中":
      return "processing";
    case "已完成":
      return "completed";
    default:
      return "";
  }
};
</script>

<style scoped>
:deep(.filter) {
  width: auto !important;
}

/* 整体背景 */
.device-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  width: 100%;

  /* min-height: 100vh; */
  padding: 20px;
  margin: 0 auto;
}

/* 设备卡片样式 */
.device-card {
  width: 150px;
  padding: 15px;
  text-align: center;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-color: var(--el-text-color-regular);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
  transition: box-shadow 0.3s ease;
}
.device-card:hover {
  box-shadow: 0 4px 8px rgb(0 0 0 / 20%);
}
.device-section {
  display: flex;
  flex-flow: row wrap;
  gap: 10px;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.device-row {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(170px, 1fr));
  gap: 13px;
  justify-items: center; /* 水平居中 */
  width: 100%;
}
.device-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.device-wrapper {
  position: relative;
  display: inline-block;
}
.device-icon {
  width: 80px;
  height: 80px;
}
.status-icon {
  position: absolute;
  top: -10px;
  right: -10px;
  z-index: 10;
  width: 24px;
  height: 24px;
}
.device-label {
  margin-top: 10px;
  font-size: 14px;
  color: var(--el-text-color-regular);
}
.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 5px;
  width: 100%;
  margin-top: 10px;
}
.status-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5px;
  font-size: 10px;
  color: #ffffff;
  text-align: center;
  border-radius: 4px;
}
.alert-table-section {
  width: 100%;
  margin-top: 20px;
}
.alert-table-header {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-regular);
}

/* 微调 ElementPlus 表格样式 */
.el-table {
  overflow: hidden;
  border-radius: 8px;
}
.el-table th {
  color: var(--el-text-color-regular);
  background-color: #e9f1f7;
}
.status-cell.pending {
  background-color: #ff0000;
}
.status-cell.processing {
  background-color: #ff8000;
}
.status-cell.completed {
  background-color: #00ff00;
}

/* 新增提示框样式 */
.tooltip-content p {
  margin: 6px 0;
  line-height: 1.5;
}

/* 新增 section 标题样式 */
.section-title {
  width: 100%;
  padding-bottom: 10px;
  margin: 20px 0 10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-regular);
  border-bottom: 2px solid #e9f1f7;
}
</style>
