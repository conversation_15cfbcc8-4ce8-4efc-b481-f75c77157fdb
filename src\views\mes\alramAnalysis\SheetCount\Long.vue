<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table']"
    @data-loaded="handleDataLoaded"
    :machines="machines"
    @export-data="handleExportData"
    chart-height="220px"
    ref="userTable"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import moment from "moment";
import { ElMessage } from "element-plus";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

// 定义emits事件类型
const emits = defineEmits(["dataReady", "exportData"]);
const props = defineProps({
  machines: {
    type: Array as PropType<Array<{ id: string; name: string }>>,
    default: () => []
  }
});
// const machines = ref<any[]>([]);
// const handleExportData = (csvContent: string) => {
//   emits("exportData", { data: csvContent });
// };
// const handleExportData = () => {
//   try {
//     // 提取图表数据
//     const categories = chartOptions.value.xAxis.data; // X轴数据
//     // const seriesData = chartOptions.value.series[0].data.map(item => item.value); // Y轴数据
//     const seriesData = chartOptions.value.series[0]?.data.map(item => item.value) || []; // Y轴数据
//     // 构造二维数组
//     const data = [["时间", "故障时长"]]; // 表头
//     categories.forEach((category, index) => {
//       data.push([category, seriesData[index]]);
//     });

//     // 转换为 CSV 字符串
//     const csvContent = data.map(row => row.join(",")).join("\n");

//     // 触发父组件事件
//     emits("exportData", { data: csvContent });
//   } catch (error) {
//     console.error("导出失败:", error);
//     ElMessage.error("导出失败，请查看控制台错误信息！");
//   }
// };

// 颜色配置
const COLORS = {
  fault_duration: "#00bfff",
  font: "#666",
  splitLine: "#eee"
};

// 存储图表数据的响应式变量
const chartData = ref<{ categories: string[]; seriesData: number[] }>({ categories: [], seriesData: [] });

// 图表配置
const chartOptions = computed(() => ({
  title: {
    subtext: t("sheetCount.faultDurationStats"),
    left: "center",
    textStyle: {
      fontSize: 16,
      color: COLORS.font
    },
    top: -12
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["bar", "line"],
        title: {
          bar: t("alarmRealtime.time.switchToBar"),
          line: t("alarmRealtime.time.switchToLine")
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" },
    formatter: (params: any) => {
      const fault_duration = params[0];
      return `
        <div style="padding:5px;min-width:120px">
          <div>
            <span style="display:inline-block;width:10px;height:10px;background:${COLORS.fault_duration};border-radius:50%"></span>
            ${t("common.time")}: ${fault_duration.name}<br/>
            ${t("alarmRealtime.long.duration")}: ${fault_duration.value}
          </div>
        </div>
      `;
    }
  },
  xAxis: {
    type: "category",
    data: chartData.value.categories,
    axisLabel: {
      color: COLORS.font,
      interval: 0,
      rotate: 45,
      formatter: (value: string) => {
        if (value.includes("-")) {
          return value.split(" - ").join("\n");
        }
        return value;
      }
    }
  },
  yAxis: {
    type: "value",
    name: t("alarmRealtime.long.durationMinutes"),
    min: 0,
    axisLabel: {
      color: COLORS.font
    },
    splitLine: { lineStyle: { color: COLORS.splitLine } }
  },
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true
  },
  series: [
    {
      name: t("alarmRealtime.long.duration"),
      type: "bar",
      data: chartData.value.seriesData,
      itemStyle: { color: COLORS.fault_duration },
      label: {
        show: true,
        position: "top",
        formatter: "{c}"
      }
    }
  ]
}));
const fetchData = async (params: { time: Date[]; machine?: string }): Promise<{ data: { categories: string[]; seriesData: number[] } }> => {
  try {
    const startTime = moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss.SSS");
    const endTime = moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss");

    const queryParams = {
      Deck: params.machine || (props.machines.length > 0 ? props.machines[0].id : ""),
      StartDate: startTime,
      EndDate: endTime,
      Type: 10
    };

    const response = await alramAnalysisApi.getListMesReportData(queryParams);
    const timeType = response.data.list.length > 0 && response.data.list[0].type ? response.data.list[0].type : "Hour";

    const data1 = transformData(response.data.list, timeType);

    const exposedata = {
      categories: data1.categories,
      seriesData: data1.seriesData,
      timeType
    };
    emits("dataReady", exposedata);

    return {
      data: {
        categories: data1.categories,
        seriesData: data1.seriesData
      }
    };
  } catch (error) {
    if ((error as any).name === "CanceledError") {
      console.log("请求已取消");
    } else {
      ElMessage.error(t("common.message.operationFailed"));
      console.error("获取故障时长数据失败:", error);
    }
    return {
      data: {
        categories: [],
        seriesData: []
      }
    };
  }
};
// 转换数据函数
const transformData = (
  responseData: any[],
  timeType: string
): {
  categories: string[];
  seriesData: number[];
} => {
  const timeMap = new Map();
  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Mon":
      formatPattern = "MM-DD";
      break;
    case "Year":
      formatPattern = "YYYY-MM";
      break;
    default:
      formatPattern = "HH:00";
  }
  responseData.forEach(item => {
    const timeKey = moment(item.start_time).format(formatPattern);
    const duration = item.fault_duration || 0;

    if (!timeMap.has(timeKey)) {
      timeMap.set(timeKey, {
        time: timeKey,
        duration: 0,
        machines: new Set()
      });
    }

    const timeData = timeMap.get(timeKey);
    timeData.duration += duration;
  });

  const sortedTimes = Array.from(timeMap.entries()).sort((a, b) => moment(a[0], formatPattern).valueOf() - moment(b[0], formatPattern).valueOf());

  return {
    categories: sortedTimes.map(item => item[0]),
    seriesData: sortedTimes.map(item => item[1].duration)
  };
};
// 数据获取函数
// let currentController: AbortController | null = null;
const handleDataLoaded = (data: { categories: string[]; seriesData: number[] }) => {
  chartData.value = data; // 更新 chartData
};

// 导出数据
const handleExportData = () => {
  const categories = chartData.value.categories;
  const seriesData = chartData.value.seriesData;

  const data = [[t("common.time"), t("alarmRealtime.long.duration")]];
  categories.forEach((category, index) => {
    data.push([category, seriesData[index]]);
  });

  const csvContent = data.map(row => row.join(",")).join("\n");
  emits("exportData", { data: csvContent });
};
const userTable = ref(null);

defineExpose({
  tableRef: userTable
});
onMounted(async () => {
  // await fetchMachines();
});
onUnmounted(() => {
  // if (currentController) {
  //   currentController.abort();
  // }
});
</script>
