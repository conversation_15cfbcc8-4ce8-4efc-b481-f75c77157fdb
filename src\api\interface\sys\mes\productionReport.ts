export namespace mesProductionReport {
  export interface ReportQuery {
    Deck?: string;
    StartDate?: string; // 假设日期格式为 ISO 字符串，例如 "2023-03-15T00:00:00Z"
    EndDate?: string; // 同上
    Type?: number; // 1: 产能; 2: 质量; 3: 能耗
  }

  /** 计划标准配置 */
  export interface PlanStandardConfig {
    /** 机台名称 */
    machine?: string;
    /** PPM值 */
    ppm: number;
    /** 每天运行时长（小时） */
    dailyRunningHours: number;
    /** 日期 */
    date?: string;
  }
}
