<template>
  <div class="main-box" id="651546396528709">
    <TreeFilter
      :default-expand-all="false"
      ref="treeFilter"
      label="name"
      :title="t('alarmHistory.organizationList')"
      :request-api="alramAnalysisApi.getTree"
      @change1="handleNodeClick"
    >
      <template #shit>
        <CalendarCustom current-theme="light" :header-title="title" :date-data="dateData"> </CalendarCustom>
      </template>
    </TreeFilter>
    <div class="table-box">
      <ProTable v-if="!isChart" ref="proTableRef" :init-param="initParam" :columns="columns" :request-api="fetchFilteredData">
        <template #tableHeader>
          <el-button type="primary" @click="exportData">{{ t("alarmHistory.export") }}</el-button>
        </template>
        <template #operation="{ row }">
          <el-button type="info" size="small" @click="openViewDialog(row)">{{ t("alarmHistory.view") }}</el-button>
        </template>
        <template #toolButton>
          <el-tooltip content="切换表格" placement="top">
            <el-button :icon="Switch" circle @click="changeTable" />
          </el-tooltip>
        </template>
      </ProTable>
      <SwitchChart :init-params="switchchartData" :current-tree-type="currentTreeType" v-else @toggle-view="changeTable"></SwitchChart>
      <el-dialog width="1200" v-model="dialogVisible" :title="dialogTitle">
        <el-form :model="formData" ref="formRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('alarmHistory.form.factory')">
                <el-input v-model="formData.factory" :disabled="isView"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('alarmHistory.form.workshop')">
                <el-input v-model="formData.workshop" :disabled="isView"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('alarmHistory.form.workline')">
                <el-input v-model="formData.workline" :disabled="isView"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('alarmHistory.form.deck')">
                <el-input v-model="formData.deck" :disabled="isView"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('alarmHistory.form.station')">
                <el-input v-model="formData.station" :disabled="isView"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('alarmHistory.form.faultType')">
                <el-input v-model="formData.fault_type" :disabled="isView"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('alarmHistory.form.faultName')">
                <el-input v-model="formData.fault_name" :disabled="isView"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('alarmHistory.form.alarmInfo')">
                <el-input v-model="formData.alarm_info" :disabled="isView"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('alarmHistory.form.startTime')">
                <el-date-picker
                  v-model="formData.start_time"
                  type="datetime"
                  :placeholder="t('alarmHistory.form.startTime')"
                  :disabled="isView"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('alarmHistory.form.durationMinutes')">
                <el-input-number v-model="formData.duration_minutes" :disabled="isView" min="0"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">{{ t("alarmHistory.close") }}</el-button>
          <el-button v-if="!isView" type="primary" @click="saveData">{{ t("alarmHistory.save") }}</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts" name="历史故障记录">
import { ElMessage } from "element-plus";
import { productionReportCapacityApi, alramAnalysisApi } from "@/api";
import * as XLSX from "xlsx";
import moment from "moment";
import { Switch } from "@element-plus/icons-vue";
import SwitchChart from "./SwitchChart.vue";
import { convertCalendarData } from "@/utils";
import { useI18n } from "vue-i18n";
import { treeArr } from "@/config";

const { t } = useI18n();

const title = ref(t("alarmHistory.title"));
const dateData = ref([]);
const proTableRef = ref(null);
const initParam = ref({
  Type: 9
});
const exportParam = ref({});
const dialogVisible = ref(false);
const dialogType = ref<"add" | "edit" | "view">("add");
const formData = reactive({
  id: 0,
  factory: "",
  workshop: "",
  workline: "",
  deck: "",
  station: "",
  fault_type: "",
  fault_name: "",
  alarm_info: "",
  start_time: null,
  duration_minutes: 0
});
const formRef = ref(null);
// const selectedRows = ref([]);
const isView = computed(() => dialogType.value === "view");
const dialogTitle = computed(() => {
  switch (dialogType.value) {
    case "add":
      return t("alarmHistory.dialog.add");
    case "edit":
      return t("alarmHistory.dialog.edit");
    case "view":
      return t("alarmHistory.dialog.view");
    default:
      return "";
  }
});

const columns = computed(() => [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "id", label: t("alarmHistory.table.id"), isShow: false },
  {
    prop: "time",
    label: t("alarmHistory.table.time"),
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": "Start date",
        "end-placeholder": "End date"
      },
      defaultValue: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")]
    },
    isShow: false
  },
  {
    prop: "factory",
    label: t("alarmHistory.table.factory"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "workshop",
    label: t("alarmHistory.table.workshop"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "workline",
    label: t("alarmHistory.table.workline"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "deck",
    label: t("alarmHistory.table.deck"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "station",
    label: t("alarmHistory.table.station"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "fault_type",
    label: t("alarmHistory.table.faultType"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "fault_name",
    label: t("alarmHistory.table.faultName"),
    width: 200,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "alarm_info",
    label: t("alarmHistory.table.alarmInfo"),
    width: 300,
    align: "left"
    // search: { el: "input" }
  },
  {
    prop: "start_time",
    label: t("alarmHistory.table.startTime"),
    width: 180,
    align: "center"
    // search: {
    //   el: "date-picker",
    //   props: { type: "datetime" }
    // }
  },
  {
    prop: "duration_minutes",
    label: t("alarmHistory.table.durationMinutes"),
    width: 150,
    align: "center"
    // search: { el: "input-number" }
  },
  { prop: "operation", label: t("alarmHistory.table.operation"), width: 200, fixed: "right" }
]);
const isChart = ref(false);
const changeTable = () => {
  isChart.value = !isChart.value;
};
const fetchData = async (params: any) => {
  const time = {
    StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
    EndDate: moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString()
  };
  delete params.time;
  const query = {
    ...time,
    Type: params.Type,
    IsPage: true
  };
  return productionReportCapacityApi.getListMesReportData({ ...params, ...query });
};

const fetchFilteredData = async (params: any) => {
  exportParam.value = params;
  const response = await fetchData(params);
  exportParam.value.pageSize = response.data.total;
  return response;
};

const openViewDialog = (row: any) => {
  dialogType.value = "view";
  Object.assign(formData, row);
  dialogVisible.value = true;
};

interface TableResponse {
  data: {
    list: any[];
    total: number;
  };
}

const exportData = async () => {
  try {
    if (!proTableRef.value) return;

    const params = {
      ...proTableRef.value.searchParam,
      ...exportParam.value,
      page: 1,
      pageSize: 1000
    };

    const response = (await fetchFilteredData(params)) as TableResponse;
    const tableData = response.data.list;

    const exportColumns = columns.value.filter(col => !["selection", "operation", "id", "time"].includes(col.type || col.prop));
    const headers = exportColumns.map(col => col.label);

    const dataRows = tableData.map(item => {
      return exportColumns.map(col => {
        if (col.prop === "start_time" && item[col.prop]) {
          return new Date(item[col.prop]).toLocaleString();
        }
        return item[col.prop] || "";
      });
    });

    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...dataRows]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "故障记录");

    const exportDate = new Date().toISOString().slice(0, 10);
    XLSX.writeFile(workbook, `设备故障记录_${exportDate}.xlsx`);

    ElMessage.success(t("alarmHistory.export.success"));
  } catch (error: any) {
    ElMessage.error(`${t("alarmHistory.export.error")}: ${error.message}`);
  }
};
const currentTreeType = ref<any>(0);

const switchchartData = ref({});
/**
 * 处理树节点点击事件
 * @param nodeObj 点击的节点对象
 */
const handleNodeClick = (nodeObj: any) => {
  currentTreeType.value = nodeObj.level;

  const nodeParams: Record<string, string> = {};
  let currentNode = nodeObj;

  if (!isChart.value && proTableRef.value) {
    treeArr.forEach(key => delete proTableRef.value!.searchParam[key]);
  }

  while (currentNode) {
    const nodeData = currentNode.data;
    if (nodeData?.type && nodeData?.name) {
      nodeParams[nodeData.type] = nodeData.name;

      if (proTableRef.value) {
        proTableRef.value.searchParam[nodeData.type] = nodeData.name;
      }
    }
    currentNode = currentNode.parent;
  }
  switchchartData.value = nodeParams;
  // 根据模式执行相应操作
  if (!isChart.value) {
    proTableRef.value.search();
  }
};

onMounted(async () => {
  // proTableRef.value.getTableList();
  const query = {
    Type: 21,
    IsPage: false
  };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);
  dateData.value = convertCalendarData(data.list);
});
</script>

<style scoped>
:deep(.filter) {
  width: auto !important;
}

/* 保持原有样式不变 */
.custom-tree {
  width: 20%;
  padding: 10px;
  margin-right: 20px;
  overflow-y: auto;
  background-color: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}
.custom-tree .el-tree-node__content {
  height: 32px;
  padding: 0 8px;
  font-size: 14px;
  line-height: 32px;
  color: #333333;
  border-radius: 4px;
  transition: background-color 0.3s;
}
.custom-tree .el-tree-node__content:hover {
  background-color: #eef1f6;
}
.custom-tree .el-tree-node.is-current > .el-tree-node__content {
  color: #ffffff;
  background-color: #409eff;
}
.custom-tree .el-tree-node__expand-icon {
  font-size: 12px;
  color: #909399;
}
.custom-tree .el-tree-node__expand-icon.expanded {
  transition: transform 0.3s;
  transform: rotate(90deg);
}
</style>
