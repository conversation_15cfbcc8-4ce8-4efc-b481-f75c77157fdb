<template>
  <div class="environment-monitor p-2" v-if="monitorData.length > 0">
    <div class="data-display">
      <div class="data-item">
        <i class="icon temperature-icon"></i>
        <span class="value">{{ monitorData[0].Temperature }} °C</span>
      </div>
      <div class="data-item">
        <i class="icon humidity-icon"></i>
        <span class="value">{{ monitorData[0].Humidity }} %</span>
      </div>
      <div class="data-item">
        <i class="icon particles-icon"></i>
        <span class="value">{{ monitorData[0].PplValue }} PPL</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from "vue";

defineProps({
  monitorData: {
    type: Array,
    default: () => []
  }
});
</script>

<style scoped>
.environment-monitor {
  text-align: center;
  border-radius: 8px;
}
.data-display {
  display: flex;
  justify-content: space-around;
}
.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.icon {
  margin-bottom: 10px;
  font-size: 35px;
}
.temperature-icon::before {
  color: #00ff00;
  content: "🌡️";
}
.humidity-icon::before {
  color: #00bfff;
  content: "💧";
}
.particles-icon::before {
  color: #ffa500;
  content: "🌫️";
}
.value {
  font-size: 30px;
  font-weight: bold;
}
</style>
