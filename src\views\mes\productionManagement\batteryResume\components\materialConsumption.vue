<!-- 
 * @Description: 物料消耗详情
 * @Date: 2024-08-11
!-->
<template>
  <el-dialog v-model="visible" title="物料消耗" width="80%" :destroy-on-close="true" class="material-consumption-dialog">
    <div class="material-consumption-detail">
      <!-- 表格 -->
      <div class="table-box">
        <el-table v-loading="loading" :data="tableData" border style="width: 100%" height="calc(100vh - 300px)">
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column prop="batchNo" label="物料批次号" min-width="120" />
          <el-table-column prop="batteryCode" label="电芯码/电芯码" min-width="180" />
          <el-table-column prop="materialCode" label="物料编码" min-width="120" />
          <el-table-column prop="materialName" label="物料名称" min-width="180" />
          <el-table-column prop="consumeQty" label="消耗数量" min-width="100" align="right" />
          <el-table-column prop="unit" label="单位" min-width="80" align="center" />
          <el-table-column prop="supplierCode" label="供应商编码" min-width="120" />
          <el-table-column prop="supplierName" label="供应商名称" min-width="200" />
          <el-table-column prop="createTime" label="创建时间" min-width="150" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-box">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";

// 控制对话框显示
const visible = ref(false);
const loading = ref(false);

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 表格数据
const tableData = ref<any[]>([]);

// 打开弹窗方法
const open = (row?: any) => {
  visible.value = true;
  // 根据传入的行信息查询物料消耗数据
  fetchMaterialConsumptionData(row);
};

// 获取物料消耗数据
const fetchMaterialConsumptionData = () => {
  loading.value = true;
  // 这里应替换为真实API调用
  setTimeout(() => {
    // 模拟数据
    tableData.value = [
      {
        id: "1",
        batchNo: "10011100012#720003",
        batteryCode: "71000310260301129-8511",
        materialCode: "10011100012",
        materialName: "正极 PET T0.03*W25mm*200mm",
        consumeQty: "0.00448",
        unit: "μa",
        supplierCode: "72000333",
        supplierName: "昆山市德馨电子有限公司",
        createTime: "2025-04-10 14:33:08"
      },
      {
        id: "2",
        batchNo: "10010400071#000012",
        batteryCode: "71000310260301129-8511",
        materialCode: "10010400071",
        materialName: "梯形正极铝箔（2+7+1.5+2）*251mm",
        consumeQty: "0.5538",
        unit: "M2",
        supplierCode: "00001200",
        supplierName: "哈尔滨电控继电器有限责任公司",
        createTime: "2025-04-10 14:33:08"
      },
      {
        id: "3",
        batchNo: "10011100011#710003",
        batteryCode: "71000310260301129-8511",
        materialCode: "10011100011",
        materialName: "二维码胶带 Pet T0.03*W25mm*200m",
        consumeQty: "0.00015",
        unit: "μa",
        supplierCode: "71000310",
        supplierName: "广东新之源技术有限公司",
        createTime: "2025-04-10 14:33:08"
      },
      {
        id: "4",
        batchNo: "A050250407531112",
        batteryCode: "71000310260301129-8511",
        materialCode: "2001140001",
        materialName: "热压极电极卷-162Ah",
        consumeQty: "1",
        unit: "PC",
        supplierCode: "--",
        supplierName: "--",
        createTime: "2025-04-10 14:33:08"
      },
      {
        id: "5",
        batchNo: "C050250404291123",
        batteryCode: "71000310260301129-8511",
        materialCode: "2001000001",
        materialName: "正极电极卷-162Ah",
        consumeQty: "1",
        unit: "PC",
        supplierCode: "--",
        supplierName: "--",
        createTime: "2025-04-10 14:33:08"
      }
    ];
    pagination.total = 5; // 模拟总条数
    loading.value = false;
  }, 300);
};

// 分页相关方法
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  // 这里应该重新请求数据
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  // 这里应该重新请求数据
};

// 向外暴露方法
defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.material-consumption-detail {
  .table-box {
    .el-table {
      margin-bottom: 16px;
    }
  }
  .pagination-box {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }
}
.material-consumption-dialog {
  :deep(.el-dialog__body) {
    padding: 16px;
  }
}
</style>
