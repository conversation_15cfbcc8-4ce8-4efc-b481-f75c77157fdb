<template>
  <router-link to="/mes/alram-analysis/sheet-count">
    <div class="dashboard-charts">
      <!-- 故障类型最多次数图表 -->
      <div class="chart-container glow-effect" @mouseenter="handleChartHover('type')" @mouseleave="handleChartLeave('type')">
        <div class="chart-title">{{ $t("chart.faultTypeCount") }}</div>
        <v-chart ref="typeChartRef" :autoresize="true" :option="typeChartOptions" :theme="chartTheme" class="chart-instance" />
      </div>

      <!-- 故障次数统计图表 -->
      <div class="chart-container glow-effect" @mouseenter="handleChartHover('count')" @mouseleave="handleChartLeave('count')">
        <div class="chart-title">{{ $t("chart.faultCount") }}</div>
        <v-chart ref="countChartRef" :autoresize="true" :option="countChartOptions" :theme="chartTheme" class="chart-instance" />
      </div>

      <!-- 故障率分析图表 -->
      <div class="chart-container glow-effect" @mouseenter="handleChartHover('rate')" @mouseleave="handleChartLeave('rate')">
        <div class="chart-title">{{ $t("chart.faultRate") }}</div>
        <v-chart ref="rateChartRef" :autoresize="true" :option="rateChartOptions" :theme="chartTheme" class="chart-instance" />
      </div>
    </div>
  </router-link>
</template>

<script setup lang="ts">
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { BarChart } from "echarts/charts";
import { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from "echarts/components";
// import { debounce } from "lodash-es";
import { graphic } from "echarts/core";
import { useI18n } from "vue-i18n";

use([CanvasRenderer, BarChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent]);

const i18n = useI18n();

// 定义props
const props = defineProps({
  data: {
    type: Object,
    required: true,
    validator: value => {
      return (
        "类型次数" in value &&
        "次数统计" in value &&
        "故障率" in value &&
        value.类型次数.every(item => "machine" in item && "fault_type" in item && "fault_count" in item) &&
        value.次数统计.every(item => "machine" in item && "fault_count" in item) &&
        value.故障率.every(item => "machine" in item && "failure_rate" in item)
      );
    }
  },
  theme: {
    type: String,
    default: "dark"
  }
});

// 图表引用
const typeChartRef = ref();
const countChartRef = ref();
const rateChartRef = ref();

// 图表主题
const chartTheme = computed(() => (props.theme === "dark" ? "dark" : "light"));

// 颜色方案（保留原有逻辑，优化随机色生成）
const getRandomColor = () =>
  new graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "#3B7BFF" },
    { offset: 1, color: "#00D1FF" }
  ]);

const COLOR_PALETTE = {
  dark: {
    primary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: getRandomColor() },
      { offset: 1, color: getRandomColor() }
    ]),
    secondary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#FF7EB3" },
      { offset: 1, color: "#FF758C" }
    ]),
    background: "rgba(8, 18, 44, 0.8)",
    font: "#E1F3FF",
    axisLine: "rgba(200, 200, 255, 0.2)",
    splitLine: "rgba(100, 150, 255, 0.1)"
  },
  light: {
    primary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#4285F4" },
      { offset: 1, color: "#34A853" }
    ]),
    secondary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#EA4335" },
      { offset: 1, color: "#FBBC05" }
    ]),
    background: "rgba(255, 255, 255, 0.9)",
    font: "#333333",
    axisLine: "rgba(0, 0, 0, 0.1)",
    splitLine: "rgba(0, 0, 0, 0.05)"
  }
};

const colors = computed(() => COLOR_PALETTE[chartTheme.value]);

// 处理后的数据
const sortedTypeData = computed(() => (props.data.类型次数 ? [...props.data.类型次数].sort((a, b) => b.fault_count - a.fault_count) : []));

const sortedCountData = computed(() => (props.data.次数统计 ? [...props.data.次数统计].sort((a, b) => b.fault_count - a.fault_count) : []));

const sortedRateData = computed(() => (props.data.故障率 ? [...props.data.故障率].sort((a, b) => b.failure_rate - a.failure_rate) : []));

// 图表配置 - 类型次数图表
const typeChartOptions = computed(() => ({
  backgroundColor: "transparent",
  grid: { top: "25%", left: "8%", right: "4%", bottom: "5%", containLabel: true },
  tooltip: {
    trigger: "axis",
    backgroundColor: "rgba(0, 30, 80, 0.9)",
    padding: [10, 15],
    textStyle: { color: colors.value.font, fontSize: 14 },
    formatter: params => {
      const data = sortedTypeData.value[params[0].dataIndex];
      return `
        <div style="font-weight:600;margin-bottom:6px;font-size:16px">${data.machine}</div>
        <div style="display:flex;align-items:center">
          <span style="background:${params[0].color};width:12px;height:12px;border-radius:2px;display:inline-block;margin-right:8px"></span>
          <span>${i18n.t("chart.faultType")}：${data.fault_type}</span>
          <span style="font-weight:600;margin-left:20px">${i18n.t("chart.count")}：${data.fault_count}</span>
        </div>
      `;
    }
  },
  xAxis: {
    type: "category",
    data: sortedTypeData.value.map(item => item.machine),
    axisLabel: { color: colors.value.font, fontSize: 12, rotate: 30 }
  },
  yAxis: {
    type: "value",
    name: i18n.t("chart.faultCountUnit"),
    nameTextStyle: {
      color: colors.value.font,
      padding: [0, 0, 0, 0],
      fontSize: 12
    },
    nameGap: 30,
    nameLocation: "end",
    axisLabel: { color: colors.value.font, margin: 12 }
  },
  series: [
    {
      type: "bar",
      barWidth: "45%",
      data: sortedTypeData.value.map(item => ({
        value: item.fault_count,
        itemStyle: { color: getRandomColor(), borderRadius: 6 }
      })),
      label: {
        show: true,
        position: "top",
        color: colors.value.font,
        fontSize: 11,
        distance: 8,
        formatter: params => {
          // params包含dataIndex，用于获取原始数据
          const dataItem = sortedTypeData.value[params.dataIndex];
          return `${dataItem.fault_type}\n${dataItem.fault_count}`;
        }
      }
    }
  ]
}));

// 图表配置 - 次数统计图表
const countChartOptions = computed(() => ({
  backgroundColor: "transparent",
  grid: { top: "20%", left: "3%", right: "4%", bottom: "5%", containLabel: true },
  tooltip: {
    trigger: "axis",
    backgroundColor: "rgba(0, 30, 80, 0.9)",
    padding: [10, 15],
    textStyle: { color: colors.value.font, fontSize: 14 },
    formatter: params => `
      <div>${params[0].name}</div>
      <div>${i18n.t("chart.faultCountUnit")}：${params[0].value}</div>
    `
  },
  xAxis: {
    type: "category",
    data: sortedCountData.value.map(item => item.machine),
    axisLabel: { color: colors.value.font, fontSize: 12, rotate: 30 }
  },
  yAxis: {
    type: "value",
    name: i18n.t("chart.faultCountUnit"),
    nameTextStyle: { color: colors.value.font, padding: [0, 0, 0, 40] },
    axisLabel: { color: colors.value.font }
  },
  series: [
    {
      type: "bar",
      barWidth: "45%",
      data: sortedCountData.value.map(item => ({
        value: item.fault_count,
        itemStyle: { color: getRandomColor(), borderRadius: 6 }
      })),
      label: { show: true, position: "top", color: colors.value.font, fontSize: 12 }
    }
  ]
}));

// 图表配置 - 故障率图表
const rateChartOptions = computed(() => ({
  backgroundColor: "transparent",
  grid: { top: "20%", left: "3%", right: "4%", bottom: "5%", containLabel: true },
  tooltip: {
    trigger: "axis",
    backgroundColor: "rgba(0, 30, 80, 0.9)",
    padding: [10, 15],
    textStyle: { color: colors.value.font, fontSize: 14 },
    formatter: params => `
      <div>${params[0].name}</div>
      <div>${i18n.t("chart.faultRateUnit")}：${(params[0].value * 100).toFixed(2)}%</div>
    `
  },
  xAxis: {
    type: "category",
    data: sortedRateData.value.map(item => item.machine),
    axisLabel: { color: colors.value.font, fontSize: 12, rotate: 30 }
  },
  yAxis: {
    type: "value",
    name: i18n.t("chart.faultRateUnit"),
    nameTextStyle: { color: colors.value.font, padding: [0, 0, 0, 40] },
    axisLabel: { color: colors.value.font, formatter: "{value}%" }
  },
  series: [
    {
      type: "bar",
      barWidth: "45%",
      data: sortedRateData.value.map(item => ({
        value: item.failure_rate,
        itemStyle: { color: getRandomColor(), borderRadius: 6 }
      })),
      label: {
        show: true,
        position: "top",
        color: colors.value.font,
        fontSize: 12,
        formatter: params => {
          const dataItem = sortedRateData.value[params.dataIndex];

          return `${dataItem.failure_rate.toFixed(2)}%`;
        }
      }
    }
  ]
}));

// 图表交互
const handleChartHover = type => {
  const chart = type === "rate" ? rateChartRef.value : type === "count" ? countChartRef.value : typeChartRef.value;
  chart?.dispatchAction({ type: "highlight", seriesIndex: 0 });
};

const handleChartLeave = type => {
  const chart = type === "rate" ? rateChartRef.value : type === "count" ? countChartRef.value : typeChartRef.value;
  chart?.dispatchAction({ type: "downplay", seriesIndex: 0 });
};

// 响应式调整
// const handleResize = debounce(() => {
//   typeChartRef.value?.resize();
//   countChartRef.value?.resize();
//   rateChartRef.value?.resize();
// }, 300);

// onMounted(() => {
//   window.addEventListener("resize", handleResize);
// });

defineExpose({
  typeChartRef,
  countChartRef,
  rateChartRef
  // handleResize
});
</script>

<style scoped lang="scss">
// 保持原有样式不变，仅调整图表标题对应关系
.dashboard-charts {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  width: 100%;
  height: 100%;
}
.chart-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background: rgb(255 255 255 / 5%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 8px 25px rgb(0 180 255 / 30%);
    transform: translateY(-5px);
    &::before {
      left: 100%;
    }
  }
  &::before {
    position: absolute;
    top: 0;
    left: -100%;
    z-index: 10;
    width: 100%;
    height: 2px;
    content: "";
    background: linear-gradient(90deg, transparent, rgb(0 180 255 / 80%), transparent);
    transition: 0.5s;
  }
}
.chart-title {
  padding: 16px 20px 12px;
  font-size: 18px;
  font-weight: 600;
  color: v-bind("colors.font");
  background: linear-gradient(90deg, rgb(0 120 255 / 10%), transparent);
  border-bottom: 1px solid rgb(255 255 255 / 5%);
}
.chart-instance {
  flex: 1;
  width: 100%;
  height: 100%;
}
.glow-effect {
  position: relative;
  overflow: hidden;
  &::after {
    position: absolute;
    inset: 0;
    pointer-events: none;
    content: "";
    background: radial-gradient(circle at 20% 30%, rgb(0 180 255 / 10%), transparent 60%);
  }
}

@media (width <= 992px) {
  .dashboard-charts {
    grid-template-columns: 1fr;
  }
}
</style>
