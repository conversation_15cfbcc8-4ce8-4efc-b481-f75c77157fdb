<template>
  <div class="main-box">
    <div class="table-box">
      <div>
        <SearchForm :columns="searchColumns" :search-param="searchParam" :search-col="3" :search="handleSearch" :reset="handleReset">
          <template #any>
            <el-button @click="exportData" :icon="Document">{{ t("common.tableExport") }}</el-button>
            <el-button @click="exportImgs" :icon="Picture">{{ t("common.imageExport") }}</el-button>
          </template>
        </SearchForm>
      </div>
      <!-- 操作按钮 -->
      <el-scrollbar height="100%">
        <div class="charts" id="648085807235141">
          <div class="chart-item" id="chart2">
            <alramtop ref="alramtopChart" :machines="machineList" @export-data="handleAlramtopData"></alramtop>
          </div>
          <div class="chart-item" id="chart3">
            <alarmTypeTop ref="alarmTypeTopChart" :machines="machineList" @export-data="handleAlramTypeTopData"></alarmTypeTop>
          </div>

          <div class="chart-item" id="chart4">
            <Long ref="qualityChart" :machines="machineList" @export-data="handleQualityData"></Long>
          </div>
          <div class="chart-item" id="chart5">
            <AlarmRate ref="achievementRateChart" :machines="machineList" @export-data="handleAchievementRateData"></AlarmRate>
          </div>

          <div class="chart-item2" id="chart1">
            <NgCount ref="ngCountChart" :machines="machineList" @export-data="handleNgcountData"></NgCount>
          </div>
          <div class="table-container" id="table1">
            <!-- 使用ProTable组件 -->
            <ProTable
              ref="proTableRef"
              :columns="alertColumns"
              :border="true"
              :tool-button="false"
              :request-api="fetchFilteredData"
              @data-loaded="handleProTableData"
            >
            </ProTable>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { Document, Picture } from "@element-plus/icons-vue";
import moment from "moment";
import type { ColumnProps } from "@/components/ProTable/interface";
import SearchForm from "@/components/SearchForm/index.vue";
import alarmTypeTop from "./alarmTypeTop.vue";
import NgCount from "./NgCount.vue";
import alramtop from "./alramtop.vue";
import AlarmRate from "./AlarmRate.vue";
import Long from "./Long.vue";
import * as XLSX from "xlsx";
import { exportElementsAsImages } from "@/utils";
import { alramAnalysisApi } from "@/api";
import { ElMessage } from "element-plus";
import { useI18n } from "vue-i18n";
import { onMounted, nextTick, computed, watch } from "vue";

// 添加 onMounted 钩子，页面加载时自动触发搜索
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
});

const { t, locale } = useI18n();

const searchParam = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
  machine: ""
});

const alramtopChart = ref();
const ngCountChart = ref();
const exportDataObj = ref<any[]>([]);
const alarmTypeTopChart = ref();
const achievementRateChart = ref();
const qualityChart = ref();
const proTableRef = ref<any>(null);

// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const getMachineOptions = computed(() => [
  // { label: "全部机台", value: "" }, // 添加空选项
  ...machineList.value.map(item => ({
    label: item.name,
    value: item.id
  }))
]);
const searchColumns = ref<ColumnProps[]>([
  {
    prop: "time",
    label: t("common.compareList.time"),
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": "Start date",
        "end-placeholder": "End date"
      }
    },
    isShow: false
  },
  {
    prop: "machine",
    label: t("common.compareList.machine"),
    search: {
      el: "select",
      props: {
        placeholder: "请选择机台",
        clearable: true,
        // 绑定动态选项
        options: getMachineOptions
      }
      // options: machineOptions // 使用计算属性
    }
  }
]);

// 数据获取函数
const fetchData = async (params: any) => {
  try {
    // 参数校验：确保time存在且为有效日期数组
    if (!params?.time || !Array.isArray(params.time) || params.time.length !== 2 || !params.time.every(time => moment(time).isValid())) {
      throw new Error("时间参数无效");
    }

    // 动态获取分页参数
    const pageNum = params.pageNum || 1;
    const pageSize = params.pageSize || 100;

    // 格式化时间参数
    const startTime = moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss.SSS");
    const endTime = moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss");

    // 构建查询参数
    const queryParams = {
      // machine: params.machine || "",
      // start_time: startTime,
      // end_time: endTime,
      PageNum: pageNum,
      PageSize: pageSize,
      Deck: params.machine || "",
      StartDate: startTime,
      EndDate: endTime,
      Type: 8,
      Ispage: true
    };

    // 调用接口
    const response: any = await alramAnalysisApi.getListMesReportData(queryParams);
    console.log("API响应数据:", response);

    // 数据处理
    const dataList = response.data.list || [];
    response.data.list = dataList.map(item => ({
      station: item.station || "N/A",
      line_name: item.line_name || item.production_line || "N/A",
      machine_name: item.machine_name || item.machine || "N/A",
      alarm_name: item.fault_name || "N/A",
      alarm_category: item.fault_type || item.alarm_category || "N/A",
      alarm_info: item.alarm_info || "N/A",
      start_time: moment(item.start_time).format("YYYY-MM-DD HH:mm:ss"),
      end_time: item.end_time ? moment(item.end_time).format("YYYY-MM-DD HH:mm:ss") : "N/A",
      duration_minutes: item.duration_minutes || 0
    }));

    return response;
  } catch (error: any) {
    console.error("API请求失败:", { params, error });
    ElMessage.error(`数据获取失败: ${error.message}`);
    return { list: [], total: 0 };
  }
};
// 定义数据类型
interface AlarmData {
  station: string;
  line_name: string;
  machine_name: string;
  alarm_name: string;
  alarm_category: string;
  alarm_info: string;
  start_time: string;
  end_time: string;
  duration_minutes: number;
}

const proTableData = ref<AlarmData[]>([]); // 存储ProTable数据

// 数据过滤函数
const fetchFilteredData = async (params: any) => {
  // 合并searchParam中的时间参数到请求参数中
  const mergedParams = {
    ...params,
    time: searchParam.value.time // 确保时间参数存在
  };
  try {
    const allData = await fetchData(mergedParams);
    proTableData.value = allData.data.list; // 存储数据
    return allData;
  } catch (error) {
    console.error("数据过滤失败:", error);
    return { list: [], total: 0 };
  }
};

const handleSearch = async () => {
  try {
    // 确保searchParam有效性
    if (!searchParam.value.time) {
      searchParam.value.time = [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")];
    }

    await fetchFilteredData(searchParam.value);
    // 等待 DOM 更新后调用图表组件的方法
    await nextTick();
    proTableRef.value?.refresh(); // 触发 ProTable 的刷新
    const chartRefs = [alramtopChart.value, ngCountChart.value, alarmTypeTopChart.value, achievementRateChart.value, qualityChart.value];
    const params = {
      ...searchParam.value,
      machine: searchParam.value.machine // 确保参数名与接口一致
    };
    console.log(params, "params");

    chartRefs.forEach(chart => {
      chart.tableRef.handleSearch(params);
      if (chart && chart.tableRef && typeof chart.tableRef.handleSearch === "function") {
        // chart.tableRef.handleSearch(searchParam.value);
      }
    });
  } catch (error: any) {
    ElMessage.error(`搜索失败: ${error.message}`);
  }
};

// 1. 获取机台列表（Type=0）
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response: any = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map((item: any) => ({
      id: item.MachineName || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // 添加：设置默认选中第一个机台
    if (machineList.value.length > 0) {
      searchParam.value.machine = machineList.value[0].id; // 设置默认机台 id
    }

    console.log("机台列表已更新:", machineList.value);
  } catch (error: any) {
    console.error("机台列表请求失败:", error);
    ElMessage.error("获取机台列表失败");
  }
};

const alertColumns = computed<ColumnProps[]>(() => [
  { prop: "station", label: t("sheetCount.station") },
  { prop: "line_name", label: t("sheetCount.lineName") },
  { prop: "machine_name", label: t("sheetCount.machineName") },
  { prop: "alarm_category", label: t("sheetCount.alarmCategory") },
  { prop: "alarm_info", label: t("sheetCount.alarmInfo") },
  { prop: "start_time", label: t("sheetCount.startTime") },
  { prop: "end_time", label: t("sheetCount.endTime") },
  { prop: "duration_minutes", label: t("sheetCount.duration") }
]);

const addDataIfNotExists = (newItem: any) => {
  const index = exportDataObj.value.findIndex(item => item.name === newItem.name);
  if (index !== -1) {
    exportDataObj.value[index] = newItem;
  } else {
    exportDataObj.value.push(newItem);
  }
};
const handleAlramTypeTopData = (data: any) => {
  const param = {
    name: "故障类型top10",
    data: data.data
  };
  addDataIfNotExists(param);
};

const handleAlramtopData = (data: any) => {
  const param = {
    name: "故障次数top10",
    data: data.data
  };
  addDataIfNotExists(param);
};

const handleNgcountData = (data: any) => {
  const param = {
    name: "告警统计",
    data: data.data
  };
  addDataIfNotExists(param);
};

const handleAchievementRateData = (data: any) => {
  const param = {
    name: "故障率",
    data: data.data
  };
  addDataIfNotExists(param);
};

const handleQualityData = (data: any) => {
  const param = {
    name: "故障时长",
    data: data.data
  };
  addDataIfNotExists(param);
};
const handleReset = () => {
  searchParam.value = {
    time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
    machine: ""
  };
  handleSearch();
};

const exportImgs = () => {
  const start_time = moment(searchParam.value.time[0]).format("YYYY-MM-DD");
  const endTime = moment(searchParam.value.time[1]).format("YYYY-MM-DD");
  const exportItems = [
    { elementId: "chart5", fileName: `故障率${start_time}-${endTime}.png` },
    { elementId: "chart4", fileName: `故障时长${start_time}-${endTime}.png` },
    { elementId: "chart3", fileName: `故障类型top10${start_time}-${endTime}.png` },
    { elementId: "chart2", fileName: `故障top10${start_time}-${endTime}.png` },
    { elementId: "chart1", fileName: `告警统计${start_time}-${endTime}.png` },
    { elementId: "table1", fileName: `统计${start_time}-${endTime}.png` }
  ];
  try {
    exportElementsAsImages(exportItems);
    ElMessage.success("图片批量导出成功");
  } catch (error) {
    ElMessage.error("图片批量导出失败，请查看错误提示");
  }
};
const exportData = () => {
  const wb = XLSX.utils.book_new();
  for (let index = 0; index < exportDataObj.value.length; index++) {
    const element = exportDataObj.value[index];
    const data = element.data.split("\n").map(row => row.split(","));
    const ws = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(wb, ws, element.name);
  }
  console.log(exportDataObj.value, "111111111");
  const headers = ["工位名称", "线体名称", "机台名称", "报警类别", "告警信息", "开始时间", "结束时间", "持续时间"];
  const currentAlarmDataArray = proTableData.value.map((item: any) => [
    item.station,
    item.line_name,
    item.machine_name,
    item.alarm_category,
    item.alarm_info,
    item.start_time,
    item.end_time,
    item.duration_minutes
  ]);
  const arr1 = [headers, ...currentAlarmDataArray];
  // 将表格数据添加到工作簿
  const tableWs = XLSX.utils.aoa_to_sheet(arr1);
  XLSX.utils.book_append_sheet(wb, tableWs, "统计");
  XLSX.writeFile(wb, "告警报表与统计.xlsx");
  ElMessage.success("导出成功");
};

// 监听语言变化，强制更新表格
watch(
  () => locale.value,
  () => {
    // 强制更新表格
    nextTick(() => {
      proTableRef.value?.refresh();
    });
  }
);
</script>

<style lang="scss" scoped>
.charts {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
  width: 100%;
}
.chart-item {
  box-sizing: border-box;
  width: calc(50% - 10px);
  margin-bottom: 10px;
}
.chart-item2 {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 10px;
}
.chart {
  width: 100%;
  height: 100%;
}

@media (width <= 768px) {
  .chart-item {
    width: 100%;
    height: 100%;
  }
}
.table-container {
  width: 100%;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: inset 0 0 4px rgb(0 0 0 / 10%);
}

@media (width <= 768px) {
  .table-container {
    padding: 10px;
  }
}
</style>
