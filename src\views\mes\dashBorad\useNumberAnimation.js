import { ref, watch, onMounted } from "vue";

export function useNumberAnimation(source) {
  const animatedValue = ref(0);
  let animationFrame = null;

  const animate = (start, end, duration = 1000) => {
    const startTime = performance.now();
    const updateValue = currentTime => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      animatedValue.value = Math.floor(start + progress * (end - start));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(updateValue);
      }
    };
    animationFrame = requestAnimationFrame(updateValue);
  };

  watch(
    source,
    (newVal, oldVal) => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
      animate(oldVal || 0, newVal);
    },
    { immediate: true }
  );

  onMounted(() => {
    animate(0, source());
  });

  return { animatedValue };
}
