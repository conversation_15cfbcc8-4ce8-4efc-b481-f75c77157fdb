<template>
  <div class="process-params-container">
    <el-empty v-if="!hasData" description="请输入查询内容进行查询" />
    <div v-else class="info-content">
      <el-table :data="paramsList" border style="width: 100%">
        <el-table-column prop="processName" label="工序名称" width="180" />
        <el-table-column prop="paramName" label="参数名称" width="180" />
        <el-table-column prop="paramValue" label="参数值" width="180" />
        <el-table-column prop="unit" label="单位" width="100" />
        <el-table-column prop="upperLimit" label="上限" width="100" />
        <el-table-column prop="lowerLimit" label="下限" width="100" />
        <el-table-column prop="recordTime" label="记录时间" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts" name="processParams">
import { ref, watch } from "vue";

// 定义组件属性
const props = defineProps({
  queryParams: {
    type: Object,
    required: true
  }
});

// 定义过程参数接口
interface ProcessParam {
  processName: string;
  paramName: string;
  paramValue: string | number;
  unit: string;
  upperLimit: number;
  lowerLimit: number;
  recordTime: string;
}

// 是否有数据标志
const hasData = ref(false);

// 过程参数列表
const paramsList = ref<ProcessParam[]>([]);

// 监听查询参数变化
watch(
  () => props.queryParams,
  newVal => {
    if (newVal.queryValue) {
      fetchProcessParams(newVal.queryType, newVal.queryValue);
    }
  },
  { deep: true }
);

/**
 * 获取电芯过程参数
 */
function fetchProcessParams(queryType: string, queryValue: string) {
  // TODO: 替换为实际API调用
  console.log("查询过程参数:", queryType, queryValue);

  // 模拟数据，实际项目中应替换为API请求
  setTimeout(() => {
    paramsList.value = [
      {
        processName: "极片分切",
        paramName: "分切速度",
        paramValue: 120,
        unit: "mm/s",
        upperLimit: 150,
        lowerLimit: 100,
        recordTime: "2025-06-10 08:10:00"
      },
      {
        processName: "极片分切",
        paramName: "分切压力",
        paramValue: 5.2,
        unit: "MPa",
        upperLimit: 6.0,
        lowerLimit: 4.5,
        recordTime: "2025-06-10 08:10:00"
      },
      {
        processName: "极耳焊接",
        paramName: "焊接温度",
        paramValue: 180,
        unit: "℃",
        upperLimit: 200,
        lowerLimit: 160,
        recordTime: "2025-06-10 08:35:00"
      },
      {
        processName: "极耳焊接",
        paramName: "焊接时间",
        paramValue: 2.5,
        unit: "s",
        upperLimit: 3.0,
        lowerLimit: 2.0,
        recordTime: "2025-06-10 08:35:00"
      }
    ];
    hasData.value = true;
  }, 500);
}
</script>

<style lang="scss" scoped>
.process-params-container {
  height: 100%;
  padding: 20px;
  .info-content {
    margin-top: 20px;
  }
}
</style>
