<!-- 横向布局 -->
<template>
  <el-container class="layout">
    <el-header>
      <div class="logo flx-center">
        <img class="logo-img" :src="sysInfo.SYS_LOGO" alt="logo" />
        <span class="logo-text">{{ sysInfo.SYS_NAME }}</span>
      </div>
      <el-menu ref="shitmenu" mode="horizontal" :router="false" :default-active="activeMenu">
        <!-- 不能直接使用 SubMenu 组件，无法触发 el-menu 隐藏省略功能 -->
        <template v-for="subItem in menuList" :key="subItem.path">
          <el-sub-menu v-if="subItem.children?.length" :key="subItem.path" :index="subItem.path + 'el-sub-menu'">
            <template #title>
              <svg-icon :icon="subItem.meta.icon" class="el-icon" />
              <!-- <span>{{ subItem.meta.title }}</span> -->
              <span>
                {{ $t(`menu.${titleToKeyMap[subItem.meta.title] || subItem.meta.title}`, subItem.meta.title) }}
                <!-- {{ subItem.meta.title }} -->
              </span>
            </template>
            <SubMenu :menu-list="subItem.children" />
          </el-sub-menu>
          <el-menu-item v-else :key="subItem.path + 'el-menu-item'" :index="subItem.path" @click="handleClickMenu(subItem)">
            <svg-icon :icon="subItem.meta.icon" class="el-icon" />
            <template #title>
              <span>
                {{ $t(`menu.${titleToKeyMap[subItem.meta.title] || subItem.meta.title}`, subItem.meta.title) }}
                <!-- {{ subItem.meta.title }} -->
              </span>
            </template>
          </el-menu-item>
        </template>
      </el-menu>
      <ToolBarRight />
    </el-header>
    <Main />
  </el-container>
</template>

<script setup lang="ts" name="layoutTransverse">
import { computed } from "vue";
import { useAuthStore, useConfigStore } from "@/stores/modules";
import { useRoute, useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import Main from "@/layouts/components/Main/index.vue";
import ToolBarRight from "@/layouts/components/Header/ToolBarRight.vue";
import SubMenu from "@/layouts/components/Menu/SubMenu.vue";
import { titleToKeyMap } from "@/languages/common";

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const configStore = useConfigStore();
const { locale } = useI18n();
const menuList = computed(() => authStore.showMenuListGet);
const activeMenu = computed(() => (route.meta.activeMenu ? route.meta.activeMenu : route.path) as string);
const sysInfo = computed(() => configStore.sysBaseInfoGet);
const shitmenu = ref();

// 监听语言切换
watch(
  () => locale.value,
  () => {
    nextTick(() => {
      shitmenu.value?.handleResize();
    });
  }
);

const handleClickMenu = (subItem: Menu.MenuOptions) => {
  if (subItem.meta.isLink) return window.open(subItem.meta.isLink, "_blank");
  router.push(subItem.path);
};
</script>

<style scoped lang="scss">
@import "./index";
</style>
