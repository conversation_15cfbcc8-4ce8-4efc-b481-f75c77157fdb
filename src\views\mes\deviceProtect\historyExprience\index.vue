<template>
  <div class="main-box" id="661535347249221">
    <!-- 左侧树状结构 -->
    <TreeFilter
      :default-expand-all="false"
      ref="treeFilter"
      label="name"
      :title="t('deviceProtect.maybeLife.organizationList')"
      :request-api="deviceProtectdailyProtectApi.getProdEquipmentMaterialInfoGetTree"
      @change1="handleNodeClick"
    >
      <template #shit>
        <CalendarCustom current-theme="light" :header-title="title" :date-data="dateData"> </CalendarCustom>
      </template>
    </TreeFilter>
    <div class="table-box">
      <!-- 右侧表格 -->
      <ProTable
        v-if="!isChart"
        :init-param="initParam"
        ref="proTableRef"
        :columns="columns"
        :request-api="fetchFilteredData"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      >
        <template #toolButton>
          <el-tooltip content="切换表格" placement="top">
            <el-button :icon="Switch" circle @click="changeTable" />
          </el-tooltip>
        </template>
        <!-- 操作按钮 -->
        <template #tableHeader>
          <!-- <el-button type="primary" @click="openAddDialog">新增</el-button>
          <el-button type="success" @click="importData">导入</el-button> -->
          <el-button type="primary" @click="exportData">{{ t("deviceProtect.maybeLife.export") }}</el-button>
          <!-- <el-button type="danger" :disabled="!selectedRows.length" @click="deleteSelectedRows"> 批量删除 </el-button> -->
        </template>
        <!-- 操作列 -->
        <template #operation="{ row }">
          <!-- <el-button type="primary" size="small" @click="openEditDialog(row)"> 编辑 </el-button>
          <el-button type="danger" size="small" @click="deleteRow(row)"> 删除 </el-button> -->
          <el-button type="info" size="small" @click="openViewDialog(row)"> {{ t("deviceProtect.maybeLife.view") }} </el-button>
        </template>
      </ProTable>
      <SwitchChart :current-tree-type="currentTreeType" :init-params="switchchartData" v-else @toggle-view="changeTable"></SwitchChart>

      <!-- 新增/编辑/查看对话框 -->
      <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增' : dialogType === 'edit' ? '编辑' : '查看详情'">
        <el-form :model="formData" ref="formRef" label-width="180px" :rules="rules" class="form-dialog">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.number')">
                <el-input v-model="formData.number" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.factory')">
                <el-input v-model="formData.factory" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.workshop')">
                <el-input v-model="formData.workshop" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.workline')">
                <el-input v-model="formData.workline" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.deck')">
                <el-select v-model="formData.deck" :placeholder="t('deviceProtect.maybeLife.form.deck')" :disabled="dialogType === 'view'">
                  <el-option v-for="option in getMachineOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.station')">
                <el-input v-model="formData.station" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.component')">
                <el-select v-model="formData.component" :placeholder="t('deviceProtect.maybeLife.form.component')" :disabled="dialogType === 'view'">
                  <el-option v-for="option in componentOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.workstationCategory')">
                <el-input v-model="formData.workstationCategory" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.materialCode')">
                <el-input v-model="formData.materialCode" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.materialName')">
                <el-input v-model="formData.materialName" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.materialSpecification')">
                <el-input v-model="formData.materialSpecification" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.brand')">
                <el-input v-model="formData.brand" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.theoreticalLife')" prop="theoreticalLife">
                <el-input-number v-model="formData.theoreticalLife" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.experienceLife')" prop="experienceLife">
                <el-input-number v-model="formData.experienceLife" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.usedLife')" prop="usedLife">
                <el-input-number v-model="formData.usedLife" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.lifeUnit')">
                <el-input v-model="formData.lifeUnit" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.lifeWarningLowerLimit')" prop="lifeWarningLowerLimit">
                <el-input-number v-model="formData.lifeWarningLowerLimit" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.lifeWarningUpperLimit')" prop="lifeWarningUpperLimit">
                <el-input-number v-model="formData.lifeWarningUpperLimit" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.warningTime')" prop="warningTime">
                <el-date-picker
                  v-model="formData.warningTime"
                  type="datetime"
                  :placeholder="t('deviceProtect.maybeLife.form.warningTime')"
                  :disabled="dialogType === 'view'"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.processingStatus')">
                <el-select
                  v-model="formData.processingStatus"
                  :placeholder="t('deviceProtect.maybeLife.form.processingStatus')"
                  :disabled="dialogType === 'view'"
                >
                  <el-option v-for="option in processingStatusOptions" :key="option.value" :label="option.label" :value="option.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.processor')">
                <el-input v-model="formData.processor" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.processingTime')">
                <el-date-picker
                  v-model="formData.processingTime"
                  type="datetime"
                  :placeholder="t('deviceProtect.maybeLife.form.processingTime')"
                  :disabled="dialogType === 'view'"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('deviceProtect.maybeLife.form.processingDuration')">
                <el-input-number v-model="formData.processingDuration" :disabled="dialogType === 'view'"></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item :label="t('deviceProtect.maybeLife.form.remarks')">
                <el-input v-model="formData.remarks" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button v-if="dialogType !== 'view'" type="primary" @click="saveData">保存</el-button>
        </template>
      </el-dialog>

      <!-- 导入文件选择框 -->
      <el-dialog v-model="importDialogVisible" title="导入数据">
        <el-upload ref="uploadRef" action="/api/sys/upload/uploadExcel" :auto-upload="false" :before-upload="beforeUpload">
          <el-button type="primary">选择文件</el-button>
        </el-upload>
        <template #footer>
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="startImport">开始导入</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus"; // 引入 ElMessage
import { deviceProtectMaybeLifeApi, deviceProtectdailyProtectApi, productionReportCapacityApi } from "@/api";
import { convertCalendarData } from "@/utils";
import { Switch } from "@element-plus/icons-vue";
import SwitchChart from "./SwitchChart.vue";
import moment from "moment";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
import { treeArr } from "@/config";
const { t } = useI18n();

const isChart = ref(false);
const changeTable = () => {
  isChart.value = !isChart.value;
};
// 添加 onMounted 钩子，页面加载时自动触发搜索
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
});
const initParam = ref({
  // station: "",
  // mach: "",
  // production_line: "",
  // workshop: "",
  // factory: ""
  // time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")]
});
const rules = {
  processingTime: [{ required: true, message: "请选择处理时间", trigger: "blur" }],
  theoreticalLife: [{ required: true, message: "请填写理论寿命", trigger: "blur" }],
  experienceLife: [{ required: true, message: "请填写经验寿命", trigger: "blur" }],
  usedLife: [{ required: true, message: "请填写已使用寿命", trigger: "blur" }],
  lifeWarningLowerLimit: [{ required: true, message: "请填写寿命预警下限值", trigger: "blur" }],
  lifeWarningUpperLimit: [{ required: true, message: "请填写寿命预警上限值", trigger: "blur" }],
  warningTime: [{ required: true, message: "请选择预警时间", trigger: "blur" }]
};
const title = ref("本月维保日历");
const dateData = ref([]);

onMounted(async () => {
  const query = {
    Type: 442,
    IsPage: false
  };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);
  dateData.value = convertCalendarData(data.list);
});
const exportParam = ref({});

// ProTable 实例引用
const proTableRef = ref(null);
// 对话框相关
const dialogVisible = ref(false);
const importDialogVisible = ref(false);
const dialogType = ref("add"); // 对话框类型：add, edit, view
// 表单数据
const formData = reactive({
  number: "",
  factory: "",
  workshop: "",
  workline: "",
  deck: "",
  station: "",
  // component: 0,
  component: "",
  workstationCategory: "",
  materialCode: "",
  materialName: "",
  materialSpecification: "",
  brand: "",
  theoreticalLife: 0,
  experienceLife: 0,
  usedLife: 0,
  lifeUnit: "",
  lifeWarningLowerLimit: 0,
  lifeWarningUpperLimit: 0,
  warningTime: "",
  warningStatus: "",
  processingStatus: "",
  processor: "",
  processingTime: "",
  processingDuration: 0,
  remarks: ""
});
// 表单引用
const formRef = ref(null);
// 上传组件引用
const uploadRef = ref(null);
// 选中的行数据
const selectedRows = ref([]);
// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const getMachineOptions = computed(() => [
  // { label: "全部机台", value: "" }, // 添加空选项
  ...machineList.value.map(item => ({
    label: item.name,
    value: item.id
  }))
]);
// 组件选项
// const componentOptions = [
//   { value: 1, label: "组件1" },
//   { value: 2, label: "组件2" },
//   { value: 3, label: "组件3" },
//   { value: 4, label: "组件4" },
//   { value: 5, label: "组件5" }
// ];
const componentOptions = [
  { value: "组件1", label: "组件1" },
  { value: "组件2", label: "组件2" },
  { value: "组件3", label: "组件3" },
  { value: "组件4", label: "组件4" },
  { value: "组件5", label: "组件5" }
];
// 预警状态选项
const warningStatusOptions = [
  { value: "正常", label: "正常" },
  { value: "预警", label: "预警" },
  { value: "预警中", label: "预警中" },
  { value: "危险", label: "危险" }
];
// 处理状态选项
const processingStatusOptions = [
  { value: "未处理", label: "未处理" },
  { value: "处理中", label: "处理中" },
  { value: "已处理", label: "已处理" }
];

// 表格列配置
const columns = computed(() => [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "id", label: t("deviceProtect.maybeLife.form.id"), isShow: false },
  {
    prop: "time",
    label: t("deviceProtect.maybeLife.form.time"),
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": t("deviceProtect.maybeLife.form.startDate"),
        "end-placeholder": t("deviceProtect.maybeLife.form.endDate")
      },
      defaultValue: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")]
    },
    isShow: false
  },
  {
    prop: "number",
    label: t("deviceProtect.maybeLife.form.number"),
    width: 80,
    align: "center"
  },
  {
    prop: "factory",
    label: t("deviceProtect.maybeLife.form.factory"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "workshop",
    label: t("deviceProtect.maybeLife.form.workshop"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "workline",
    label: t("deviceProtect.maybeLife.form.workline"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "deck",
    label: t("deviceProtect.maybeLife.form.deck"),
    search: {
      el: "select",
      props: {
        placeholder: t("deviceProtect.maybeLife.form.selectMachine"),
        clearable: true,
        options: getMachineOptions.value
      }
    }
  },
  {
    prop: "station",
    label: t("deviceProtect.maybeLife.form.station"),
    width: 150,
    align: "left",
    search: { el: "input" }
  },
  {
    prop: "component",
    label: t("deviceProtect.maybeLife.form.component"),
    enum: componentOptions,
    width: 120
  },
  {
    prop: "workstationCategory",
    label: t("deviceProtect.maybeLife.form.workstationCategory"),
    width: 150,
    align: "left"
  },
  {
    prop: "materialCode",
    label: t("deviceProtect.maybeLife.form.materialCode"),
    width: 150,
    align: "left"
  },
  {
    prop: "materialName",
    label: t("deviceProtect.maybeLife.form.materialName"),
    width: 150,
    align: "left"
  },
  {
    prop: "materialSpecification",
    label: t("deviceProtect.maybeLife.form.materialSpecification"),
    width: 150,
    align: "left"
  },
  {
    prop: "brand",
    label: t("deviceProtect.maybeLife.form.brand"),
    width: 100,
    align: "left"
  },
  {
    prop: "theoreticalLife",
    label: t("deviceProtect.maybeLife.form.theoreticalLife"),
    width: 100,
    align: "right"
  },
  {
    prop: "experienceLife",
    label: t("deviceProtect.maybeLife.form.experienceLife"),
    width: 100,
    align: "right"
  },
  {
    prop: "usedLife",
    label: t("deviceProtect.maybeLife.form.usedLife"),
    width: 100,
    align: "right"
  },
  {
    prop: "lifeUnit",
    label: t("deviceProtect.maybeLife.form.lifeUnit"),
    width: 180,
    align: "center"
  },
  {
    prop: "lifeWarningLowerLimit",
    label: t("deviceProtect.maybeLife.form.lifeWarningLowerLimit"),
    width: 100,
    align: "right"
  },
  {
    prop: "lifeWarningUpperLimit",
    label: t("deviceProtect.maybeLife.form.lifeWarningUpperLimit"),
    width: 100,
    align: "right"
  },
  {
    prop: "warningTime",
    label: t("deviceProtect.maybeLife.form.warningTime"),
    width: 180,
    align: "center"
  },
  {
    prop: "warningStatus",
    label: t("deviceProtect.maybeLife.form.warningStatus"),
    enum: warningStatusOptions,
    width: 100
  },
  {
    prop: "processingStatus",
    label: t("deviceProtect.maybeLife.form.processingStatus"),
    enum: processingStatusOptions,
    width: 100
  },
  {
    prop: "processor",
    label: t("deviceProtect.maybeLife.form.processor"),
    width: 150,
    align: "left"
  },
  {
    prop: "processingTime",
    label: t("deviceProtect.maybeLife.form.processingTime"),
    width: 180,
    align: "center"
  },
  {
    prop: "processingDuration",
    label: t("deviceProtect.maybeLife.form.processingDuration"),
    width: 100,
    align: "right"
  },
  {
    prop: "remarks",
    label: t("deviceProtect.maybeLife.form.remarks"),
    width: 200,
    align: "left"
  },
  { prop: "operation", label: t("deviceProtect.maybeLife.form.operation"), width: 230, fixed: "right" }
]);
// 1. 获取机台列表（Type=0）
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineName || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // 添加：设置默认选中第一个机台
    // if (machineList.value.length > 0) {
    //   param.value.machine = machineList.value[0].id; // 设置默认机台 id
    // }

    console.log("机台列表已更新:", machineList.value);
  } catch (error) {
    // console.error("机台列表请求失败:", error);
    // ElMessage.error("获取机台列表失败");
  }
};

// 真实的数据请求
const fetchData = async param => {
  try {
    const time = {
      StartDate: moment(param.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
      EndDate: moment(param.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString()
    };
    delete param.time;
    const query = {
      ...time,
      Type: param.Type,
      IsPage: true
    };
    const response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoGet({ ...param, ...query });
    // 确保返回的数据格式正确
    if (response.code === 200 && Array.isArray(response.data.list)) {
      return {
        data: response.data
      };
    } else {
      console.error("数据格式不正确:", response);
      return {
        data: {
          total: 0,
          list: []
        }
      };
    }
  } catch (error) {
    console.error("获取数据失败:", error); // 添加错误日志输出
    ElMessage.error("获取数据失败");
    return {
      data: {
        total: 0,
        list: []
      }
    };
  }
};

// 过滤后的数据请求
// const currentFilter = ref(null);
const fetchFilteredData = async params => {
  const allData = await fetchData(params);
  exportParam.value.pageSize = allData.data.total;
  return allData;
};
// const openAddDialog = () => {
//   dialogType.value = "add";
//   // 清空表单数据
//   Object.keys(formData).forEach(key => {
//     formData[key] = "";
//   });
//   dialogVisible.value = true;
// };

// // 打开编辑对话框
// const openEditDialog = row => {
//   dialogType.value = "edit";
//   // 填充表单数据
//   Object.assign(formData, row);
//   dialogVisible.value = true;
// };

// 打开查看详情对话框
const openViewDialog = row => {
  dialogType.value = "view";
  // 填充查看详情表单数据
  Object.assign(formData, row);
  dialogVisible.value = true;
};

// 保存数据
// const saveData = async () => {
//   formRef.value.validate(valid => {
//     if (valid) {
//       // 表单验证通过，执行保存逻辑
//       let response;
//       if (dialogType.value === "add") {
//         response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoAdd(formData);
//       } else if (dialogType.value === "edit") {
//         response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoEdit(formData);
//       }
//       if (response && response.code === 200) {
//         ElMessage.success("保存成功");
//         dialogVisible.value = false;
//         // 刷新表格数据
//         proTableRef.value.getTableList();
//         // 重新构建树状结构数据
//       } else {
//         ElMessage.error("保存失败");
//       }
//     } else {
//       // 表单验证不通过，提示用户
//       ElMessage.error("请填写必填项");
//       return;
//     }
//   });
// };
const saveData = async () => {
  try {
    const valid = await formRef.value.validate();
    if (valid) {
      let response;
      if (dialogType.value === "add") {
        response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoAdd(formData);
      } else if (dialogType.value === "edit") {
        response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoEdit(formData);
      }
      if (response && response.code === 200) {
        ElMessage.success("保存成功");
        dialogVisible.value = false;
        proTableRef.value.getTableList();
        // 检查formData.component的值
        console.log("formData.component after save:", formData.component);
      } else {
        ElMessage.error("保存失败");
      }
    } else {
      ElMessage.error("请填写必填项");
    }
  } catch (error) {
    // console.error(error);
    // ElMessage.error("保存过程中出现错误");
  }
};
// const saveData = async () => {
//   if (!formData.factory) {
//     ElMessage.error("请填写必要信息");
//     return;
//   }
//   try {
//     let response;
//     if (dialogType.value === "add") {
//       response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoAdd(formData);
//     } else if (dialogType.value === "edit") {
//       response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoEdit(formData);
//     }
//     if (response && response.code === 200) {
//       ElMessage.success("保存成功");
//       dialogVisible.value = false;
//       // 刷新表格数据
//       proTableRef.value.getTableList();
//       // 重新构建树状结构数据
//     } else {
//       ElMessage.error("保存失败");
//     }
//   } catch (error) {
//     ElMessage.error("保存过程中出现错误");
//   }
// };
// // 删除行
// const deleteRow = async row => {
//   // 这里应该替换为实际的删除 API 请求
//   ElMessage.success(`删除 ${row.factory} 成功`);
//   // 刷新表格数据
//   proTableRef.value.getTableList();
//   // 重新构建树状结构数据
//   buildTreeData();
// };
// 删除行
// const deleteRow = async row => {
//   try {
//     const response = await deviceProtectMaybeLifeApi.ProdEquipmentMaterialInfoDelete(row);

//     if (response && response.code === 200) {
//       ElMessage.success(`删除 ${row.factory} 成功`);
//       // 刷新表格数据
//       proTableRef.value.getTableList();
//       // 重新构建树状结构数据
//     } else {
//       ElMessage.error(`删除 ${row.factory} 失败`);
//     }
//   } catch (error) {
//     ElMessage.error(`删除 ${row.factory} 过程中出现错误`);
//   }
// };

// // 批量删除选中行
// const deleteSelectedRows = async () => {
//   // 这里应该替换为实际的批量删除 API 请求
//   ElMessage.success(`批量删除 ${selectedRows.value.length} 条记录成功`);
//   // 清空选中行
//   selectedRows.value = [];
//   // 刷新表格数据
//   proTableRef.value.getTableList();
//   // 重新构建树状结构数据
// };

// 处理行点击事件
const handleRowClick = row => {
  console.log("点击行数据：", row);
};

// 处理选中行变化事件
const handleSelectionChange = rows => {
  selectedRows.value = rows;
};

// 打开导入对话框
// const importData = () => {
//   importDialogVisible.value = true;
// };

// 导出数据
const exportData = async () => {
  try {
    // 获取当前表格的所有数据（包含分页参数）
    const params = {
      ...proTableRef.value.searchParam, // 获取当前查询参数
      page: 1,
      pageSize: exportParam.value.pageSize // 获取足够大的分页尺寸
    };

    // 调用API获取完整数据
    const response = await fetchFilteredData(params);
    if (response.code !== 200) throw new Error(response.msg);
    const tableData = response.data.list;

    // 过滤需要导出的列（排除操作列和选择列）
    const exportColumns = columns.value.filter(col => !["selection", "operation", "id", "time"].includes(col.type || col.prop));

    // 准备表头数据
    const headers = exportColumns.map(col => col.label);

    // 准备表格数据（包括数据转换）
    const dataRows = tableData.map(item => {
      return exportColumns.map(col => {
        // 特殊处理时间字段
        if (col.prop === "mcTime" && item[col.prop]) {
          return new Date(item[col.prop]).toLocaleString();
        }
        // 处理状态字段的枚举值
        if (col.prop === "mcStatus") {
          return mcStatusOptions.find(opt => opt.value === item[col.prop])?.label || item[col.prop];
        }
        return item[col.prop] || "";
      });
    });

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...dataRows]);

    // 创建工作簿并导出
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "设备数据");

    // 生成文件名（带日期）
    const exportDate = new Date().toISOString().slice(0, 10);
    XLSX.writeFile(workbook, `设备维护记录_${exportDate}.xlsx`);

    ElMessage.success("导出成功");
  } catch (error) {
    ElMessage.error(`导出失败: ${error.message}`);
  }
};

// 导入文件前置处理
const beforeUpload = () => {
  // 这里可以添加文件类型、大小等验证逻辑
  return true;
};

// 开始导入
const startImport = () => {
  uploadRef.value!.submit();
  // 这里应该替换为实际的导入 API 请求
  importDialogVisible.value = false;
  ElMessage.success("导入成功");
  // 刷新表格数据
  proTableRef.value.getTableList();
  // 重新构建树状结构数据
};
const currentTreeType = ref<any>(0);

const switchchartData = ref({});
/**
 * 处理树节点点击事件
 * @param nodeObj 点击的节点对象
 */
const handleNodeClick = (nodeObj: any) => {
  currentTreeType.value = nodeObj.level;

  const nodeParams: Record<string, string> = {};
  let currentNode = nodeObj;

  if (!isChart.value && proTableRef.value) {
    treeArr.forEach(key => delete proTableRef.value!.searchParam[key]);
  }

  while (currentNode) {
    const nodeData = currentNode.data;
    if (nodeData?.type && nodeData?.name) {
      nodeParams[nodeData.type] = nodeData.name;

      if (proTableRef.value) {
        proTableRef.value.searchParam[nodeData.type] = nodeData.name;
      }
    }
    currentNode = currentNode.parent;
  }
  switchchartData.value = nodeParams;
  // 根据模式执行相应操作
  if (!isChart.value) {
    proTableRef.value.search();
  }
};

// onMounted(() => {});
</script>

<style scoped>
:deep(.filter) {
  width: auto !important;
}

/* 自定义树的样式 */
.custom-tree {
  width: 20%;
  padding: 10px;
  margin-right: 20px;
  overflow-y: auto;
  background-color: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}

/* 树节点样式 */
.custom-tree .el-tree-node__content {
  height: 32px;
  padding: 0 8px;
  font-size: 14px;
  line-height: 32px;
  color: #333333;
  border-radius: 4px;
  transition: background-color 0.3s;
}

/* 鼠标悬停节点样式 */
.custom-tree .el-tree-node__content:hover {
  background-color: #eef1f6;
}

/* 选中节点样式 */
.custom-tree .el-tree-node.is-current > .el-tree-node__content {
  color: #ffffff;
  background-color: #409eff;
}

/* 展开/收缩图标样式 */
.custom-tree .el-tree-node__expand-icon {
  font-size: 12px;
  color: #909399;
}

/* 展开图标旋转动画 */
.custom-tree .el-tree-node__expand-icon.expanded {
  transition: transform 0.3s;
  transform: rotate(90deg);
}
</style>
