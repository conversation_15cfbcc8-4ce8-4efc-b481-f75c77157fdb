<template>
  <div class="main-box" id="shit">
    <div class="table-box">
      <div>
        <SearchForm :columns="searchColumns" :search-param="searchParam" :search-col="3" :search="handleSearch" :reset="handleReset">
          <template #any>
            <el-button @click="exportData" :icon="Document">表格导出</el-button>
            <el-button @click="exportImgs" :icon="Picture">图片导出</el-button>
          </template>
        </SearchForm>
      </div>
      <!-- 操作按钮 -->

      <div class="charts">
        <div class="chart-item" id="chart4">
          <Times :data="data" ref="capacityChart" @export-data="handleCapacityData"></Times>
        </div>
        <div class="chart-item" id="chart2">
          <Long :data="data" ref="qualityChart" @export-data="handleQualityData"></Long>
        </div>
        <div class="chart-item" id="chart3">
          <AlarmRate :data="data" ref="achievementRateChart" @export-data="handleAchievementRateData"></AlarmRate>
        </div>

        <div class="chart-item" id="chart1">
          <Mttr :data="data" ref="ngCountChart" @export-data="handleNgcountData"></Mttr>
        </div>

        <!--  产能-->

        <!-- OEE -->
      </div>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { Document, Picture } from "@element-plus/icons-vue";
import { alramAnalysisApi } from "@/api";

// import { ref } from "vue";
import moment from "moment";
import type { ColumnProps } from "@/components/ProTable/interface";
import SearchForm from "@/components/SearchForm/index.vue"; // 引入 SearchForm 组件
// import { exportMultipleTablesToExcel } from "@/utils";
import AlarmRate from "./AlarmRate.vue";
import Times from "./Times.vue";
import Long from "./Long.vue";
import Mttr from "./Mttr.vue";
import * as XLSX from "xlsx";
import { exportElementsAsImages } from "@/utils";
const searchParam = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")] // 初始化time为当天日期范围
});
const capacityChart = ref();
const qualityChart = ref();
const ngCountChart = ref();
const exportDataObj = ref([]);

const achievementRateChart = ref();
const data = ref([]);
// 搜索字段配置
const searchColumns = ref<ColumnProps[]>([
  {
    prop: "time",
    label: "时间",
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": "Start date",
        "end-placeholder": "End date"
        // defaultValue: [todayStart, todayEnd]
      }
    },
    isShow: false
  },
  {
    prop: "machine",
    search: {
      isCustom: true,
      render: () => <MachineSwitcher></MachineSwitcher>
    },
    isShow: false
  }
]);

// 查询
const handleSearch = async () => {
  // 调用子组件方法
  const data1 = await alramAnalysisApi.alarmFaultStatsGet();
  data.value = data1.data;

  // capacityChart.value.tableRef.handleSearch(searchParam.value);
  // qualityChart.value.tableRef.handleSearch(searchParam.value);
  // ngCountChart.value.tableRef.handleSearch(searchParam.value);
  // achievementRateChart.value.tableRef.handleSearch(searchParam.value);
};

const handleCapacityData = data => {
  const param = {
    name: "产能",
    data: data.data
  };
  addDataIfNotExists(param);

  // 处理子组件返回的数据
  // 这里可以做进一步的数据处理或传递给其他组件
};
const addDataIfNotExists = newItem => {
  const index = exportDataObj.value.findIndex(item => item.name === newItem.name);
  if (index !== -1) {
    // 如果找到相同 name 的项，替换旧的项
    exportDataObj.value[index] = newItem;
  } else {
    // 如果没找到，添加新项
    exportDataObj.value.push(newItem);
  }
};
const handleAchievementRateData = data => {
  const param = {
    name: "计划达成率",
    data: data.data
  };
  // 处理子组件返回的数据
  addDataIfNotExists(param);
  // 处理子组件返回的数据
  // 这里可以做进一步的数据处理或传递给其他组件
};
const handleQualityData = data => {
  const param = {
    name: "综合效率",
    data: data.data
  };
  // 处理子组件返回的数据
  addDataIfNotExists(param);
  // 处理子组件返回的数据
  // 这里可以做进一步的数据处理或传递给其他组件
};
const handleNgcountData = data => {
  const param = {
    name: "ng统计",
    data: data.data
  };
  // 处理子组件返回的数据
  addDataIfNotExists(param);
};
// 重置
const handleReset = () => {
  // 重置searchParam

  searchParam.value = {
    time: [moment().startOf("day"), moment().endOf("day")] // 重置为当天日期范围
  };
};

const exportImgs = () => {
  console.log(searchParam, "searchParam");
  // 格式化开始时间和结束时间
  const startTime = moment(searchParam.value.time[0]).format("YYYY-MM-DD");
  const endTime = moment(searchParam.value.time[1]).format("YYYY-MM-DD");
  const exportItems = [
    { elementId: "chart4", fileName: `生产报表${startTime}-${endTime}.png` },
    { elementId: "chart3", fileName: `综合效率${startTime}-${endTime}.png` },
    { elementId: "chart2", fileName: `计划达成率${startTime}-${endTime}.png` },
    { elementId: "chart1", fileName: `ng统计${startTime}-${endTime}.png` }
  ];
  try {
    exportElementsAsImages(exportItems);
    ElMessage.success("图片批量导出成功");
  } catch (error) {
    ElMessage.error("图片批量导出失败，请查看错误提示");
  }
};
// 导出数据
const exportData = () => {
  // 这里应该替换为实际的导出 API 请求
  // handleExportExcel();
  // const ws = XLSX.utils.aoa_to_sheet(data);
  // const wb = XLSX.utils.book_new();
  // XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
  const wb = XLSX.utils.book_new();

  // 导出文件
  for (let index = 0; index < exportDataObj.value.length; index++) {
    const element = exportDataObj.value[index];
    const data = element.data.split("\n").map(row => row.split(","));
    const ws = XLSX.utils.aoa_to_sheet(data);
    XLSX.utils.book_append_sheet(wb, ws, element.name);
  }
  XLSX.writeFile(wb, "生产数据总览.xlsx");

  ElMessage.success("导出成功");
};
onMounted(() => {
  handleSearch();
});
</script>

<style lang="scss" scoped>
.charts {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
  width: 100%;
  height: 100%;

  // 根据图表数量和布局，设置合适的高度，这里以适应两个图表高度为例
  // height: calc(50% - 10px);
}

// 修改chart - item样式
.chart-item {
  display: flex;
  flex: 1 0 calc(50% - 10px); /* 每行两个，减去间距 */
  align-items: stretch; /* 内容撑满高度 */
  height: calc(50% - 1rem); /* 两行布局，高度50%减去间距 */
  overflow: hidden; /* 溢出隐藏 */
  background: white; /* 可选背景色 */
  border: 1px solid #e0e0e0; /* 可选边框 */
  border-radius: 8px; /* 可选圆角 */
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%); /* 可选阴影 */
}
.chart {
  width: 100%;
  height: 100%;
}

// 针对小屏幕的响应式设计
@media (width <= 768px) {
  .chart-item {
    // 小屏幕下单列显示，宽度为100%
    width: 100%;

    // 适当调整高度以适应小屏幕
    height: 100%;
  }
}

// .charts {
//   display: flex;
//   flex-wrap: wrap;
//   gap: 10px;
//   justify-content: space-between;
//   width: 100%;

//   // height: calc(50% - 20px); /* 设置高度为页面的一半减去边距 */
// }
// .chart-item {
//   box-sizing: border-box;

//   // width: calc(33.33% - 16px);
//   width: calc(50% - 10px); /* 设置宽度为页面的一半减去边距 */
//   margin-bottom: 10px; /* 添加底部间距 */
// }
// .chart {
//   width: 100%;
//   height: 100%;

//   // height: 80vw;
//   // height: 100px;
// }

// @media (width <= 768px) {
//   .chart-item {
//     width: 100%;
//     height: 100%;
//   }
// }
</style>
