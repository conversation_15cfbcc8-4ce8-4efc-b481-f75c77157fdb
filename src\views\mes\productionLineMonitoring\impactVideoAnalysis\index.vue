<template>
  <div class="h-full bg-gray-100 p-4 w-full">
    <iframe
      id="myIframe"
      src="http://*************:80/xauthplus-plugin/thirdLogin?type=third&service=http://*************:80/vms/playback&token=123"
      width="100%"
      height="100%"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script setup>
// import { ref } from "vue";
// import jiankong from "@/assets/img/jiankong.jpg";
// 定义监控点数据
// const monitorItems = ref([
//   {
//     id: 1,
//     imageUrl: jiankong,
//     title: "监控点1"
//     // description: "这里是监控点1的描述"
//   },
//   {
//     id: 2,
//     imageUrl: jiankong,
//     title: "监控点2"
//     // description: "这里是监控点2的描述"
//   },
//   {
//     id: 3,
//     imageUrl: jiankong,
//     title: "监控点3"
//     // description: "这里是监控点3的描述"
//   },
//   {
//     id: 4,
//     imageUrl: jiankong,
//     title: "监控点4"
//     // description: "这里是监控点4的描述"
//   },
//   {
//     id: 5,
//     imageUrl: jiankong,
//     title: "监控点5"
//     // description: "这里是监控点5的描述"
//   },
//   {
//     id: 6,
//     imageUrl: jiankong,
//     title: "监控点6"
//     // description: "这里是监控点6的描述"
//   }
// ]);
// onMounted(){
//   window.addEventListener('message', function (event) {
//             if (event.origin === 'https://*************') {
//                 // 接收到重定向 URL 消息，在 iframe 内进行重定向
//                 const iframe = document.getElementById('myIframe');
//                 iframe.src = event.data;
//             }
//         })
// }
// onMounted(() => {
//   window.addEventListener("message", function (event) {
//     if (event.origin === "https://*************") {
//       // 接收到重定向 URL 消息，在 iframe 内进行重定向
//       const iframe = document.getElementById("myIframe");
//       iframe.src = event.data;
//     }
//   });
// });
</script>

<style>
.bg-black-opacity {
  background-color: rgb(0 0 0 / 50%);
}
</style>
