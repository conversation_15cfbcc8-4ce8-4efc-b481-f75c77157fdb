<template>
  <div class="chart-container flex flex-col">
    <v-chart :autoresize="true" :option="options" class="chart card flex-1" ref="chartRef" />
    <div class="flex total-elec">
      <div class="data1">
        <div class="qiu">
          <p>{{ totalRealTimeElec }}{{ t("energyConsumption.current.electricity.unit") }}</p>
        </div>
        <span>{{ t("energyConsumption.current.electricity.realTimePower") }}</span>
      </div>
      <div class="data2 m-l-[30px]">
        <div class="qiu">
          <p>{{ totalElecConsumed }} {{ t("energyConsumption.current.electricity.unit") }}</p>
        </div>
        <span>{{ t("energyConsumption.current.electricity.totalPower") }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineProps, defineExpose } from "vue";
import { transformChart, exportMultipleTablesToExcel } from "@/utils";
import moment from "moment";
import { useI18n } from "vue-i18n";

const { t, locale } = useI18n();

const props = defineProps({
  elecData: {
    type: Object,
    default: () => ({})
  }
});

const chartRef = ref();

interface ChartOption {
  title: {
    text: string;
    left: string;
    textStyle: {
      color: string;
    };
  };
  grid: {
    top: string;
    left: string;
    right: string;
  };
  toolbox: {
    show: boolean;
    feature: {
      magicType: {
        type: string[];
        title: {
          bar: string;
          line: string;
        };
      };
      saveAsImage: { show: boolean };
      myExcelExport: {
        show: boolean;
        title: string;
        icon: string;
        onclick: () => void;
      };
    };
  };
  tooltip: {
    trigger: string;
    axisPointer: {
      type: string;
    };
    textStyle: {
      color: string;
    };
  };
  legend: {
    data: string[];
    bottom: number;
    textStyle: {
      color: string;
    };
  };
  xAxis: {
    type: string;
    data: string[];
    axisLabel: {
      color: string;
    };
  };
  yAxis: {
    type: string;
    name: string;
    axisLabel: {
      color: string;
    };
  };
  series: Array<{
    name: string;
    type: string;
    data: number[];
    label: {
      show: boolean;
      position: string;
      color: string;
    };
  }>;
}

const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");

const options = ref<ChartOption>({
  title: {
    text: "",
    left: "center",
    textStyle: {
      color: chartColor
    }
  },
  grid: {
    top: "30%",
    left: "5%",
    right: "5%"
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["bar", "line"],
        title: {
          bar: "",
          line: ""
        }
      },
      saveAsImage: { show: true },
      myExcelExport: {
        show: true,
        title: "",
        icon: ""
      }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow"
    },
    textStyle: {
      color: chartColor
    }
  },
  legend: {
    data: [],
    bottom: 10,
    textStyle: {
      color: chartColor
    }
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      color: chartColor
    }
  },
  yAxis: {
    type: "value",
    name: "",
    axisLabel: {
      color: chartColor
    }
  },
  series: []
});

// 计算实时消耗电能总和
const totalRealTimeElec = computed(() => {
  return props.elecData.powerData ? props.elecData.powerData.reduce((a, b) => a + b, 0).toFixed(2) : 0;
});

// 计算消耗能耗总和
const totalElecConsumed = computed(() => {
  return props.elecData.consumedData ? parseFloat(props.elecData.consumedData.reduce((a, b) => a + b, 0).toFixed(2)) : 0;
});

const exportExcel = () => {
  const arr1 = transformChart(chartRef.value, "机台");

  // 获取当前时间并格式化
  const currentTime = moment().format("YYYY:MM:DD_HH:mm:ss");

  // 将当前时间拼接到文件名中
  const fileName = `实时电能能耗_${currentTime}.xlsx`;

  exportMultipleTablesToExcel([arr1], ["电能"], fileName);
};

// 更新图表配置
const updateChartOptions = () => {
  options.value = {
    title: {
      text: t("energyConsumption.current.electricity.title"),
      left: "center",
      textStyle: {
        color: chartColor
      }
    },
    toolbox: {
      show: true,
      feature: {
        magicType: {
          type: ["bar", "line"],
          title: {
            bar: t("energyConsumption.current.electricity.switchToBar"),
            line: t("energyConsumption.current.electricity.switchToLine")
          }
        },
        saveAsImage: { show: true },
        myExcelExport: {
          icon: "path://M665.38 65.024c11.48 0 22.53 4.46 30.72 12.58l0.44 0.37 152.65 153.6c8.05 8.12 12.65 19.02 12.8 30.43v250.51a13.75 13.75 0 0 1-13.68 13.75h-28.6a13.75 13.75 0 0 1-13.75-13.75v-245.03L660.48 121.05H216.94v199.02h269.24c7.61 0 13.75 6.14 13.75 13.75v420.57a13.68 13.68 0 0 1-13.75 13.68H217.01v136.05h589.02v-24.72c0-7.61 6.14-13.75 13.68-13.75h28.53c7.61 0 13.75 6.14 13.75 13.75v44.62a35.99 35.99 0 0 1-35.33 36.06h-629.76a35.99 35.99 0 0 1-35.84-35.4V768h-83.38a13.68 13.68 0 0 1-13.68-13.75v-420.57c0-7.53 6.14-13.68 13.75-13.68H160.91V101.01c0-19.68 15.73-35.69 35.33-35.99h469.07zM361.33 437.98a54.86 54.86 0 0 0-42.13 19.53l-37.3 44.47-37.3-44.4a54.86 54.86 0 0 0-41.98-19.6h-30.13a6.88 6.88 0 0 0-5.27 11.26l79.51 94.72-79.51 94.72a6.88 6.88 0 0 0 5.27 11.26h30.28a54.86 54.86 0 0 0 41.98-19.6l37.16-44.32 37.3 44.32a54.86 54.86 0 0 0 41.98 19.6h30.21c5.85 0 9-6.8 5.27-11.26L317.22 543.96l79.51-94.72a6.88 6.88 0 0 0-5.19-11.26zm214.6-104.3c0-7.53 6.14-13.68 13.75-13.68h164.57c7.53 0 13.68 6.14 13.68 13.75v28.53a13.68 13.68 0 0 1-13.75 13.75h-164.57a13.68 13.68 0 0 1-13.68-13.75v-28.53zm13.75 102.33a13.68 13.68 0 0 0-13.75 13.68v28.6c0 7.61 6.14 13.75 13.75 13.75h164.57a13.68 13.68 0 0 0 13.68-13.75v-28.53a13.68 13.68 0 0 0-13.75-13.75h-164.57zm192 348.23a21.21 21.21 0 0 0-8.19 16.82v48.64c0 4.39 5.12 6.88 8.56 4.17l168.67-130.78a24.5 24.5 0 0 0 0-38.62L782.19 553.69a5.27 5.27 0 0 0-8.56 4.24v48.71c0 6.58 3 12.73 8.19 16.82l68.53 53.03H617.25a10.61 10.61 0 0 0-10.53 10.61v33.94c0 5.85 4.68 10.61 10.53 10.61h232.45l-67.95 52.66z",
          show: true,
          title: t("energyConsumption.current.electricity.exportExcel"),
          onclick: exportExcel
        }
      }
    },
    grid: {
      top: "30%",
      left: "5%",
      right: "5%"
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow"
      },
      textStyle: {
        color: chartColor
      }
    },
    legend: {
      data: [t("energyConsumption.current.electricity.powerConsumption"), t("energyConsumption.current.electricity.energyConsumption")],
      bottom: 10,
      textStyle: {
        color: chartColor
      }
    },
    xAxis: {
      type: "category",
      data: props.elecData.machines || [],
      axisLabel: {
        color: chartColor
      }
    },
    yAxis: {
      type: "value",
      name: t("energyConsumption.current.electricity.unit"),
      axisLabel: {
        color: chartColor
      }
    },
    series: [
      {
        name: t("energyConsumption.current.electricity.powerConsumption"),
        type: "bar",
        data: props.elecData.powerData || [],
        label: {
          show: true,
          position: "top",
          color: chartColor
        }
      },
      {
        name: t("energyConsumption.current.electricity.energyConsumption"),
        type: "bar",
        data: props.elecData.consumedData || [],
        label: {
          show: true,
          position: "top",
          color: chartColor
        }
      }
    ]
  };
};

// 初始化图表配置
updateChartOptions();

// 监听语言变化
watch(locale, () => {
  updateChartOptions();
});

// 监听数据变化
watch(
  () => props.elecData,
  () => {
    options.value.series[0].data = props.elecData.powerData;
    options.value.series[1].data = props.elecData.consumedData;
    options.value.xAxis.data = props.elecData.machines;
  },
  { deep: true }
);

defineExpose({
  chartRef // 暴露 v-chart 实例
});
</script>

<style scoped>
@import "./style.css";
.shittop {
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  box-shadow: 0 0 12px rgb(0 0 0 / 5%);
}
.chart-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.chart {
  width: 100%;
  height: 100%;
}

/* .total-elec {
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 1rem;
  font-weight: bold;
  
} */
.total-elec {
  position: absolute;
  top: 10px;
  left: 10px;
  font-size: 0.9rem;
  font-weight: bold;
  color: var(--el-text-color-regular);
}

/* 新增实时显示样式（可选） */
.total-elec:first-child {
  color: #42b983; /* 绿色区分 */
}
.total-elec:nth-child(2) {
  color: #2780da; /* 深蓝色 */
}
</style>
