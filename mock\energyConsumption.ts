import Mock from "mockjs";

// 统一响应格式（只读模式）
function mockResponse(data) {
  return {
    code: 200,
    msg: "请求成功",
    data: data,
    extras: null,
    time: new Date().toISOString().replace("T", "").split(".")[0]
  };
}

// 辅助函数，将数字转换为中文数字
Mock.Random.extend({
  toChineseNumber: function (num) {
    const chnNumChar = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
    return chnNumChar[num];
  }
});

export default [
  // 单机能耗信息表接口
  {
    url: "/sys/mes/SingleMachineEnergyConsumption",
    method: "get",
    response: () => {
      const data = Mock.mock({
        "list|1-10": [
          {
            "ID|+1": 1,
            factory: "@word(8)",
            workshop: "@word(4)",
            production_line: "@word(6)",
            machine: "@word(50)",
            station: "@word(50)",
            start_time: '@datetime("yyyy-MM-dd HH:mm:ss")',
            end_time: '@datetime("yyyy-MM-dd HH:mm:ss")',
            ElectricRealTimePower: "@float(0, 100, 0, 2)",
            GasRealTimeFlow: "@float(0, 100, 0, 2)",
            RecordTime: '@datetime("yyyy-MM-dd HH:mm:ss")'
          }
        ]
      }).list;
      return mockResponse(data);
    }
  },
  // 当前总电能能耗表接口
  {
    url: "/sys/mes/TotalElectricEnergyConsumption",
    method: "get",
    response: () => {
      const data = Mock.mock({
        "list|1-10": [
          {
            "ID|+1": 1,
            factory: "@word(8)",
            workshop: "@word(4)",
            production_line: "@word(6)",
            machine: "@word(50)",
            station: "@word(50)",
            start_time: '@datetime("yyyy-MM-dd HH:mm:ss")',
            end_time: '@datetime("yyyy-MM-dd HH:mm:ss")',
            ElecConsumed: "@float(0, 100, 0, 2)",
            RecTime: '@datetime("yyyy-MM-dd HH:mm:ss")'
          }
        ]
      }).list;
      return mockResponse(data);
    }
  },
  // 气能流量统计主表接口
  {
    url: "/sys/mes/GasFlowStatistics",
    method: "get",
    response: () => {
      const data = Mock.mock({
        "list|1-10": [
          {
            "ID|+1": 1,
            factory: "@word(8)",
            workshop: "@word(4)",
            production_line: "@word(6)",
            machine: "@word(50)",
            station: "@word(50)",
            start_time: '@datetime("yyyy-MM-dd HH:mm:ss")',
            end_time: '@datetime("yyyy-MM-dd HH:mm:ss")',
            AvgFlow: "@float(0, 100, 0, 2)",
            MaxFlow: "@float(0, 100, 0, 2)",
            TotalConsumption: "@float(0, 1000, 0, 2)"
          }
        ]
      }).list;
      return mockResponse(data);
    }
  },
  // 电能及气流能耗总览表接口
  {
    url: "/sys/mes/EnergyOverview",
    method: "get",
    response: () => {
      const data = Mock.mock({
        "list|1-10": [
          {
            "ID|+1": 1,
            factory: "@word(8)",
            workshop: "@word(4)",
            production_line: "@word(6)",
            machine: "@word(50)",
            start_time: '@datetime("yyyy-MM-dd HH:mm:ss")',
            end_time: '@datetime("yyyy-MM-dd HH:mm:ss")',
            total_electric_consumption: "@float(0, 1000, 0, 2)",
            total_gas_flow_consumption: "@float(0, 1000, 0, 2)"
          }
        ]
      }).list;
      return mockResponse(data);
    }
  }
];
