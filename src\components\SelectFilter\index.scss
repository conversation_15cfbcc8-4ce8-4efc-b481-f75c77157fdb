.select-filter {
  width: 100%;
  .select-filter-item {
    display: flex;
    align-items: center;
    border-bottom: 1px dashed var(--el-border-color-light);
    &:last-child {
      border-bottom: none;
    }
    .select-filter-item-title {
      margin-top: -2px;
      span {
        font-size: 14px;
        color: var(--el-text-color-regular);
        white-space: nowrap;
      }
    }
    .select-filter-notData {
      margin: 18px 0;
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
    .select-filter-list {
      display: flex;
      flex: 1;
      padding: 0;
      margin: 13px 0;
      li {
        display: flex;
        align-items: center;
        padding: 5px 15px;
        margin-right: 16px;
        font-size: 13px;
        color: var(--el-color-primary);
        list-style: none;
        cursor: pointer;
        background: var(--el-color-primary-light-9);
        border: 1px solid var(--el-color-primary-light-5);
        border-radius: 32px;
        &:hover {
          color: #ffffff;
          background: var(--el-color-primary);
          border-color: var(--el-color-primary);
          transition: 0.1s;
        }
        &.active {
          font-weight: bold;
          color: #ffffff;
          background: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }
        .el-icon {
          margin-right: 4px;
          font-size: 16px;
          font-weight: bold;
        }
        span {
          white-space: nowrap;
        }
      }
    }
  }
}
