import { moduleRequest } from "@/api/request";

const http = moduleRequest("/sys/mes/"); // 复用基础路径

// 告警信息表查询参数类型
interface AlarmAlarmInfoQueryParams {
  id?: number;
  station_name?: string;
  station_type?: string;
  alarm_name?: string;
  alarm_info?: string;
  start_time?: Date;
  duration_minutes?: number;
}

// 故障 Top10 表查询参数类型
interface AlarmFaultTop10QueryParams {
  id?: number;
  fault_name?: string;
  fault_count?: number;
}

// 故障类型 Top10 表查询参数类型
interface AlarmFaultTypeTop10QueryParams {
  id?: number;
  fault_type?: string;
  type_count?: number;
}

// 故障统计信息表查询参数类型
interface AlarmFaultStatsQueryParams {
  id?: number;
  mttr_minutes?: number;
  mtbf_minutes?: number;
  total_fault_time_minutes?: number;
  fault_count?: number;
}

// 故障汇总表查询参数类型
interface AlarmFaultSummaryQueryParams {
  id?: number;
  fault_time?: Date;
  fault_count?: number;
  failure_rate?: number;
  mttr_minutes?: number;
  mtbf_minutes?: number;
}

// 历史故障记录表查询参数类型
interface AlarmHistoricalFaultRecordQueryParams {
  id?: number;
  factory?: string;
  workshop?: string;
  workline?: string;
  machine?: string;
  station?: string;
  fault_type?: string;
  fault_name?: string;
  alarm_info?: string;
  start_time?: Date;
  duration_minutes?: number;
}

// 整线汇总表查询参数类型
interface AlarmWholeLineSummaryQueryParams {
  id?: number;
  top10_alarm_count?: number;
  top10_fault_time?: number;
  factory?: string;
  workshop?: string;
  workline?: string;
  machine?: string;
  station?: string;
  fault_type?: string;
  fault_name?: string;
  alarm_info?: string;
  start_time?: Date;
  duration_minutes?: number;
}

const alramAnalysisApi = {
  /** 告警信息 */
  getListMesReportData(params?: object) {
    return http.get("getListMesReportData", params);
  },
  /** 查询告警信息 */
  alarmAlarmInfoGet(params?: AlarmAlarmInfoQueryParams) {
    return http.get("alarm_alarm_info", params);
  },

  /** 查询故障 Top10 信息 */
  alarmFaultTop10Get(params?: AlarmFaultTop10QueryParams) {
    return http.get("alarm_fault_top10", params);
  },

  /** 查询故障类型 Top10 信息 */
  alarmFaultTypeTop10Get(params?: AlarmFaultTypeTop10QueryParams) {
    return http.get("alarm_fault_type_top10", params);
  },

  /** 查询故障统计信息 */
  alarmFaultStatsGet(params?: AlarmFaultStatsQueryParams) {
    return http.get("alarm_fault_stats", params);
  },

  /** 查询故障汇总信息 */
  alarmFaultSummaryGet(params?: AlarmFaultSummaryQueryParams) {
    return http.get("alarm_fault_summary", params);
  },

  /** 查询历史故障记录信息 */
  alarmHistoricalFaultRecordGet(params?: AlarmHistoricalFaultRecordQueryParams) {
    return http.get("alarm_historical_fault_record", params);
  },

  /** 查询整线汇总信息 */
  alarmWholeLineSummaryGet(params?: AlarmWholeLineSummaryQueryParams) {
    return http.get("alarm_whole_line_summary", params);
  },
  getTree(params?: object) {
    return http.get("alarm_historical_fault_recordGetTree", params);
  }
};

export { alramAnalysisApi };
