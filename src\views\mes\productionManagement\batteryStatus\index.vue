<!-- 
 * @Description: 电池状态
 * @Date: 2025-06-10
!-->
<template>
  <div class="main-box">
    <TreeFilter
      ref="treeFilter"
      label="name"
      title="工序类型"
      :request-api="batteryTypeApi.tree"
      :default-value="initParam.typeId"
      @change="changeTreeFilter"
    />
    <div class="table-box card">
      <!-- 查询条件区域 -->
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item>
          <el-radio-group v-model="queryParams.queryType">
            <el-radio label="battery">电芯</el-radio>
            <el-radio label="semi">半成品批次</el-radio>
            <el-radio label="material">原材料批次</el-radio>
            <el-radio label="workOrder">工单号</el-radio>
            <el-radio label="equipmentNo">设备号</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <el-input v-model="queryParams.queryValue" placeholder="请输入查询内容" clearable style="width: 300px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
          <el-button type="primary" @click="handleBatchQuery">批量查询</el-button>
        </el-form-item>
      </el-form>

      <!-- 标签页区域 -->
      <el-tabs v-model="activeTab" class="tabs-container">
        <el-tab-pane label="基本信息" name="basicInfo">
          <BasicInfo :query-params="queryParams" />
        </el-tab-pane>
        <el-tab-pane label="过站信息" name="stationInfo">
          <StationInfo :query-params="queryParams" />
        </el-tab-pane>
        <el-tab-pane label="过程参数" name="processParams">
          <ProcessParams :query-params="queryParams" />
        </el-tab-pane>
        <el-tab-pane label="检验数据" name="inspectionData">
          <InspectionData :query-params="queryParams" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts" name="batteryStatus">
import { ref, reactive } from "vue";
import TreeFilter from "@/components/TreeFilter/index.vue";
import BasicInfo from "./components/basicInfo.vue";
import StationInfo from "./components/stationInfo.vue";
import ProcessParams from "./components/processParams.vue";
import InspectionData from "./components/inspectionData.vue";

// 电池工序树形结构数据
const treeData = [
  {
    id: 1,
    name: "正极电极段",
    children: [
      { id: 11, name: "正极高速辊压" },
      { id: 12, name: "正极陶瓷涂片" },
      { id: 13, name: "正极涂布分切" },
      { id: 14, name: "正极辊压" },
      { id: 15, name: "正极激光模切" }
    ]
  },
  {
    id: 2,
    name: "负极电极段",
    children: [
      { id: 21, name: "负极涂片" },
      { id: 22, name: "负极涂布分切" },
      { id: 23, name: "负极辊压" },
      { id: 24, name: "负极激光模切" }
    ]
  },
  {
    id: 3,
    name: "电芯段",
    children: [
      { id: 31, name: "切叠" },
      { id: 32, name: "X-ray" },
      { id: 33, name: "超声波焊接&倒厚度" },
      { id: 34, name: "负极极耳激光焊接" },
      { id: 35, name: "包mylar" }
    ]
  }
];

// 模拟的树形API
const batteryTypeApi = {
  tree: () => Promise.resolve({ data: treeData })
};

// 树形筛选器引用
const treeFilter = ref(null);

// 初始化参数
interface InitParam {
  typeId: number | string;
}
const initParam = reactive<InitParam>({ typeId: 0 });

// 查询参数
interface QueryParams {
  queryType: string;
  queryValue: string;
  typeId: number | string;
}
const queryParams = reactive<QueryParams>({
  queryType: "battery",
  queryValue: "",
  typeId: 0
});

// 当前激活的标签页
const activeTab = ref("basicInfo");

/**
 * 树形筛选器变更事件
 */
function changeTreeFilter(val: number | string) {
  if (val !== "") {
    queryParams.typeId = val;
  } else {
    queryParams.typeId = 0;
  }
}

/**
 * 查询按钮点击事件
 */
function handleQuery() {
  console.log("查询参数:", queryParams);
  // 根据当前激活的标签页，调用对应组件的查询方法
}

/**
 * 重置查询条件
 */
function resetQuery() {
  queryParams.queryValue = "";
  // 保留树形筛选器选中的值和查询类型
}

/**
 * 批量查询按钮点击事件
 */
function handleBatchQuery() {
  console.log("执行批量查询");
  // 实现批量查询逻辑
}
</script>

<style lang="scss" scoped>
// .main-box {
//   display: flex;
//   width: 100%;
//   height: 100%;
//   .table-box {
//     display: flex;
//     flex: 1;
//     flex-direction: column;
//     padding: 10px;
//     .search-form {
//       margin-bottom: 10px;
//     }
//     .tabs-container {
//       flex: 1;
//       overflow: auto;
//     }
//   }
// }
</style>
