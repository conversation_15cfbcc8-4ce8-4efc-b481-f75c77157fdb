<template>
  <div class="flex items-center justify-center gap-1 bg-[--el-bg-color] rounded shadow-xs shitmachine">
    <!-- 左侧按钮 -->
    <el-button type="text" :icon="ArrowLeftBold" size="small" @click="switchMachine('prev')" class="!p-1 !w-6 !h-6 hover:!bg-gray-100" />

    <!-- 带动画的内容区域 -->
    <div class="min-w-[100px] overflow-hidden relative h-6">
      <transition :name="transitionName" mode="out-in">
        <div :key="currentIndex" class="absolute inset-0 flex items-baseline justify-center gap-1.5 px-1.5">
          <span class="text-[var(--el-button-text-color,var(--el-text-color-regular))] truncate font-bold text-lg">
            {{ currentMachine }}
          </span>
          <span class="text-[10px] text-gray-400"> {{ currentIndex + 1 }}/{{ machineList.length }} </span>
        </div>
      </transition>
    </div>

    <!-- 右侧按钮 -->
    <el-button type="text" :icon="ArrowRightBold" size="small" @click="switchMachine('next')" class="!p-1 !w-6 !h-6 hover:!bg-gray-100" />
  </div>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { ElButton } from "element-plus";
import { ArrowLeftBold, ArrowRightBold } from "@element-plus/icons-vue";

const props = defineProps({
  machineList: {
    type: Array,
    default: () => []
  },
  currentMachineIndex: {
    // 新增：接收父组件的当前索引
    type: Number,
    default: 0
  }
});

// const currentIndex = ref(0);
// 使用 props.currentMachineIndex 初始化 currentIndex
const currentIndex = ref(props.currentMachineIndex);

// 监听 props.currentMachineIndex 的变化并同步到内部状态
watch(
  () => props.currentMachineIndex,
  newVal => {
    currentIndex.value = newVal;
  }
);
const transitionName = ref("slide-next");

// 计算 currentMachine，依赖于 currentIndex
const currentMachine = computed(() => {
  return props.machineList[currentIndex.value]?.name || "--";
});

const emit = defineEmits(["change"]);
const switchMachine = direction => {
  const total = props.machineList.length;
  if (total === 0) return;

  transitionName.value = direction === "next" ? "slide-next" : "slide-prev";

  currentIndex.value = direction === "next" ? (currentIndex.value + 1) % total : (currentIndex.value - 1 + total) % total;
  emit("change", currentIndex.value);
};
// 暴露更新索引的方法
defineExpose({
  setCurrentIndex: index => {
    currentIndex.value = index;
  }
});
</script>

<style>
/* 切换动画 */
.slide-next-enter-active,
.slide-next-leave-active,
.slide-prev-enter-active,
.slide-prev-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.slide-next-enter-from {
  opacity: 0;
  transform: translateX(20px);
}
.slide-next-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
.slide-prev-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}
.slide-prev-leave-to {
  opacity: 0;
  transform: translateX(20px);
}
</style>
