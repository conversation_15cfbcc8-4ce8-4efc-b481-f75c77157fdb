<script setup lang="ts">
// import { exportElementsAsImages } from "@/utils";
// import domtoimage from "dom-to-image";
import * as htmlToImage from "html-to-image";

// import { RouterView } from "vue-router";
import ScaleScreen from "@/components/scale-screen";
import Headers from "./header.vue";
import moment from "moment";
// import { useUserStore } from "@/stores/modules/user";
// const userStore = useUserStore();
// const isAlarm = computed(() => userStore.isAlarm);
// import { uploadFeishuImage } from "@/utils";

// 获取当前路由信息
// import { exportElementsAsImages } from "@/utils";
// import { exportElementsAsImages } from "@/utils";
// import moment from "moment";

const handleKeyDown = () => {
  const shitScreen = document.querySelector(".el-main");
  if (document.fullscreenElement) {
    document.exitFullscreen();
  } else if (shitScreen) {
    shitScreen.requestFullscreen();
  }
};

onMounted(async () => {
  const element = document.getElementById("shitboard");

  element?.addEventListener("dblclick", handleKeyDown);
  await nextTick();

  // 3. 执行截图上传
  // uploadFeishuImage("shitboard");
});

onBeforeUnmount(() => {
  const element = document.getElementById("shitboard");

  element?.removeEventListener("dblclick", handleKeyDown);
});
const screenContainer = ref();
// 生成图片并上传

// watch(isAlarm, newValue => {
//   if (newValue) {
//     uploadFeishuImage("shitboard");
//   }
// });

const handleExportData = async () => {
  const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");
  // const exportItems = [{ elementId: "shit-screen", fileName: `气能${currentTime}.png` }];
  // try {
  //   exportElementsAsImages(exportItems);
  //   ElMessage.success("图片批量导出成功");
  // } catch (error) {
  //   ElMessage.error("图片批量导出失败，请查看错误提示");
  // }

  htmlToImage
    .toPng(document.getElementById("shitboard"), { quality: 0.95 })
    .then(function (dataUrl) {
      const link = document.createElement("a");
      link.download = `大屏实时${currentTime}.png`;
      link.href = dataUrl;
      link.click();
      ElMessage.success("图片批量导出成功");
    })
    .catch(err => {
      console.log(err, "err");
    });
};
// import autofit from "autofit.js";
const wrapperStyle = {};
// const testupload = () => {
//   uploadFeishuImage("shitboard", "shitboard");
// };
// const shitboard = ref();
</script>

<template>
  <div style="width: 100%; height: 100%" id="shitboard">
    <scale-screen :delay="500" :full-screen="true" :wrapper-style="wrapperStyle" :auto-scale="true">
      <div class="content_wrap" id="648038545621061">
        <!-- <el-button @click="testupload" class="absolute z-30" type="primary">测试</el-button> -->

        <Headers @export-data="handleExportData" id="ookk" />
        <DashBoard ref="screenContainer" id="ookk1"></DashBoard>
      </div>
    </scale-screen>
  </div>
</template>
<style lang="scss" scoped>
.content_wrap {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 16px;
  background-image: url("@/assets/images/53bg.png");
  background-position: center center;
  background-size: cover;
}

/* 路由切换动画 */

/* fade-transform */
.fade-leave-active,
.fade-enter-active {
  transition: all 0.3s;
}

/* 可能为enter失效，拆分为 enter-from和enter-to */
.fade-enter-from {
  opacity: 0;
  transform: translateY(-30px);
}
.fade-enter-to {
  opacity: 1;
  transform: translateY(0);
}
.fade-leave-to {
  opacity: 0;
  transform: translateY(30px);
}
</style>
