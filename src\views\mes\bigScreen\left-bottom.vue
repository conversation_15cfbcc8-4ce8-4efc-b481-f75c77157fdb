<template>
  <v-chart :option="chartOptions" class="chart" autoresize />
</template>

<script setup>
import { ref, onMounted } from "vue";
import { chartColor } from "@/assets/const/index.ts";
import { graphic } from "echarts/core";
// 注册 VChart 组件
const chartOptions = ref({});

onMounted(() => {
  chartOptions.value = {
    // title: {
    //   text: '质量',
    //   left: 'center'
    // },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross"
      }
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["8:00", "9:00", "10:00", "11:00", "12:00", "13:00", "14:00", "15:00", "16:00", "17:00", "18:00"],
      axisLabel: {
        color: chartColor.fontColor // 使用外部引入的 fontColor 变量
      }
    },
    grid: {
      //布局
      show: true,
      left: "10px",
      right: "30px",
      bottom: "10px",
      top: "32px",
      containLabel: true,
      borderColor: "none"
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: chartColor.fontColor // 使用外部引入的 fontColor 变量
      },
      splitLine: {
        lineStyle: {
          color: chartColor.splitLine
        }
      }
    },
    series: [
      {
        name: "质量",
        type: "line",
        smooth: true,
        symbol: "none",
        areaStyle: {
          color: new graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: chartColor.mainColor }, // 颜色深浅通过 rgba 调整
            { offset: 1, color: chartColor.mainColor + "10" }
          ])
        },
        data: [20, 50, 30, 40, 45, 50, 40, 30, 45, 30, 50]
      }
    ]
  };
});
</script>

<style scoped lang="scss">
.chart {
  // width: 100%;
  // height: 400px;
  border: none; // 移除边框
}
</style>
