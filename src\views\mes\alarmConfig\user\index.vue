<!-- 
 * @Description: 用户管理
 * @Author: huguodong 
 * @Date: 2023-12-15 15:46:03
!-->
<template>
  <div class="main-box">
    <TreeFilter
      ref="treeFilter"
      label="name"
      :title="t('user.organizationList')"
      :request-api="sysOrgApi.tree"
      :default-value="initParam.orgId"
      @change="changeTreeFilter"
    />
    <div class="table-box">
      <ProTable ref="proTable" :title="t('user.title')" :columns="columns" :request-api="sysUserApi.page" :init-param="initParam">
        <!-- 表格 header 按钮 -->
        <!-- <template #tableHeader>
          <el-button type="primary" @click="onOpen1(FormOptEnum.EDIT)">预警时间配置</el-button>
        </template> -->
        <!-- 状态 -->
        <template #tableHeader>
          <el-button type="success" @click="importData">{{ t("user.import") }}</el-button>
          <el-button type="primary" @click="exportData">{{ t("user.export") }}</el-button>
        </template>
        <template #avatar="scope">
          <el-avatar :src="scope.row.avatar" :size="30" />
          <!-- <span>{{ scope.row.avatar }}}</span> -->
        </template>
        <!-- 状态 -->
        <template #isWarn="scope">
          <el-switch :model-value="scope.row.isWarn === CommonStatusEnum.ENABLE" :loading="switchLoading" @change="() => editStatus(scope.row)" />
        </template>
        <!-- 操作 -->
        <template #operation="scope">
          <el-space>
            <s-button link :opt="FormOptEnum.EDIT" @click="onOpen(FormOptEnum.EDIT, scope.row)">{{ $t("user.form.editor") }} </s-button>
            <s-button
              link
              :opt="FormOptEnum.DELETE"
              @click="onDelete([scope.row.id], `${t('user.form.delete')}【${scope.row.account}】${t('user.form.user')}`)"
              >{{ $t("user.form.delete") }}
            </s-button>
            <el-dropdown @command="handleCommand">
              <el-link type="primary" :underline="false" :icon="ArrowDown">{{ $t("user.form.mores") }} </el-link>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="(item, index) in cmdEnum" :key="index" :command="command(scope.row, item)">{{ item }}</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </el-space>
        </template>
      </ProTable>
      <!-- 新增/编辑表单 -->
      <Form ref="formRef" />
      <!-- 角色选择器组件 -->
      <role-selector
        ref="roleSelectorRef"
        multiple
        :org-tree-api="sysOrgApi.tree"
        :role-selector-api="sysRoleApi.roleSelector"
        @successful="handleChooseRole"
      ></role-selector>
      <!-- 授权资源组件 -->
      <GrantResource user ref="grantResourceRef" />
      <!-- 授权权限组件 -->
      <GrantPermission user ref="grantPermissionRef" />
    </div>
    <el-dialog v-model="importDialogVisible" :title="t('user.import')">
      <el-upload ref="uploadRef" action="/api/sys/upload/UploadExcelryxx" :auto-upload="false" :before-upload="beforeUpload">
        <el-button type="primary">{{ t("user.selectFile") }}</el-button>
      </el-upload>
      <template #footer>
        <el-button @click="importDialogVisible = false">{{ t("user.cancel") }}</el-button>
        <el-button type="primary" @click="startImport">{{ t("user.startImport") }}</el-button>
      </template>
    </el-dialog>
    <Form1 ref="formRef1" />
  </div>
</template>

<script setup lang="ts" name="SysUser">
import { sysOrgApi, sysUserApi, SysUser, SysRole, sysRoleApi } from "@/api";
import { ArrowDown } from "@element-plus/icons-vue";
import { ProTableInstance } from "@/components/ProTable/interface";
// import { SysDictEnum, FormOptEnum } from "@/enums";
import { FormOptEnum, CommonStatusEnum } from "@/enums";

import { useHandleData } from "@/hooks/useHandleData";
// import { useDictStore } from "@/stores/modules";
import TreeFilter from "@/components/TreeFilter/index.vue";
import Form from "./components/form/index.vue";
// import { ElMessage } from "element-plus";
import { RoleSelectorInstance } from "@/components/Selectors/RoleSelector/interface";
import GrantResource from "../../../sys/limit/role/components/grantResource.vue";
import GrantPermission from "../../../sys/limit/role/components/grantPermission.vue";
import Form1 from "../Form.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const importDialogVisible = ref(false);

// const dictStore = useDictStore(); //字典仓库
const importData = () => {
  importDialogVisible.value = true;
};

// 状态选项
// const statusOptions = dictStore.getDictList(SysDictEnum.COMMON_STATUS);
const treeFilter = ref<InstanceType<typeof TreeFilter> | null>(null);
//状态开关loading
const switchLoading = ref(false);

interface InitParam {
  orgId: number | string;
}
// 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive<InitParam>({ orgId: 0 });
// 获取 ProTable 元素
const proTable = ref<ProTableInstance>();
const formRef1 = ref<InstanceType<typeof Form> | null>(null);
// function onOpen1(opt: any) {
//   formRef1.value?.onOpen({ opt: opt });
// }
const uploadRef = ref(null);

const startImport = () => {
  uploadRef.value!.submit();
  importDialogVisible.value = false;
  ElMessage.success(t("user.importSuccess"));
  RefreshTable();
};
// 表格配置项
const columns = computed(() => [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "searchKey", label: t("user.form.nameOrAccount"), search: { el: "input" }, isShow: false },
  { prop: "avatar", label: t("user.form.avatar"), width: 80 },
  { prop: "account", label: t("user.form.account") },
  { prop: "name", label: t("user.form.name") },
  { prop: "gender", label: t("user.form.gender"), width: 80 },
  { prop: "phone", label: t("user.form.phone") },
  { prop: "orgName", label: t("user.form.organization") },
  { prop: "positionName", label: t("user.form.position") },
  { prop: "createTime", label: t("user.form.createTime") },
  { prop: "operation", label: t("user.form.operation"), width: 230, fixed: "right" }
]);

/** 部门切换 */
function changeTreeFilter(val: number | string) {
  proTable.value!.pageable.pageNum = 1;
  if (val != "") {
    // 如果传入的val不为空，则将val赋值给initParam.parentId
    initParam.orgId = val;
  } else {
    // 否则将initParam.parentId赋值为0
    initParam.orgId = 0;
  }
}

// 表单引用
const formRef = ref<InstanceType<typeof Form> | null>(null);

/**
 * 打开表单
 * @param opt  操作类型
 * @param record  记录
 */
function onOpen(opt: FormOptEnum, record: {} | SysUser.SysUserInfo = {}) {
  formRef.value?.onOpen({ opt: opt, record: record, successful: RefreshTable });
}

/**
 * 删除
 * @param ids  id数组
 */
async function onDelete(ids: string[], msg: string) {
  // 二次确认 => 请求api => 刷新表格
  await useHandleData(sysUserApi.delete, { ids }, msg);
  RefreshTable();
}

/**
 * 刷新表格
 */
function RefreshTable() {
  proTable.value?.refresh(); //刷新表格
  treeFilter.value?.refresh(); //刷新树形筛选器
}

/** 更多下拉菜单命令枚举 */
// enum cmdEnum {
//   ResetPassword = "user.more.resetPassword",
//   GrantRole = "user.more.grantRole",
//   GrantResource = "user.more.grantResource",
//   GrantPermission = "user.more.grantPermission"
// }
// ✅ 修复后：使用常量对象替代枚举
const cmdEnum = {
  ResetPassword: t("user.more.resetPassword"),
  GrantRole: t("user.more.grantRole"),
  GrantResource: t("user.more.grantResource"),
  GrantPermission: t("user.more.grantPermission")
} as const;

/** 下拉菜单参数接口 */
// interface Command {
//   row: SysUser.SysUserInfo;
//   command: cmdEnum;
// }
/** 下拉菜单参数接口 */
interface Command {
  row: SysUser.SysUserInfo;
  command: keyof typeof cmdEnum; // 改为键名类型
}
/**配置command的参数 */
function command(row: SysUser.SysUserInfo, command: cmdEnum): Command {
  return {
    row: row,
    command: command
  };
}

const roleSelectorRef = ref<RoleSelectorInstance>(); //角色选择器引用
const userId = ref<number | string>(0); //用户id
const grantResourceRef = ref<InstanceType<typeof GrantResource> | null>(null); //授权资源组件引用
const grantPermissionRef = ref<InstanceType<typeof GrantPermission> | null>(null); //授权权限组件引用
/**
 * 更多下拉菜单点击事件
 * @param command
 */
function handleCommand(command: Command) {
  switch (command.command) {
    case cmdEnum.ResetPassword:
      useHandleData(sysUserApi.resetPassword, { id: command.row.id }, cmdEnum.ResetPassword);
      break;
    case cmdEnum.GrantRole:
      userId.value = command.row.id; //获取用户id
      sysUserApi.ownRole({ id: command.row.id }).then(res => {
        roleSelectorRef.value?.showSelector(res.data); //显示用户选择器
      });
      break;
    case cmdEnum.GrantResource:
      // 打开授权资源组件
      grantResourceRef.value?.onOpen({
        opt: FormOptEnum.EDIT,
        record: command.row,
        successful: RefreshTable
      });
      break;
    case cmdEnum.GrantPermission:
      // 打开授权权限组件
      grantPermissionRef.value?.onOpen({
        opt: FormOptEnum.EDIT,
        record: command.row,
        successful: RefreshTable
      });
      break;
  }
}

/** 选择角色 */
function handleChooseRole(data: SysRole.SysRoleInfo[]) {
  //组装参数
  const grantUser: SysUser.GrantRoleReq = {
    id: userId.value,
    roleIdList: data.map(item => item.id) as number[] | string[]
  };
  sysUserApi.grantRole(grantUser); //授权角色
}

/**
 * 修改状态
 * @param row  当前行数据
 */
function editStatus(row: any) {
  switchLoading.value = true;
  const isWarn = row.isWarn === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE; // 状态取反
  sysUserApi
    .EditIsWarn({ ...row, isWarn: isWarn })
    .then(() => {
      switchLoading.value = false;
      row.isWarn = isWarn;
    })
    .catch(() => {
      switchLoading.value = false;

      // onClose();
    });
  // sysUserApi.updateStatus({ id: row.id }, isWarn).then(() => {
  //   switchLoading.value = false;
  //   // ElMessage.success("修改成功");
  //   row.isWarn = isWarn;
  // });
}
</script>

<style lang="scss" scoped></style>
