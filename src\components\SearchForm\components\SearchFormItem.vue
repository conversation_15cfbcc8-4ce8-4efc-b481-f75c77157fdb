<template>
  <component
    :is="column.search?.isCustom ? column.search?.render : (column.search?.render ?? `el-${column.search?.el}`)"
    v-bind="{ ...handleSearchProps, ...placeholder, searchParam: _searchParam, clearable }"
    v-on="{
      ...column.search?.on,
      'update:modelValue': val => updateModelValue(val)
    }"
    v-model.trim="_searchParam[column.search?.key ?? handleProp(column.prop!)]"
    :data="column.search?.el === 'tree-select' ? columnEnum : []"
    :options="['cascader', 'select-v2'].includes(column.search?.el!) ? columnEnum : []"
  >
    <template v-if="column.search?.el === 'cascader'" #default="{ data }">
      <span>{{ data[fieldNames.label] }}</span>
    </template>
    <!-- Select 选项渲染 -->
    <template v-if="column.search?.el === 'select'">
      <component
        :is="`el-option`"
        v-for="(col, index) in columnEnum"
        :key="index"
        :label="col[fieldNames.label] ?? '未知标签'"
        :value="col[fieldNames.value] ?? null"
      ></component>
    </template>
    <!-- <template v-if="column.search?.el === 'select'">
      <component
        :is="`el-option`"
        v-for="(col, index) in columnEnum"
        :key="index"
        :label="col[fieldNames.label]"
        :value="col[fieldNames.value]"
      ></component>
    </template> -->
    <slot v-else></slot>
  </component>
</template>

<script setup lang="ts" name="SearchFormItem">
import { computed, inject, ref } from "vue";
import { handleProp } from "@/utils";
import { ColumnProps } from "@/components/ProTable/interface";
const updateModelValue = (val: any) => {
  _searchParam.value[props.column.search?.key ?? handleProp(props.column.prop!)] = val;
  // 手动触发change事件
  if (props.column.search?.on?.change) {
    props.column.search.on.change(val);
  }
};
interface SearchFormItem {
  column: ColumnProps;
  searchParam: { [key: string]: any };
}
const props = defineProps<SearchFormItem>();

// Re receive SearchParam
const _searchParam = computed(() => props.searchParam);

// 判断 fieldNames 设置 label && value && children 的 key 值
const fieldNames = computed(() => {
  return {
    label: props.column.fieldNames?.label ?? "label",
    value: props.column.fieldNames?.value ?? "value",
    children: props.column.fieldNames?.children ?? "children"
  };
});

// 接收 enumMap (el 为 select-v2 需单独处理 enumData)
// const enumMap = inject("enumMap", ref(new Map()));
// 在 script setup 中
const enumMap = inject("enumMap", ref(new Map())) as Ref<Map<string, any[]>>;
// const columnEnum = computed(() => {
//   let enumData = enumMap.value.get(props.column.prop);
//   console.log(enumData, 123123);

//   if (!enumData) return [];
//   if (props.column.search?.el === "select-v2" && props.column.fieldNames) {
//     enumData = enumData.map((item: { [key: string]: any }) => {
//       return { ...item, label: item[fieldNames.value.label], value: item[fieldNames.value.value] };
//     });
//   }
//   console.log(enumData, "enumData");
//   return enumData;
// });
// SearchFormItem.vue 中的 columnEnum 计算属性
const columnEnum = computed(() => {
  // 为 options 提供默认值
  const options = props.column.search?.props?.options ?? [];

  // 若为普通 select 且已设置 options，直接使用
  if (props.column.search?.el === "select" && options.length > 0) {
    return options;
  }

  let enumData = enumMap.value.get(props.column.prop);
  if (!enumData) return [];

  // 处理 select-v2 的字段映射
  if (props.column.search?.el === "select-v2" && props.column.fieldNames) {
    enumData = enumData.map((item: any) => {
      const labelField = fieldNames.value.label;
      const valueField = fieldNames.value.value;

      // 防御性代码：确保 item 包含所需字段
      const label = item[labelField] ?? "未知标签";
      const value = item[valueField] ?? null;

      return { ...item, label, value };
    });
  }

  return enumData;
});
// const columnEnum = computed(() => {
//   console.log(1357908642, props.column.search?.props?.options);

//   // 若为普通 select 且已设置 options，直接使用
//   if (props.column.search?.el === "select" && props.column.search?.props?.options) {
//     return props.column.search?.props?.options;
//   }
//   let enumData = enumMap.value.get(props.column.prop);
//   if (!enumData) return [];
//   if (props.column.search?.el === "select-v2" && props.column.fieldNames) {
//     enumData = enumData.map((item: { [key: string]: any }) => ({
//       ...item,
//       label: item[fieldNames.value.label],
//       value: item[fieldNames.value.value]
//     }));
//   }
//   return enumData;
// });

// 处理透传的 searchProps (el 为 tree-select、cascader 的时候需要给下默认 label && value && children)
const handleSearchProps = computed(() => {
  const label = fieldNames.value.label;
  const value = fieldNames.value.value;
  const children = fieldNames.value.children;
  const searchEl = props.column.search?.el;
  let searchProps = props.column.search?.props ?? {};
  if (searchEl === "tree-select") {
    searchProps = { ...searchProps, props: { ...searchProps, label, children }, nodeKey: value };
  }
  if (searchEl === "cascader") {
    searchProps = { ...searchProps, props: { ...searchProps, label, value, children } };
  }
  return searchProps;
});

// 处理默认 placeholder
const placeholder = computed(() => {
  const search = props.column.search;
  if (["datetimerange", "daterange", "monthrange"].includes(search?.props?.type) || search?.props?.isRange) {
    return {
      rangeSeparator: search?.props?.rangeSeparator ?? "至",
      startPlaceholder: search?.props?.startPlaceholder ?? "开始时间",
      endPlaceholder: search?.props?.endPlaceholder ?? "结束时间"
    };
  }
  const label = props.column.label ?? "";
  const placeholder = search?.props?.placeholder ?? (search?.el?.includes("input") ? "请输入" + label : "请选择" + label);
  return { placeholder };
});

// 是否有清除按钮 (当搜索项有默认值时，清除按钮不显示)
const clearable = computed(() => {
  const search = props.column.search;
  return search?.props?.clearable ?? (search?.defaultValue == null || search?.defaultValue == undefined);
});
</script>
