<template>
  <router-link to="/mes/alram-analysis/sheet-count">
    <div class="dashboard-chart">
      <!-- 故障次数统计图表 -->
      <div class="chart-container glow-effect" @mouseenter="handleChartHover" @mouseleave="handleChartLeave">
        <div class="chart-title">{{ $t("chart.faultCount") }}</div>
        <v-chart ref="countChartRef" :autoresize="true" :option="countChartOptions" :theme="chartTheme" class="chart-instance" />
      </div>
    </div>
  </router-link>
</template>

<script setup lang="ts">
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { BarChart } from "echarts/charts";
import { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from "echarts/components";
import { graphic } from "echarts/core";
import { useI18n } from "vue-i18n";

use([Can<PERSON><PERSON>ender<PERSON>, <PERSON><PERSON>hart, GridComponent, TooltipComponent, LegendComponent, TitleComponent]);

const i18n = useI18n();

// 定义props
const props = defineProps({
  data: {
    type: Object,
    required: true,
    validator: value => {
      return "次数统计" in value && value.次数统计.every(item => "machine" in item && "fault_count" in item);
    }
  },
  timeData: {
    type: Array,
    default: () => []
  },
  theme: {
    type: String,
    default: "dark"
  }
});

// 图表引用
const countChartRef = ref();

// 图表主题
const chartTheme = computed(() => (props.theme === "dark" ? "dark" : "light"));

// 颜色方案
const getRandomColor = () =>
  new graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "#3B7BFF" },
    { offset: 1, color: "#00D1FF" }
  ]);

const COLOR_PALETTE = {
  dark: {
    primary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: getRandomColor() },
      { offset: 1, color: getRandomColor() }
    ]),
    secondary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#FF7EB3" },
      { offset: 1, color: "#FF758C" }
    ]),
    background: "rgba(8, 18, 44, 0.8)",
    font: "#E1F3FF",
    axisLine: "rgba(200, 200, 255, 0.2)",
    splitLine: "rgba(100, 150, 255, 0.1)"
  },
  light: {
    primary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#4285F4" },
      { offset: 1, color: "#34A853" }
    ]),
    secondary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#EA4335" },
      { offset: 1, color: "#FBBC05" }
    ]),
    background: "rgba(255, 255, 255, 0.9)",
    font: "#333333",
    axisLine: "rgba(0, 0, 0, 0.1)",
    splitLine: "rgba(0, 0, 0, 0.05)"
  }
};

const colors = computed(() => COLOR_PALETTE[chartTheme.value]);

// 处理后的数据
const sortedCountData = computed(() => (props.data.次数统计 ? [...props.data.次数统计].sort((a, b) => b.fault_count - a.fault_count) : []));

// 图表配置 - 次数统计图表
const countChartOptions = computed(() => ({
  backgroundColor: "transparent",
  grid: { top: "20%", left: "3%", right: "4%", bottom: "5%", containLabel: true },
  tooltip: {
    trigger: "axis",
    backgroundColor: "rgba(0, 30, 80, 0.9)",
    padding: [10, 15],
    textStyle: { color: colors.value.font, fontSize: 14 },
    formatter: params => `
      <div>${params[0].name}</div>
      <div>${i18n.t("chart.faultCountUnit")}：${params[0].value}</div>
    `
  },
  xAxis: {
    type: "category",
    data: sortedCountData.value.map(item => item.machine),
    axisLabel: { color: colors.value.font, fontSize: 12, rotate: 30 }
  },
  yAxis: {
    type: "value",
    name: i18n.t("chart.faultCountUnit"),
    nameTextStyle: { color: colors.value.font, padding: [0, 0, 0, 40] },
    axisLabel: { color: colors.value.font }
  },
  series: [
    {
      type: "bar",
      barWidth: "45%",
      data: sortedCountData.value.map(item => ({
        value: item.fault_count,
        itemStyle: { color: getRandomColor(), borderRadius: 6 }
      })),
      label: { show: true, position: "top", color: colors.value.font, fontSize: 12 }
    }
  ]
}));

// 图表交互
const handleChartHover = () => {
  countChartRef.value?.dispatchAction({ type: "highlight", seriesIndex: 0 });
};

const handleChartLeave = () => {
  countChartRef.value?.dispatchAction({ type: "downplay", seriesIndex: 0 });
};

defineExpose({
  countChartRef
});
</script>

<style scoped lang="scss">
.dashboard-chart {
  width: 100%;
  height: 100%;
}
.chart-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background: rgb(255 255 255 / 5%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 8px 25px rgb(0 180 255 / 30%);
    transform: translateY(-5px);
    &::before {
      left: 100%;
    }
  }
  &::before {
    position: absolute;
    top: 0;
    left: -100%;
    z-index: 10;
    width: 100%;
    height: 2px;
    content: "";
    background: linear-gradient(90deg, transparent, rgb(0 180 255 / 80%), transparent);
    transition: 0.5s;
  }
}
.chart-title {
  padding: 16px 20px 12px;
  font-size: 18px;
  font-weight: 600;
  color: v-bind("colors.font");
  background: linear-gradient(90deg, rgb(0 120 255 / 10%), transparent);
  border-bottom: 1px solid rgb(255 255 255 / 5%);
}
.chart-instance {
  flex: 1;
  width: 100%;
  height: 100%;
}
.glow-effect {
  position: relative;
  overflow: hidden;
  &::after {
    position: absolute;
    inset: 0;
    pointer-events: none;
    content: "";
    background: radial-gradient(circle at 20% 30%, rgb(0 180 255 / 10%), transparent 60%);
  }
}
</style>
