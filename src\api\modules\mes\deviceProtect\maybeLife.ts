import { moduleRequest } from "@/api/request";
// const http = moduleRequest("/sys/upload/uploadExcel");
const http = moduleRequest("/sys/mes/");
const deviceProtectMaybeLifeApi = {
  /** 导入维护数据 */
  // uploadExcel(params) {
  //   return http.post("uploadExcel", params);
  // },
  /**新增易损件信息 */
  ProdEquipmentMaterialInfoAdd(params) {
    return http.post("ProdEquipmentMaterialInfoAdd", params);
  },
  /**修改易损件信息 */
  ProdEquipmentMaterialInfoEdit(params) {
    return http.post("ProdEquipmentMaterialInfoEdit", params);
  },
  /**查询易损件信息 */
  ProdEquipmentMaterialInfoGet(params) {
    return http.get("ProdEquipmentMaterialInfoGet", params);
  },
  /**删除易损件信息 */
  ProdEquipmentMaterialInfoDelete(params) {
    return http.delete("ProdEquipmentMaterialInfoDelete", params);
  }
};
export { deviceProtectMaybeLifeApi };
