<!-- 
 * @Description: 单文件流转履历详情
 * @Date: 2024-08-11
!-->
<template>
  <el-dialog v-model="visible" title="流转履历1" width="90%" :destroy-on-close="true" class="resume-detail-dialog">
    <div class="resume-detail">
      <div class="filter-box">
        <el-form :inline="true" :model="queryForm" class="form-inline">
          <el-form-item label="产品">
            <el-select v-model="queryForm.product" placeholder="请选择" clearable>
              <el-option label="全部" value="" />
            </el-select>
          </el-form-item>
          <el-form-item label="工单号">
            <el-select v-model="queryForm.workOrderNo" placeholder="请选择" clearable>
              <el-option label="全部" value="" />
            </el-select>
          </el-form-item>
          <el-form-item label="派工单号">
            <el-select v-model="queryForm.dispatchNo" placeholder="请选择" clearable>
              <el-option label="全部" value="" />
            </el-select>
          </el-form-item>
          <el-form-item label="工序">
            <el-select v-model="queryForm.process" placeholder="请选择" clearable>
              <el-option label="全部" value="" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <div class="button-group">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="resetForm">重置</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <div class="table-box">
        <el-table v-loading="loading" :data="tableData" border style="width: 100%" height="calc(100vh - 300px)">
          <el-table-column type="index" label="流水序号" width="80" align="center" />
          <el-table-column prop="productCode" label="产品编码" min-width="120" />
          <el-table-column prop="productName" label="产品名称" min-width="180" />
          <el-table-column prop="workOrderNo" label="工单号" min-width="120" />
          <el-table-column prop="dispatchNo" label="派工单号" min-width="150" />
          <el-table-column prop="processCode" label="工序编码" min-width="100" />
          <el-table-column prop="processName" label="工序名称" min-width="120" />
          <el-table-column prop="deviceCode" label="设备编码" min-width="120" />
          <el-table-column prop="deviceName" label="设备名称" min-width="150" />
          <el-table-column prop="flowResult" label="流转结果" min-width="100">
            <template #default="scope">
              <el-tag :type="scope.row.flowResult === 'OK' ? 'success' : 'danger'">
                {{ scope.row.flowResult }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="shift" label="班次" min-width="100" />
          <el-table-column prop="shiftDate" label="班次日期" min-width="120" />
          <el-table-column prop="carrierCode" label="载具号" min-width="120" />
          <el-table-column prop="operation" label="操作" width="120" fixed="right">
            <template #default="scope">
              <s-button type="primary" size="small" @click="viewResumeDetails(scope.row)">物料消耗</s-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-box">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";

// 定义props
const props = defineProps({
  batteryCode: {
    type: String,
    default: ""
  }
});

// 控制对话框显示
const visible = ref(false);
const loading = ref(false);

// 查询表单
const queryForm = reactive({
  product: "",
  workOrderNo: "",
  dispatchNo: "",
  process: ""
});

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
});

// 表格数据
const tableData = ref<any[]>([]);

// 打开弹窗方法
const open = (code: string) => {
  visible.value = true;
  fetchResumeData(code);
};

// 获取履历数据
const fetchResumeData = () => {
  loading.value = true;
  // 这里应替换为真实API
  setTimeout(() => {
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        productCode: "20011400001",
        productName: "热压后锂电芯-162Ah",
        workOrderNo: "12000000775",
        dispatchNo: "12000000775-20250408-L-Z912",
        processCode: "Z912",
        processName: "切膜",
        deviceCode: "JHMO1C01312-00",
        deviceName: "切膜-联机-L2-3",
        flowResult: "OK",
        shift: "白班",
        shiftDate: "2025-04-10",
        carrierCode: "JHMO14-014"
      },
      {
        id: 2,
        productCode: "20011100001",
        productName: "一次涂控导电极片电芯-16.2Ah",
        workOrderNo: "12000000775",
        dispatchNo: "12000000775-20250410-L-Z121",
        processCode: "Z030",
        processName: "X-ray",
        deviceCode: "JHMO1C03102-00",
        deviceName: "X-ray-L2",
        flowResult: "OK",
        shift: "白班",
        shiftDate: "2025-04-10",
        carrierCode: "-"
      }
    ];
    pagination.total = 10; // 模拟总条数
    loading.value = false;
  }, 500);
};

// 搜索
const handleSearch = () => {
  pagination.currentPage = 1;
  fetchResumeData(props.batteryCode);
};

// 重置表单
const resetForm = () => {
  Object.keys(queryForm).forEach(key => {
    queryForm[key as keyof typeof queryForm] = "";
  });
  handleSearch();
};

// 分页相关方法
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  fetchResumeData(props.batteryCode);
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  fetchResumeData(props.batteryCode);
};

// 查看物料消耗
const viewResumeDetails = (row: any) => {
  console.log("物料消耗", row);
  // ElMessage.success(`查看工序 ${row.processName} 的详细信息`);
};

// 向外暴露方法
defineExpose({
  open
});
</script>

<style lang="scss" scoped>
.resume-detail {
  .filter-box {
    margin-bottom: 16px;
    .button-group {
      display: flex;
      justify-content: flex-end;
    }
  }
  .table-box {
    .el-table {
      margin-bottom: 16px;
    }
  }
  .pagination-box {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }
}
.resume-detail-dialog {
  :deep(.el-dialog__body) {
    padding: 16px;
  }
}
</style>
