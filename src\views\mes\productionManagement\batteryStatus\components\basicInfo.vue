<template>
  <div class="basic-info-container">
    <el-empty v-if="!hasData" description="请输入查询内容进行查询" />
    <div v-else class="info-content">
      <el-descriptions title="电芯基本信息" :column="3" border>
        <el-descriptions-item label="电芯编号">{{ batteryInfo.code }}</el-descriptions-item>
        <el-descriptions-item label="电芯类型">{{ batteryInfo.type }}</el-descriptions-item>
        <el-descriptions-item label="生产日期">{{ batteryInfo.produceDate }}</el-descriptions-item>
        <el-descriptions-item label="当前状态">{{ batteryInfo.status }}</el-descriptions-item>
        <el-descriptions-item label="当前工序">{{ batteryInfo.currentProcess }}</el-descriptions-item>
        <el-descriptions-item label="所属批次">{{ batteryInfo.batchNo }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script setup lang="ts" name="basicInfo">
import { ref, reactive, watch } from "vue";

// 定义组件属性
const props = defineProps({
  queryParams: {
    type: Object,
    required: true
  }
});

// 是否有数据标志
const hasData = ref(false);

// 电芯基本信息
const batteryInfo = reactive({
  code: "",
  type: "",
  produceDate: "",
  status: "",
  currentProcess: "",
  batchNo: ""
});

// 监听查询参数变化
watch(
  () => props.queryParams,
  newVal => {
    if (newVal.queryValue) {
      fetchBatteryInfo(newVal.queryType, newVal.queryValue);
    }
  },
  { deep: true }
);

/**
 * 获取电芯基本信息
 */
function fetchBatteryInfo(queryType: string, queryValue: string) {
  // TODO: 替换为实际API调用
  console.log("查询基本信息:", queryType, queryValue);

  // 模拟数据，实际项目中应替换为API请求
  setTimeout(() => {
    batteryInfo.code = queryType === "battery" ? queryValue : "B" + queryValue.substring(0, 6);
    batteryInfo.type = "正极高速辊压";
    batteryInfo.produceDate = "2025-06-10";
    batteryInfo.status = "正常";
    batteryInfo.currentProcess = "极片分切";
    batteryInfo.batchNo = "B2025061001";
    hasData.value = true;
  }, 500);
}
</script>

<style lang="scss" scoped>
.basic-info-container {
  height: 100%;
  padding: 20px;
  .info-content {
    margin-top: 20px;
  }
}
</style>
