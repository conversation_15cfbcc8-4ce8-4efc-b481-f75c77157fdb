<template>
  <!-- {{ dataSource }} -->
  <router-link to="/mes/status-monitoring/current-status">
    <div class="status-monitor-container" @mouseenter="stopTimer" @mouseleave="startTimer">
      <!-- 控制面板 -->
      <div class="control-panel" @click.stop>
        <div class="p-[4px] m-t[10px] flex justify-between">
          <el-radio-group v-model="chartType" size="small" @change="handleChartChange">
            <el-radio-button label="single">
              <svg-icon name="single-mode" class="mr-1" />
              {{ t("dashboard.leftCenter.currentStatusMode") }}
            </el-radio-button>
            <el-radio-button label="stack">
              <svg-icon name="stack-mode" class="mr-1" />
              {{ t("dashboard.leftCenter.cumulativeTimeMode") }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 图表容器 -->
      <div class="chart-wrapper" :class="{ transitioning: isTransitioning }">
        <v-chart ref="chartRef" :option="currentOptions" class="chart" :key="chartType" autoresize @finished="handleChartRendered" />
      </div>
    </div>
  </router-link>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from "vue";
import VChart from "vue-echarts";
import { use } from "echarts/core";
import { BarChart } from "echarts/charts";
import { GridComponent, TooltipComponent, LegendComponent, GraphicComponent } from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";
import { chartColor } from "@/assets/const/index.ts";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const chartRef = ref();
defineExpose({
  chartRef // 暴露 v-chart 实例
});
// 注册必要的组件
use([CanvasRenderer, BarChart, GridComponent, TooltipComponent, LegendComponent, GraphicComponent]);

// 定义props
const props = defineProps<{
  dataSource: {
    machine: string;
    machinestatuss: string;
    start_time: string;
    end_time: string;
    // 其他字段保留，但组件内只使用需要的字段
  }[];
}>();

// 类型声明
interface MachineStatus {
  machine: string;
  machinestatuss: string;
  duration: number; // 计算后的持续时间（分钟）
}

interface StackSeriesData {
  name: string;
  data: number[];
}
let timer;
const chartType = ref<"single" | "stack">("single");

const toggleChartType = () => {
  chartType.value = chartType.value === "single" ? "stack" : "single";
};

const startTimer = () => {
  timer = setInterval(toggleChartType, 5000);
};

const stopTimer = () => {
  clearInterval(timer);
};

onMounted(() => {
  startTimer();
});

// 组件卸载时清除定时器（避免内存泄漏）
onUnmounted(() => {
  stopTimer();
});
// 状态管理
const isTransitioning = ref<boolean>(false);

// 颜色映射（根据实际状态扩展）
const STATUS_COLORS = {
  调试: "yellow",
  运行: "#4FC08D",
  待机: "#C0504D",
  维修: "yellow",
  复位完成: "#4FC08D",
  生产暂停: "#C0504D",
  急停: "#C0504D",
  请复位: "yellow",
  其他: "yellow"
};

// 处理原始数据为所需格式（计算duration）
const processData = (rawData: typeof props.dataSource): MachineStatus[] => {
  return rawData.map(item => {
    const start = new Date(item.start_time);
    const end = new Date(item.end_time);
    const duration = Math.floor((end.getTime() - start.getTime()) / (60 * 1000)); // 转换为分钟
    return {
      machine: item.machine,
      machinestatuss: item.machinestatuss,
      duration
    };
  });
};

// 单状态模式数据：每个机台取第一条记录（假设每个机台只有一条数据，如需最新/特定状态需调整）
const getSingleData = (): MachineStatus[] => {
  const machineMap = new Map<string, MachineStatus>();
  props.dataSource.forEach(item => {
    const processed = processData([item])[0];
    if (!machineMap.has(processed.machine)) {
      machineMap.set(processed.machine, processed);
    }
  });
  return Array.from(machineMap.values());
};

// 图表切换处理（移除数据生成，依赖父组件数据更新）
const handleChartChange = () => {
  isTransitioning.value = true;
  setTimeout(() => {
    isTransitioning.value = false;
  }, 500);
};

// 图表渲染完成回调
const handleChartRendered = () => {
  // 可以在这里添加图表渲染后的操作
};

/* 单状态图表配置 */
const generateSingleOptions = (data: MachineStatus[]): EChartsOption => {
  return {
    title: {
      subtext: t("statusMonitoring.currentStatus.stack.subtext"),
      subtextStyle: {
        color: chartColor.fontColor,
        fontWeight: "bold"
      },
      left: "center",
      top: "-14"
    },
    backgroundColor: "transparent",
    tooltip: {
      trigger: "item",
      formatter: ({ name }: { name: string }) => {
        const item = data.find(d => d.machine === name);
        if (!item) return "";

        return `
        <div style="background:rgb(20 30 50 / 90%);" >
          <div  style="font-weight:bold;margin-bottom:8px;color:${chartColor.fontColor}">${name}</div>
          <div style="display:flex;align-items:center">
            <span style="display:inline-block;width:12px;height:12px;background:${
              STATUS_COLORS[item.machinestatuss] ?? STATUS_COLORS["其他"]
            };border-radius:2px;margin-right:6px"></span>
            <span style="color:${chartColor.fontColor}">状态: ${item.machinestatuss}</span>
          </div>
          <div style="margin-top:4px;color:${chartColor.fontColor}">持续时间: ${item.duration}分钟</div></div>
        `;
      },
      className: "enhanced-tooltip"
    },
    grid: {
      left: "3%",
      bottom: "1%",
      top: "27%",

      containLabel: true
    },
    xAxis: {
      type: "category",
      data: data.map(item => item.machine),
      axisLabel: {
        rotate: 30,
        color: chartColor.fontColor,
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: "rgba(255,255,255,0.2)"
        }
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      show: false
    },
    series: [
      {
        type: "bar",
        barWidth: "40%",
        data: data.map(item => ({
          value: 100,
          name: item.machine,
          itemStyle: {
            color: STATUS_COLORS[item.machinestatuss] ?? STATUS_COLORS["其他"],
            borderRadius: [4, 4, 0, 0],
            shadowColor: STATUS_COLORS[item.machinestatuss] ?? STATUS_COLORS["其他"],
            shadowBlur: 8,
            shadowOffsetY: 3
          }
        })),
        label: {
          show: true,
          position: "top",
          color: chartColor.fontColor,
          formatter: ({ dataIndex }: any) => {
            const item = data[dataIndex];
            return `${item.machinestatuss}\n(${item.duration}分钟)`;
          }
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 12,
            shadowColor: STATUS_COLORS[data[0]?.machinestatuss]
          }
        }
      }
    ]
  };
};

/* 堆叠状态图表配置 */
const generateStackOptions = (data: MachineStatus[]): EChartsOption => {
  const machines = [...new Set(data.map(d => d.machine))];
  const statusTypes = [...new Set(data.map(d => d.machinestatuss))];

  const seriesData: StackSeriesData[] = statusTypes.map(status => ({
    name: status,
    data: machines.map(machine => {
      return data.filter(d => d.machine === machine && d.machinestatuss === status).reduce((sum, d) => sum + d.duration, 0);
    })
  }));

  const totals = machines.map((_, idx) => seriesData.reduce((sum, s) => sum + s.data[idx], 0));

  return {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
        shadowStyle: {
          color: "rgba(150, 150, 150, 0.2)"
        }
      },
      formatter: (params: any) => {
        const total = totals[params[0].dataIndex];
        let res = `<div style="font-weight:bold;margin-bottom:8px;color:${chartColor.fontColor}">${params[0].axisValue}</div>`;
        params.forEach((item: any) => {
          res += `
            <div style="display:flex;align-items:center;margin-bottom:2px">
              <span style="display:inline-block;width:12px;height:12px;background:${item.color};border-radius:2px;margin-right:6px"></span>
              <span style="flex:1;color:${chartColor.fontColor}">${item.seriesName}:</span>
              <span style="font-weight:bold;color:${chartColor.fontColor}">${item.value}分钟</span>
            </div>
          `;
        });
        res += `
          <div style="margin-top:6px;padding-top:6px;border-top:1px dashed #666">
            <span style="font-weight:bold;color:${chartColor.fontColor}">总时长: ${total}分钟</span>
          </div>
        `;
        return res;
      },
      className: "enhanced-tooltip"
    },
    grid: {
      left: "5%",
      right: "3%",
      bottom: "12%",
      top: "16%",
      containLabel: true
    },
    legend: {
      data: statusTypes,
      bottom: 10,
      textStyle: {
        color: chartColor.fontColor
      },
      itemWidth: 12,
      itemHeight: 12
    },
    xAxis: {
      type: "category",
      data: machines,
      axisLabel: {
        color: chartColor.fontColor,
        fontSize: 12,
        rotate: 30
      },
      axisLine: {
        lineStyle: {
          color: "rgba(255,255,255,0.2)"
        }
      },
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: "value",
      name: t("statusMonitoring.currentStatus.stack.yAxisName"),
      nameTextStyle: {
        color: chartColor.fontColor,
        padding: [0, 0, 0, 40]
      },
      axisLabel: {
        color: chartColor.fontColor
      },
      axisLine: {
        lineStyle: {
          color: "rgba(255,255,255,0.2)"
        }
      },
      splitLine: {
        lineStyle: {
          color: "rgba(255,255,255,0.1)"
        }
      }
    },
    series: [
      ...statusTypes.map((status, idx) => ({
        type: "bar",
        name: status,
        stack: "total",
        barWidth: "40%",
        data: seriesData[idx].data,
        itemStyle: {
          color: STATUS_COLORS[status],
          borderRadius: [4, 4, 0, 0],
          shadowColor: "rgba(0,0,0,0.3)",
          shadowBlur: 5
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: STATUS_COLORS[status]
          }
        },
        label: {
          show: true,
          position: "inside",
          formatter: "{c}",
          color: chartColor.fontColor
        }
      })),
      {
        type: "bar",
        name: "总时长",
        barGap: "-100%",
        label: {
          show: true,
          position: "top",
          formatter: "{c}",
          color: chartColor.fontColor
        },
        itemStyle: {
          color: "transparent",
          borderColor: "rgba(255,255,255,0.3)",
          borderWidth: 1,
          borderType: "dashed"
        },
        data: totals
      }
    ]
  };
};

// 动态计算当前配置
const currentOptions = computed<EChartsOption>(() => {
  const processedData = processData(props.dataSource);
  return chartType.value === "single" ? generateSingleOptions(getSingleData()) : generateStackOptions(processedData);
});
</script>

<style scoped lang="scss">
.status-monitor-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: rgb(89 98 123 / 23%);
  border-radius: 8px;
  box-shadow: 0 0 20px rgb(0 0 0 / 30%);
  .control-panel {
    margin-bottom: 16px;
    :deep(.el-radio-button__inner) {
      color: #a1a1a1;
      background: rgb(255 255 255 / 10%);
      border: 1px solid rgb(255 255 255 / 20%);
      transition: all 0.3s;
      &:hover {
        color: #ffffff;
        background: rgb(255 255 255 / 20%);
      }
    }
    :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
      color: #ffffff;
      background: linear-gradient(90deg, #3b7bff, #00d1ff);
      border-color: transparent;
      box-shadow: 0 0 10px rgb(59 123 255 / 50%);
    }
  }
  .chart-wrapper {
    position: relative;
    flex: 1;
    transition: opacity 0.3s ease;
    &.transitioning {
      opacity: 0.5;
    }
    .chart {
      width: 100%;
      height: 100%;
    }
  }
}
:global(.enhanced-tooltip) {
  background: rgb(20 30 50 / 90%) !important;
  backdrop-filter: blur(5px) !important;
  border: 1px solid rgb(100 150 255 / 30%) !important;
  border-radius: 4px !important;
  box-shadow: 0 0 20px rgb(0 0 0 / 50%) !important;
}
</style>
