<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'search', 'download', 'table']"
    :search-fields="searchFields"
    :chart-height="chartHeight"
    @data-loaded="handleDataLoaded"
  >
  </ProChart>
</template>

<script setup lang="tsx">
import { ColumnProps } from "@/components/ProTable/interface";
// 图表实例引用

// 机台选项配置
const moduleOptions = [
  { label: "全部机台", value: "all" },
  { label: "机台一", value: "machine1" },
  { label: "机台二", value: "machine2" }
];

// 颜色配置常量
const COLORS = {
  plan: "#5470C6",
  actual: "#91CC75",
  gapPositive: "#73D13D",
  gapNegative: "#FF4D4F",
  rate: "#FAC858"
};

// 搜索字段配置
const searchFields = ref<ColumnProps[]>([
  {
    prop: "deck",
    label: "机台选择",
    enum: moduleOptions,
    search: {
      el: "select",
      props: {
        clearable: false,
        placeholder: "请选择机台",
        filterable: true
      },
      defaultValue: "all" // 明确指定有效默认值
    },
    isShow: false
  },
  {
    prop: "time",
    label: "时间",
    search: {
      el: "date-picker",
      props: {
        type: "daterange",
        "range-separator": "To",
        "start-placeholder": "Start date",
        "end-placeholder": "End date"
      }
      // defaultValue: "all" // 明确指定有效默认值
    },
    isShow: false
  }
]);

// 图表配置
const chartOptions = ref({
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: "切换为折线图",
          bar: "切换为柱状图",
          stack: "切换为堆叠图"
        }
      }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "cross" },
    backgroundColor: "rgba(0,0,0,0.8)",
    textStyle: { color: "#fff" }
  },
  legend: {
    data: ["计划数", "达成数", "GAP数", "达成率"],
    textStyle: { color: "#666" },
    top: "bottom"
  },
  grid: {
    left: "3%",
    right: "3%",
    bottom: "15%",
    containLabel: true
  },
  xAxis: {
    type: "category",
    data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月"],
    axisLabel: { color: "#666" }
  },
  yAxis: [
    {
      type: "value",
      name: "生产数量",
      axisLine: { show: false },
      splitLine: { lineStyle: { type: "dashed" } }
    },
    {
      type: "value",
      name: "达成率(%)",
      axisLine: { show: false },
      splitLine: { show: false },
      min: 0,
      max: 100
    }
  ],
  series: [
    {
      name: "计划数",
      type: "bar",
      barWidth: "20%",
      itemStyle: { color: COLORS.plan },
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
      name: "达成数",
      type: "bar",
      barWidth: "20%",
      itemStyle: { color: COLORS.actual },
      data: [220, 182, 191, 234, 290, 330, 310]
    },
    {
      name: "GAP数",
      type: "bar",
      barWidth: "20%",
      itemStyle: {
        color: (params: any) => (params.value > 0 ? COLORS.gapPositive : COLORS.gapNegative)
      },
      data: [100, 50, -90, 100, -200, 100, 100]
    },
    {
      name: "达成率",
      type: "line",
      yAxisIndex: 1,
      symbol: "circle",
      symbolSize: 8,
      itemStyle: { color: COLORS.rate },
      lineStyle: { width: 2 },
      data: [65, 82, 93, 78, 85, 92, 88]
    }
  ]
});

// 图表高度响应式
const chartHeight = ref(`${window.innerHeight * 0.6}px`);

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const fetchData = async (params: any) => {
  try {
    // 模拟真实API请求
    return await new Promise(resolve => {
      setTimeout(() => {
        resolve({
          data: {
            // 根据选择机台返回不同数据
            categories: ["1月", "2月", "3月", "4月", "5月", "6月", "7月"],
            seriesData: [
              [120, 132, 101, 134, 90, 230, 210],
              [220, 182, 191, 234, 290, 330, 310],
              [100, 50, -90, 100, -200, 100, 100],
              [65, 82, 93, 78, 85, 92, 88]
            ]
          }
        });
      }, 500);
    });
  } catch (error) {
    console.error("数据请求失败:", error);
    throw error;
  }
};

// 数据加载回调
const handleDataLoaded = (data: any) => {
  console.log("最新图表数据:", data);
  // 动态更新图表配置
  chartOptions.value.xAxis.data = data.categories;
  chartOptions.value.series.forEach((series, index) => {
    series.data = data.seriesData[index];
  });
};
</script>

<style scoped lang="scss"></style>
