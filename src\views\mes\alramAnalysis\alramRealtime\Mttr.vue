<template>
  <div class="table-box table-main">
    <v-chart ref="chartRef" :autoresize="true" :option="lineChartOptions" class="chart" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, defineProps } from "vue";
import { useI18n } from "vue-i18n";
const { t, locale } = useI18n();
const chartRef = ref();
import { transformChart, exportMultipleTablesToExcel } from "@/utils";
import moment from "moment";
import { PropType } from "vue";

interface ChartDataItem {
  machine_name: string;
  total_repair_time: number;
  total_working_time: number;
  fault_count: number;
}

// 定义 props
const props = defineProps({
  data: {
    type: Array as PropType<ChartDataItem[]>,
    default: () => []
  }
});

const lineChartOptions = ref({});

// 生成随机颜色
const getRandomColor = () => {
  const letters = "0123456789ABCDEF";
  let color = "#";
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

const exportExcel = () => {
  const arr1 = transformChart(chartRef.value, t("alarmRealtime.machine"));

  // 获取当前时间并格式化
  const currentTime = moment().format("YYYY:MM:DD_HH:mm:ss");

  // 将当前时间拼接到文件名中
  const fileName = `${t("alarmRealtime.mttr.exportFileName")}_${currentTime}.xlsx`;

  exportMultipleTablesToExcel([arr1], [t("alarmRealtime.mttr.title")], fileName);
};

const updateChartOptions = () => {
  // 计算 MTTR 和 MTBF 并按 MTTR 从高到低排序
  const sortedData = [...props.data]
    .map(item => ({
      ...item,
      mttr: item.total_repair_time / item.fault_count,
      mtbf: item.total_working_time / item.fault_count
    }))
    .sort((a, b) => b.mttr - a.mttr);

  const machineNames = sortedData.map(item => item.machine_name);
  const mttrValues = sortedData.map(item => item.mttr);
  const mtbfValues = sortedData.map(item => item.mtbf);

  const itemStylesMTTR = mttrValues.map(() => ({
    itemStyle: {
      color: getRandomColor()
    }
  }));

  const itemStylesMTBF = mtbfValues.map(() => ({
    itemStyle: {
      color: getRandomColor()
    }
  }));

  lineChartOptions.value = {
    toolbox: {
      orient: "vertical",
      itemGap: 10,
      right: -9,
      show: true,
      feature: {
        magicType: {
          type: ["bar", "line"],
          title: {
            bar: t("alarmRealtime.mttr.switchToBar"),
            line: t("alarmRealtime.mttr.switchToLine")
          }
        },
        saveAsImage: { show: true },
        myExcelExport: {
          show: true,
          title: t("alarmRealtime.mttr.exportExcel"),
          icon: "path://M665.38 65.024c11.48 0 22.53 4.46 30.72 12.58l0.44 0.37 152.65 153.6c8.05 8.12 12.65 19.02 12.8 30.43v250.51a13.75 13.75 0 0 1-13.68 13.75h-28.6a13.75 13.75 0 0 1-13.75-13.75v-245.03L660.48 121.05H216.94v199.02h269.24c7.61 0 13.75 6.14 13.75 13.75v420.57a13.68 13.68 0 0 1-13.75 13.68H217.01v136.05h589.02v-24.72c0-7.61 6.14-13.75 13.68-13.75h28.53c7.61 0 13.75 6.14 13.75 13.75v44.62a35.99 35.99 0 0 1-35.33 36.06h-629.76a35.99 35.99 0 0 1-35.84-35.4V768h-83.38a13.68 13.68 0 0 1-13.68-13.75v-420.57c0-7.53 6.14-13.68 13.75-13.68H160.91V101.01c0-19.68 15.73-35.69 35.33-35.99h469.07zM361.33 437.98a54.86 54.86 0 0 0-42.13 19.53l-37.3 44.47-37.3-44.4a54.86 54.86 0 0 0-41.98-19.6h-30.13a6.88 6.88 0 0 0-5.27 11.26l79.51 94.72-79.51 94.72a6.88 6.88 0 0 0 5.27 11.26h30.28a54.86 54.86 0 0 0 41.98-19.6l37.16-44.32 37.3 44.32a54.86 54.86 0 0 0 41.98 19.6h30.21c5.85 0 9-6.8 5.27-11.26L317.22 543.96l79.51-94.72a6.88 6.88 0 0 0-5.19-11.26zm214.6-104.3c0-7.53 6.14-13.68 13.75-13.68h164.57c7.53 0 13.68 6.14 13.68 13.75v28.53a13.68 13.68 0 0 1-13.75 13.75h-164.57a13.68 13.68 0 0 1-13.68-13.75v-28.53zm13.75 102.33a13.68 13.68 0 0 0-13.75 13.68v28.6c0 7.61 6.14 13.75 13.75 13.75h164.57a13.68 13.68 0 0 0 13.68-13.75v-28.53a13.68 13.68 0 0 0-13.75-13.75h-164.57zm192 348.23a21.21 21.21 0 0 0-8.19 16.82v48.64c0 4.39 5.12 6.88 8.56 4.17l168.67-130.78a24.5 24.5 0 0 0 0-38.62L782.19 553.69a5.27 5.27 0 0 0-8.56 4.24v48.71c0 6.58 3 12.73 8.19 16.82l68.53 53.03H617.25a10.61 10.61 0 0 0-10.53 10.61v33.94c0 5.85 4.68 10.61 10.53 10.61h232.45l-67.95 52.66z",
          onclick: exportExcel
        }
      }
    },
    grid: {
      left: "3%",
      right: "6%",
      bottom: "3%",
      containLabel: true
    },
    legend: {
      data: [t("alarmRealtime.mttr.mttr"), t("alarmRealtime.mttr.mtbf")],
      top: "top",
      left: "center"
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        const dataIndex = params[0].dataIndex;
        const item = sortedData[dataIndex];
        return `
              <div>${t("alarmRealtime.mttr.machineName")}: ${item.machine_name}</div>
              <div>${t("alarmRealtime.mttr.mttr")}: ${item.mttr.toFixed(2)} ${t("alarmRealtime.mttr.hours")}</div>
              <div>${t("alarmRealtime.mttr.mtbf")}: ${item.mtbf.toFixed(2)} ${t("alarmRealtime.mttr.hours")}</div>
            `;
      }
    },
    xAxis: {
      type: "category",
      data: machineNames,
      axisLabel: {
        interval: 0
      }
    },
    yAxis: {
      name: t("alarmRealtime.mttr.timeHours"),
      type: "value"
    },
    series: [
      {
        name: t("alarmRealtime.mttr.mttr"),
        data: mttrValues.map((mttr, index) => ({
          value: mttr.toFixed(2),
          ...itemStylesMTTR[index]
        })),
        type: "bar",
        label: {
          show: true,
          position: "inside"
        }
      },
      {
        name: t("alarmRealtime.mttr.mtbf"),
        data: mtbfValues.map((mtbf, index) => ({
          value: mtbf.toFixed(2),
          ...itemStylesMTBF[index]
        })),
        type: "bar",
        label: {
          show: true,
          position: "inside"
        }
      }
    ]
  };
};

// 初始化图表配置
updateChartOptions();

// 监听 props.data 的变化，若有变化则更新图表配置
watch(
  () => props.data,
  () => {
    updateChartOptions();
  }
);

// 监听语言变化，更新图表配置
watch(
  () => locale.value,
  () => {
    updateChartOptions();
  }
);

defineExpose({
  chartRef
});
</script>

<style scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
