<template>
  <div class="cyber-oee-dashboard h-full relative overflow-hidden">
    <div class="holographic-bg"></div>

    <div class="flex items-center justify-around w-full flex-1 relative z-10">
      <div class="oee-metrics items-center flex-1 grid grid-cols-3 gap-8 px-8">
        <!-- OEE指标 -->
        <div class="metric cyber-card" v-for="(metric, index) in metrics" :key="index" @mouseenter="hoverIndex = index" @mouseleave="hoverIndex = -1">
          <div class="cyber-glows"></div>
          <span class="metric-title">{{ metric.title }}</span>
          <v-chart
            :autoresize="true"
            :option="getChartOption(index)"
            class="chart"
            :style="{ filter: hoverIndex === index ? 'drop-shadow(0 0 15px ' + metric.glowColor + ')' : '' }"
          />
          <!-- <div class="data-glow">{{ metric.value }}</div> -->
          <div class="cyber-border"></div>
        </div>
      </div>
    </div>

    <!-- 浮动粒子 -->
    <div class="cyber-particles"></div>
  </div>
</template>

<script setup>
import { ref, defineProps, computed } from "vue";
import { useI18n } from "vue-i18n";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      Control: "综合效率",
      availability: 0,
      performanceefficiency: 0,
      oee: 0,
      start_time: "",
      end_time: ""
    })
  }
});

const hoverIndex = ref(-1);

const i18n = useI18n();

// 改为计算属性，实时响应props.data变化
const metrics = computed(() => [
  {
    title: i18n.t("metrics.overallEfficiency"),
    value: `${props.data.oee}%`,
    color: ["#4FC08D", "#4FC08D"],
    glowColor: "#4FC08D",
    data: [props.data.oee / 100, props.data.oee / 100]
  },
  {
    title: i18n.t("metrics.timeEfficiency"),
    value: `${props.data.availability}%`,
    color: ["black", "yellow"],
    glowColor: "black",
    data: [props.data.availability / 100, props.data.availability / 100]
  },
  {
    title: i18n.t("metrics.performanceEfficiency"),
    value: `${props.data.performanceefficiency}%`,
    color: ["#C0504D", "#C0504D"],
    glowColor: "#C0504D",
    data: [props.data.performanceefficiency / 100, props.data.performanceefficiency / 100]
  }
]);

const getChartOption = index => {
  const metric = metrics.value[index]; // 从计算属性中获取最新数据
  return {
    series: [
      {
        type: "liquidFill",
        data: metric.data,
        radius: "90%",
        amplitude: 6,
        waveAnimation: true,
        animationDuration: 2000,
        animationDurationUpdate: 2000,
        phase: Math.PI * Math.random(),
        color: metric.color,
        outline: {
          show: false
        },
        backgroundStyle: {
          color: "rgba(10, 30, 50, 0.3)",
          borderColor: metric.color[0],
          borderWidth: 1,
          shadowColor: metric.color[0],
          shadowBlur: 20
        },
        shape:
          'path://"M61.3,2c6.2,0,12.1,1.1,17.5,3.4C84.3,7.7,89,10.8,93,14.6c4.1,4,7.3,8.6,9.7,13.8c2.4,5.2,3.5,10.9,3.5,16.9c0,8.1-2.4,16.9-7.1,26.4c-4.7,9.4-9.9,18.2-15.5,26.2c-5.6,8-13.1,17.4-22.4,28.1c-9.3-10.7-16.8-20-22.4-28.1c-5.6-8-10.8-16.8-15.5-26.2c-4.7-9.4-7.1-18.2-7.1-26.4c0-6,1.2-11.6,3.5-16.9c2.4-5.2,5.6-9.8,9.7-13.8c4-3.9,8.8-7,14.2-9.2C49.2,3.1,55,2,61.3,2L61.3,2z"',
        label: {
          position: ["50%", "50%"],
          formatter: metric.value,
          fontSize: 20,
          color: "#fff",
          textBorderColor: metric.color[0],
          textBorderWidth: 2,
          textShadowColor: metric.color[0],
          textShadowBlur: 10
        },
        emphasis: {
          itemStyle: {
            opacity: 0.9
          }
        }
      }
    ]
  };
};
</script>

<style scoped>
.cyber-oee-dashboard {
  background: rgb(89 98 123 / 23%);
}

/* 赛博卡片样式 */
.cyber-card {
  position: relative;
  overflow: hidden;
  text-align: center;

  /* background: rgb(16 36 69 / 40%); */

  /* backdrop-filter: blur(5px); */
  border-radius: 8px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}
.cyber-card:hover {
  box-shadow: 0 10px 20px rgb(0 0 0 / 30%);
  transform: translateY(-5px);
}

/* 霓虹标题 */
.metric-title {
  display: block;
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 5px rgb(79 172 254 / 70%);
  white-space: nowrap;
}

/* 数据发光效果 */
.data-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  font-size: 24px;
  font-weight: bold;
  color: transparent;
  text-shadow: 0 0 8px #ffffff;
  pointer-events: none;
  opacity: 0.7;
  transform: translate(-50%, -50%);
}

/* 赛博边框动画 */

/* .cyber-border {
  position: absolute;
  inset: 0;
  border: 1px solid transparent;
  border-image: linear-gradient(135deg, rgb(79 172 254 / 80%) 0%, rgb(0 242 254 / 0%) 50%, rgb(79 172 254 / 80%) 100%);
  border-image-slice: 1;
  animation: borderFlow 3s linear infinite;
} */

/* 卡片光晕 */
.cyber-glows {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgb(79 172 254 / 10%) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s;
}
.cyber-card:hover .cyber-glows {
  opacity: 1;
}

/* 全息投影背景 */
.holographic-bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(45deg, rgb(16 36 69 / 80%) 0%, transparent 100%),
    repeating-linear-gradient(0deg, rgb(79 172 254 / 10%), rgb(79 172 254 / 10%) 1px, transparent 3px, transparent 5px);
  opacity: 0.3;
}

/* 浮动粒子 */
.cyber-particles {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* .cyber-particles::before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-image:
    radial-gradient(circle at 20% 30%, rgb(79 172 254 / 30%) 0%, transparent 2%),
    radial-gradient(circle at 80% 70%, rgb(0 242 254 / 30%) 0%, transparent 2%),
    radial-gradient(circle at 40% 60%, rgb(255 126 179 / 30%) 0%, transparent 2%);
  animation: particleMove 20s linear infinite;
} */
.chart {
  width: 100px;
  height: 100px;
  margin: 0 auto;
  transition: all 0.3s ease;
}

/* 动画定义 */
@keyframes borderFlow {
  0% {
    border-image-source: linear-gradient(135deg, rgb(79 172 254 / 80%) 0%, rgb(0 242 254 / 0%) 50%, rgb(79 172 254 / 80%) 100%);
  }
  50% {
    border-image-source: linear-gradient(135deg, rgb(0 242 254 / 80%) 0%, rgb(79 172 254 / 0%) 50%, rgb(0 242 254 / 80%) 100%);
  }
  100% {
    border-image-source: linear-gradient(135deg, rgb(79 172 254 / 80%) 0%, rgb(0 242 254 / 0%) 50%, rgb(79 172 254 / 80%) 100%);
  }
}

@keyframes particleMove {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-5%, 5%);
  }
  50% {
    transform: translate(5%, -5%);
  }
  75% {
    transform: translate(-3%, 3%);
  }
  100% {
    transform: translate(0, 0);
  }
}
</style>
