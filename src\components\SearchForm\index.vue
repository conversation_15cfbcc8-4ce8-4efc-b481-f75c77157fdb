<template>
  <div class="card table-search">
    <el-form ref="formRef" :model="searchParam">
      <Grid ref="gridRef" :collapsed="collapsed" :gap="[20, 0]" :cols="searchCol">
        <GridItem v-for="(item, index) in columns" :key="item.prop" v-bind="getResponsive(item)" :index="index">
          <el-form-item>
            <template #label>
              <el-space :size="4">
                <span>{{ `${item.search?.label || item.label ? (item.search?.label ?? item.label) : ""}` }}</span>
                <el-tooltip v-if="item.search?.tooltip" effect="dark" :content="item.search?.tooltip" placement="top">
                  <i :class="'iconfont icon-yiwen'"></i>
                </el-tooltip>
              </el-space>
              <span v-if="!item.search?.isCustom">&nbsp;:</span>
            </template>
            <SearchFormItem :column="item" :search-param="searchParam" />
          </el-form-item>
        </GridItem>
        <GridItem suffix>
          <div class="operation">
            <slot name="any"> </slot>
            <el-button v-if="columns.length" type="primary" :icon="Search" @click="search"> {{ t("common.search") }} </el-button>
            <el-button v-if="columns.length" :icon="Delete" @click="reset"> {{ t("common.reset") }} </el-button>
            <el-button v-if="!columns.length" type="primary" :icon="Refresh" @click="search"> {{ t("common.refresh") }} </el-button>
            <el-button v-if="showCollapse" type="primary" link class="search-isOpen" @click="collapsed = !collapsed">
              {{ collapsed ? t("common.expand") : t("common.collapse") }}
              <el-icon class="el-icon--right">
                <component :is="collapsed ? ArrowDown : ArrowUp"></component>
              </el-icon>
            </el-button>
          </div>
        </GridItem>
      </Grid>
    </el-form>
  </div>
</template>
<script setup lang="ts" name="SearchForm">
import { computed, ref } from "vue";
import { ColumnProps } from "@/components/ProTable/interface";
import { BreakPoint } from "@/components/Grid/interface";
import { Delete, Search, ArrowDown, ArrowUp, Refresh } from "@element-plus/icons-vue";
import SearchFormItem from "./components/SearchFormItem.vue";
import Grid from "@/components/Grid/index.vue";
import GridItem from "@/components/Grid/components/GridItem.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

interface ProTableProps {
  columns?: ColumnProps[]; // 搜索配置列
  searchParam?: { [key: string]: any }; // 搜索参数
  searchCol: number | Record<BreakPoint, number>;
  search: (params: any) => void; // 搜索方法
  reset: (params: any) => void; // 重置方法
}

// 默认值
const props = withDefaults(defineProps<ProTableProps>(), {
  columns: () => [],
  searchParam: () => ({})
});

// 获取响应式设置
const getResponsive = (item: ColumnProps) => {
  return {
    span: item.search?.span,
    offset: item.search?.offset ?? 0,
    xs: item.search?.xs,
    sm: item.search?.sm,
    md: item.search?.md,
    lg: item.search?.lg,
    xl: item.search?.xl
  };
};

// 是否默认折叠搜索项
const collapsed = ref(true);

// 获取响应式断点
const gridRef = ref();
const breakPoint = computed<BreakPoint>(() => gridRef.value?.breakPoint);

// 判断是否显示 展开/合并 按钮
const showCollapse = computed(() => {
  let show = false;
  props.columns.reduce((prev, current) => {
    prev +=
      (current.search![breakPoint.value]?.span ?? current.search?.span ?? 1) +
      (current.search![breakPoint.value]?.offset ?? current.search?.offset ?? 0);
    if (typeof props.searchCol !== "number") {
      if (prev >= props.searchCol[breakPoint.value]) show = true;
    } else {
      if (prev >= props.searchCol) show = true;
    }
    return prev;
  }, 0);
  return show;
});
// 定义局部变量 searchParam
const searchParam = computed(() => props.searchParam);
const search = () => {
  // 这里可以在搜索时检查机台选择是否变化
  // 如果变化，调用父组件的 handleMachineChange 方法
  const instance = getCurrentInstance();
  if (instance) {
    const parent = instance.parent as any;
    if (parent && typeof parent.handleMachineChange === "function") {
      // 假设这里能获取到正确的机台索引，根据实际情况调整
      const machineIndex = 0; // 这里需要根据实际情况获取机台索引
      parent.handleMachineChange(machineIndex);
      // 找到 MachineSwitcher 组件实例并更新索引
      const machineSwitcher = instance.root?.$refs.machineSwitcher;
      if (machineSwitcher) {
        machineSwitcher.setCurrentIndex(machineIndex);
      }
    }
  }
  props.search(searchParam.value);
};
</script>
