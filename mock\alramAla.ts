import Mock from "mockjs";
// 统一响应格式（只读模式）
function mockResponse(data) {
  return {
    code: 200,
    msg: "请求成功",
    data: data,
    extras: "UnifyContextTake()",
    time: new Date().toISOString()
  };
}
// 辅助函数，将数字转换为中文数字
Mock.Random.extend({
  toChineseNumber: function (num) {
    const chnNumChar = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
    return chnNumChar[num];
  }
});
export default [
  // 故障汇总表
  {
    url: "/api/sys/mes/alarm_fault_summary",
    method: "get",
    response: () => {
      const data = Mock.mock({
        "list|1-10": [
          {
            "id|+1": 1,
            fault_time: '@date("yyyy-MM-dd")',
            "fault_count|1-100": 1,
            "failure_rate|0-5.0": 1,
            "mttr_minutes|1-60": 1,
            "mtbf_minutes|60-1000": 1
          }
        ]
      }).list;
      return mockResponse(data);
    }
  },

  // 历史故障记录表
  // {
  //   url: "/api/sys/mes/alarm_historical_fault_record",
  //   method: "get",
  //   response: () => {
  //     const listData = Mock.mock({
  //       "list|1-10": [
  //         {
  //           "id|+1": 1,
  //           factory: "@word(8)",
  //           workshop: "@word(4)",
  //           production_line: "@word(6)",
  //           machine: "@word(8)",
  //           station: "@word(8)",
  //           fault_type: '@pick(["Mechanical", "Electrical", "Software"])',
  //           fault_name: "@word(10)",
  //           alarm_info: "@sentence(15)",
  //           start_time: '@datetime("yyyy-MM-ddTHH:mm:ss")',
  //           "duration_minutes|1-60": 1
  //         }
  //       ]
  //     }).list;

  //     const paginationData = {
  //       hasNextPages: true,
  //       hasPrevPages: false,
  //       list: listData,
  //       pageNum: 1,
  //       pageSize: 10,
  //       pages: Mock.Random.natural(1, 10), // 模拟总页数
  //       total: Mock.Random.natural(10, 100) // 模拟总记录数
  //     };

  //     return mockResponse(paginationData);
  //   }
  // },

  // 整线汇总表
  {
    url: "/api/sys/mes/alarm_whole_line_summary",
    method: "get",
    response: () => {
      const data = Mock.mock({
        "list|1-10": [
          {
            "id|+1": 1,
            "top10_alarm_count|1-100": 1,
            "top10_fault_time|1-100": 1,
            factory: "@word(8)",
            workshop: "@word(4)",
            production_line: "@word(6)",
            machine: "@word(8)",
            station: "@word(8)",
            fault_type: '@pick(["Mechanical", "Electrical", "Software"])',
            fault_name: "@word(10)",
            alarm_info: "@sentence(2)",
            start_time: '@datetime("yyyy-MM-dd HH:mm:ss")',
            "duration_minutes|1-60": 1
          }
        ]
      }).list;
      return mockResponse(data);
    }
  },
  {
    url: "/apl/sys/mes/alarm_detail",
    method: "get",
    response: () => {
      const data = Mock.mock({
        "list|1-20": [
          {
            id: "@increment(10000)", // 自增ID（从10000开始）
            factory: "@word(8)", // 工厂名称（8字符）
            workshop: "@word(6)", // 车间（6字符）
            production_line: `产线@integer(1, 10)`, // 产线（4字符）
            machine: `机台@integer(1, 20)`, // 机台名称（中文2-4字，如"机台4"）
            station: "@cword(2, 4)", // 工位（中文2-4字，如"工位3"）
            fault_type: '@pick(["机械故障", "电气故障", "软件故障", "网络故障"])', // 故障类型（中文枚举）
            fault_name: "@cword(4, 8)", // 故障名称（中文4-8字，如"电机过热故障"）
            start_time: '@datetime("yyyy-MM-dd HH:mm:ss")', // 开始时间（标准格式）
            duration_minutes: "@natural(1, 1440)",
            fault_count: "@natural(1, 50)", // 故障次数（1-50次，合理范围）
            type: "@integer(1, 10)", // 保留原始type字段（1-10随机整数）
            // 解构提取计算好的结束时间
            // 移除临时计算字段
            "@remove": ["duration_minutes"]
          }
        ]
      }).list;
      return mockResponse(data);
    }
  }
];
