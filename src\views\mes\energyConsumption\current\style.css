.data1,
.data2,
.data3,
.data4 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.data1 .qiu,
.data2 .qiu,
.data3 .qiu,
.data4 .qiu {
  position: relative;
  display: table;
  min-width: 6rem;
  min-height: 6rem;
  margin: auto;
}
.data1 .qiu p,
.data2 .qiu p,
.data3 .qiu p,
.data4 .qiu p {
  display: table-cell;
  color: var(--el-text-color-regular);
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
}
.data1 .qiu::before,
.data2 .qiu::before,
.data3 .qiu::before,
.data4 .qiu::before {
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  width: 100%;
  height: 100%;
  content: "";
  border: 3px solid rgb(255 255 255 / 50%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: scale 2s linear infinite;
}
.data1 span,
.data2 span,
.data3 span,
.data4 span {
  font-size: 0.8rem;
  color: var(--el-text-color-regular);
}

/* .data1 {
  top: 10%;
  left: 0%;
} */
.data1 .qiu {
  background: url("/src/assets/images/cicle04.png") no-repeat center;
  background-size: 100%;
}

/* .data2 {
  top: 8%;
  right: 5%;
} */
.data2 .qiu {
  background: url("/src/assets/images/cicle02.png") no-repeat center;
  background-size: 100%;
}
.data3 {
  bottom: 3%;
  left: 0;
}
.data3 .qiu {
  background: url("/src/assets/images/cicle03.png") no-repeat center;
  background-size: 100%;
}
.data4 {
  right: 6%;
  bottom: 3%;
}
.data4 .qiu {
  background: url("/src/assets/images/cicle01.png") no-repeat center;
  background-size: 100%;
}

@keyframes scale {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.9);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.5);
  }
}
