<template>
  <div class="station-info-container">
    <el-empty v-if="!hasData" description="请输入查询内容进行查询" />
    <div v-else class="info-content">
      <el-table :data="stationList" border style="width: 100%">
        <el-table-column prop="stationName" label="工序名称" width="180" />
        <el-table-column prop="stationCode" label="工序编码" width="180" />
        <el-table-column prop="startTime" label="开始时间" width="180" />
        <el-table-column prop="endTime" label="结束时间" width="180" />
        <el-table-column prop="operator" label="操作人" width="120" />
        <el-table-column prop="result" label="结果">
          <template #default="scope">
            <el-tag :type="scope.row.result === 'PASS' ? 'success' : 'danger'">
              {{ scope.row.result === "PASS" ? "通过" : "不通过" }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts" name="stationInfo">
import { ref, watch } from "vue";

// 定义组件属性
const props = defineProps({
  queryParams: {
    type: Object,
    required: true
  }
});

// 定义站点信息接口
interface StationInfo {
  stationName: string;
  stationCode: string;
  startTime: string;
  endTime: string;
  operator: string;
  result: string;
}

// 是否有数据标志
const hasData = ref(false);

// 过站信息列表
const stationList = ref<StationInfo[]>([]);

// 监听查询参数变化
watch(
  () => props.queryParams,
  newVal => {
    if (newVal.queryValue) {
      fetchStationInfo(newVal.queryType, newVal.queryValue);
    }
  },
  { deep: true }
);

/**
 * 获取电芯过站信息
 */
function fetchStationInfo(queryType: string, queryValue: string) {
  // TODO: 替换为实际API调用
  console.log("查询过站信息:", queryType, queryValue);

  // 模拟数据，实际项目中应替换为API请求
  setTimeout(() => {
    stationList.value = [
      {
        stationName: "极片分切",
        stationCode: "JPFQ",
        startTime: "2025-06-10 08:00:00",
        endTime: "2025-06-10 08:15:00",
        operator: "张三",
        result: "PASS"
      },
      {
        stationName: "极耳焊接",
        stationCode: "JEHJ",
        startTime: "2025-06-10 08:30:00",
        endTime: "2025-06-10 08:45:00",
        operator: "李四",
        result: "PASS"
      },
      {
        stationName: "电芯组装",
        stationCode: "DXZZ",
        startTime: "2025-06-10 09:00:00",
        endTime: "2025-06-10 09:30:00",
        operator: "王五",
        result: "PASS"
      }
    ];
    hasData.value = true;
  }, 500);
}
</script>

<style lang="scss" scoped>
.station-info-container {
  height: 100%;
  padding: 20px;
  .info-content {
    margin-top: 20px;
  }
}
</style>
