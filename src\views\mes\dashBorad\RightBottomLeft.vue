<template>
  <router-link to="/mes/alram-analysis/sheet-count">
    <div class="dashboard-chart">
      <!-- 故障类型最多次数图表 -->
      <div class="chart-container glow-effect" @mouseenter="handleChartHover" @mouseleave="handleChartLeave">
        <div class="chart-title">{{ $t("chart.faultTypeCount") }}</div>
        <v-chart ref="typeChartRef" :autoresize="true" :option="typeChartOptions" :theme="chartTheme" class="chart-instance" />
      </div>
    </div>
  </router-link>
</template>

<script setup lang="ts">
import { use } from "echarts/core";
import { CanvasRenderer } from "echarts/renderers";
import { BarChart } from "echarts/charts";
import { GridComponent, TooltipComponent, LegendComponent, TitleComponent } from "echarts/components";
import { graphic } from "echarts/core";
import { useI18n } from "vue-i18n";

use([Canvas<PERSON><PERSON><PERSON>, Bar<PERSON>hart, GridComponent, TooltipComponent, LegendComponent, TitleComponent]);

const i18n = useI18n();

// 定义props
const props = defineProps({
  data: {
    type: Object,
    required: true,
    validator: value => {
      return "类型次数" in value && value.类型次数.every(item => "machine" in item && "fault_type" in item && "fault_count" in item);
    }
  },
  theme: {
    type: String,
    default: "dark"
  }
});

// 图表引用
const typeChartRef = ref();

// 图表主题
const chartTheme = computed(() => (props.theme === "dark" ? "dark" : "light"));

// 颜色方案
const getRandomColor = () =>
  new graphic.LinearGradient(0, 0, 0, 1, [
    { offset: 0, color: "#3B7BFF" },
    { offset: 1, color: "#00D1FF" }
  ]);

const COLOR_PALETTE = {
  dark: {
    primary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: getRandomColor() },
      { offset: 1, color: getRandomColor() }
    ]),
    secondary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#FF7EB3" },
      { offset: 1, color: "#FF758C" }
    ]),
    background: "rgba(8, 18, 44, 0.8)",
    font: "#E1F3FF",
    axisLine: "rgba(200, 200, 255, 0.2)",
    splitLine: "rgba(100, 150, 255, 0.1)"
  },
  light: {
    primary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#4285F4" },
      { offset: 1, color: "#34A853" }
    ]),
    secondary: new graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: "#EA4335" },
      { offset: 1, color: "#FBBC05" }
    ]),
    background: "rgba(255, 255, 255, 0.9)",
    font: "#333333",
    axisLine: "rgba(0, 0, 0, 0.1)",
    splitLine: "rgba(0, 0, 0, 0.05)"
  }
};

const colors = computed(() => COLOR_PALETTE[chartTheme.value]);

// 处理后的数据
const sortedTypeData = computed(() => (props.data.类型次数 ? [...props.data.类型次数].sort((a, b) => b.fault_count - a.fault_count) : []));

// 图表配置 - 类型次数图表
const typeChartOptions = computed(() => ({
  backgroundColor: "transparent",
  grid: { top: "25%", left: "8%", right: "4%", bottom: "5%", containLabel: true },
  tooltip: {
    trigger: "axis",
    backgroundColor: "rgba(0, 30, 80, 0.9)",
    padding: [10, 15],
    textStyle: { color: colors.value.font, fontSize: 14 },
    formatter: params => {
      const data = sortedTypeData.value[params[0].dataIndex];
      return `
        <div style="font-weight:600;margin-bottom:6px;font-size:16px">${data.machine}</div>
        <div style="display:flex;align-items:center">
          <span style="background:${params[0].color};width:12px;height:12px;border-radius:2px;display:inline-block;margin-right:8px"></span>
          <span>${i18n.t("chart.faultType")}：${data.fault_type}</span>
          <span style="font-weight:600;margin-left:20px">${i18n.t("chart.count")}：${data.fault_count}</span>
        </div>
      `;
    }
  },
  xAxis: {
    type: "category",
    data: sortedTypeData.value.map(item => item.machine),
    axisLabel: { color: colors.value.font, fontSize: 12, rotate: 30 }
  },
  yAxis: {
    type: "value",
    name: i18n.t("chart.faultCountUnit"),
    nameTextStyle: {
      color: colors.value.font,
      padding: [0, 0, 0, 0],
      fontSize: 12
    },
    nameGap: 30,
    nameLocation: "end",
    axisLabel: { color: colors.value.font, margin: 12 }
  },
  series: [
    {
      type: "bar",
      barWidth: "45%",
      data: sortedTypeData.value.map(item => ({
        value: item.fault_count,
        itemStyle: { color: getRandomColor(), borderRadius: 6 }
      })),
      label: {
        show: true,
        position: "top",
        color: colors.value.font,
        fontSize: 11,
        distance: 8,
        formatter: params => {
          const dataItem = sortedTypeData.value[params.dataIndex];
          return `${dataItem.fault_type}\n${dataItem.fault_count}`;
        }
      }
    }
  ]
}));

// 图表交互
const handleChartHover = () => {
  typeChartRef.value?.dispatchAction({ type: "highlight", seriesIndex: 0 });
};

const handleChartLeave = () => {
  typeChartRef.value?.dispatchAction({ type: "downplay", seriesIndex: 0 });
};

defineExpose({
  typeChartRef
});
</script>

<style scoped lang="scss">
.dashboard-chart {
  width: 100%;
  height: 100%;
}
.chart-container {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  background: rgb(255 255 255 / 5%);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;
  &:hover {
    box-shadow: 0 8px 25px rgb(0 180 255 / 30%);
    transform: translateY(-5px);
    &::before {
      left: 100%;
    }
  }
  &::before {
    position: absolute;
    top: 0;
    left: -100%;
    z-index: 10;
    width: 100%;
    height: 2px;
    content: "";
    background: linear-gradient(90deg, transparent, rgb(0 180 255 / 80%), transparent);
    transition: 0.5s;
  }
}
.chart-title {
  padding: 16px 20px 12px;
  font-size: 18px;
  font-weight: 600;
  color: v-bind("colors.font");
  background: linear-gradient(90deg, rgb(0 120 255 / 10%), transparent);
  border-bottom: 1px solid rgb(255 255 255 / 5%);
}
.chart-instance {
  flex: 1;
  width: 100%;
  height: 100%;
}
.glow-effect {
  position: relative;
  overflow: hidden;
  &::after {
    position: absolute;
    inset: 0;
    pointer-events: none;
    content: "";
    background: radial-gradient(circle at 20% 30%, rgb(0 180 255 / 10%), transparent 60%);
  }
}
</style>
