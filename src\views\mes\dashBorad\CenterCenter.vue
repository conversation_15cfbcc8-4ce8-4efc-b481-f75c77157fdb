<template>
  <div class="main">
    <!-- 销售总额 -->
    <div class="total">
      <div class="data1">
        <span>{{ t("dashboard.centerCenter.yieldRate") }}</span>
        <p>{{ props.productionData.advantage_rate }}</p>
      </div>
      <div class="data2">
        <span>{{ t("dashboard.centerCenter.goodProductTotal") }}</span>
        <p>{{ props.productionData.totalSumCount }}</p>
      </div>
      <div class="data3">
        <span>{{ t("dashboard.centerCenter.ppm") }}</span>
        <p>{{ props.productionData.ppm }}</p>
      </div>
      <div class="data4">
        <span>{{ t("dashboard.centerCenter.ngCount") }}</span>
        <p>{{ props.productionData.ng_count }}</p>
      </div>
      <canvas class="rain"></canvas>
      <canvas class="dashed"></canvas>
      <div class="sphere">
        <div class="sphere-bg"></div>
        <div class="sum">
          <span>{{ t("dashboard.centerCenter.productionTotal") }}</span>
          <p>{{ props.productionData.prod_count }}</p>
        </div>
      </div>
      <div class="cicle3"></div>
      <div class="cicle4"></div>
      <div class="cicle5"></div>
      <div class="cicle6"></div>
      <div class="cicle7"></div>
      <div class="cicle8">
        <span>{{ props.productionData.yearonyear }}</span>
        <p>{{ t("dashboard.centerCenter.yearOnYear") }}</p>
      </div>
      <div class="cicle9">
        <span>{{ props.productionData.completion_rate }}</span>
        <p>{{ t("dashboard.centerCenter.completionRate") }}</p>
      </div>
      <div class="cicle10">
        <span>{{ props.productionData.ng_rate }}</span>
        <p>{{ t("dashboard.centerCenter.ngRate") }}</p>
      </div>
      <div class="cicle11">
        <span>{{ props.productionData.loss_rate }}</span>
        <p>{{ t("dashboard.centerCenter.lossRate") }}</p>
      </div>
    </div>
    <canvas class="rain"></canvas>
    <!-- <canvas class="dashed"></canvas> -->
  </div>
</template>

<script setup>
import { useI18n } from "vue-i18n";
import { defineProps, onMounted } from "vue";

const { t } = useI18n();

// 定义 props 接收生产数据
const props = defineProps({
  productionData: {
    type: Object,
    default: () => ({
      Control: "生产数据",
      prod_count: "0",
      ng_count: "0",
      ppm: "0",
      ng_rate: "0%",
      totalSumCount: "0",
      loss_rate: "0%",
      yearonyear: "0%",
      completion_rate: "0%",
      advantage_rate: "0%"
    })
  }
});

// function generateData(count, amplitude) {
//   const data = [];
//   for (let i = 0; i < count; i++) {
//     const value = (Math.random() - 0.5) * amplitude + 50;
//     data.push(value);
//   }
//   return data;
// }
// 这里可以写逻辑代码
function rainBg() {
  let c = document.querySelector(".rain");
  let ctx = c.getContext("2d"); //获取canvas上下文
  let w = (c.width = document.querySelector(".total").clientWidth);
  let h = (c.height = document.querySelector(".total").clientHeight);
  //设置canvas宽、高

  function random(min, max) {
    return Math.random() * (max - min) + min;
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  function RainDrop() {}
  //雨滴对象 这是绘制雨滴动画的关键
  RainDrop.prototype = {
    init: function () {
      this.x = random(0, w); //雨滴的位置x
      this.y = h; //雨滴的位置y
      this.color = "hsl(180, 100%, 50%)"; //雨滴颜色 长方形的填充色
      this.vy = random(4, 5); //雨滴下落速度
      this.hit = 0; //下落的最大值
      this.size = 2; //长方形宽度
    },
    draw: function () {
      if (this.y > this.hit) {
        let linearGradient = ctx.createLinearGradient(this.x, this.y, this.x, this.y + this.size * 30);
        // 设置起始颜色
        linearGradient.addColorStop(0, "#14789c");
        // 设置终止颜色
        linearGradient.addColorStop(1, "#090723");
        // 设置填充样式
        ctx.fillStyle = linearGradient;
        ctx.fillRect(this.x, this.y, this.size, this.size * 50); //绘制长方形，通过多次叠加长方形，形成雨滴下落效果
      }
      this.update(); //更新位置
    },
    update: function () {
      if (this.y > this.hit) {
        this.y -= this.vy; //未达到底部，增加雨滴y坐标
      } else {
        this.init();
      }
    }
  };

  function resize() {
    w = c.width = window.innerWidth;
    h = c.height = window.innerHeight;
  }

  //初始化一个雨滴

  let rs = [];
  for (let i = 0; i < 10; i++) {
    setTimeout(function () {
      let r = new RainDrop();
      r.init();
      rs.push(r);
    }, i * 300);
  }

  function anim() {
    ctx.clearRect(0, 0, w, h); //填充背景色，注意不要用clearRect，否则会清空前面的雨滴，导致不能产生叠加的效果
    for (let i = 0; i < rs.length; i++) {
      rs[i].draw(); //绘制雨滴
    }
    requestAnimationFrame(anim); //控制动画帧
  }

  window.addEventListener("resize", resize);
  //启动动画
  anim();
}

// 中间虚线
function dashed() {
  let canvas = document.querySelector(".dashed");
  let ctx = canvas.getContext("2d");
  let w = (canvas.width = document.querySelector(".total").clientWidth);
  let h = (canvas.height = (document.querySelector(".total").clientHeight / 3) * 2);
  ctx.lineWidth = 3;
  ctx.setLineDash([3, 3]);
  ctx.fillStyle = "#93f8fb";
  ctx.shadowOffsetX = 0;
  // 阴影的y偏移
  ctx.shadowOffsetY = 0;
  // 阴影颜色
  ctx.shadowColor = "#93f8fb";
  // 阴影的模糊半径
  ctx.shadowBlur = 15;
  ctx.save(); //缓存初始状态
  // 绘制第一条曲线
  ctx.beginPath();
  let grd1 = ctx.createLinearGradient((w / 11) * 2, h / 3, (w / 5) * 2, h);
  grd1.addColorStop(0, "#54e2e6");
  grd1.addColorStop(1, "#065261");
  ctx.strokeStyle = grd1;
  ctx.moveTo((w / 5) * 2, h);
  ctx.quadraticCurveTo(w / 5, (h / 6) * 5, (w / 11) * 2, h / 3);
  ctx.stroke();
  // 绘制第一条曲线上的圆光效果
  ctx.beginPath();
  ctx.moveTo((w / 11) * 2, h / 3);
  ctx.arc((w / 11) * 2, h / 3, 5, 0, Math.PI * 2);
  ctx.fill();
  ctx.restore();
  ctx.save();
  // 绘制第二条线
  ctx.beginPath();
  let grd2 = ctx.createLinearGradient((w / 11) * 3.3, h / 2, (w / 3) * 1.1, (h / 6) * 5);
  grd2.addColorStop(0, "#e08d03");
  grd2.addColorStop(1, "#2e6a5c");
  ctx.strokeStyle = grd2;
  ctx.moveTo((w / 3) * 1.1, (h / 6) * 5);
  ctx.quadraticCurveTo((w / 5) * 1.5, (h / 6) * 4.2, (w / 11) * 3.3, h / 2);
  ctx.stroke();
  // 绘制第二条曲线上的圆光效果
  ctx.beginPath();
  ctx.moveTo((w / 11) * 3.3, h / 2);
  ctx.arc((w / 11) * 3.3, h / 2, 5, 0, Math.PI * 2);
  ctx.fill();
  ctx.restore();
  ctx.save();
  // 绘制第三条线
  ctx.beginPath();
  let grd3 = ctx.createLinearGradient((w / 3) * 1.4, h / 5, (w / 5) * 2, h / 2);
  grd3.addColorStop(0, "#e08d03");
  grd3.addColorStop(1, "#2e6a5c");
  ctx.strokeStyle = grd3;
  ctx.moveTo((w / 5) * 2, h / 2);
  ctx.quadraticCurveTo((w / 3) * 1.2, (h / 4) * 1.4, (w / 3) * 1.4, h / 5);
  ctx.stroke();
  // 绘制第三条曲线上的圆光效果
  ctx.beginPath();
  ctx.moveTo((w / 3) * 1.4, h / 5);
  ctx.arc((w / 3) * 1.4, h / 5, 5, 0, Math.PI * 2);
  ctx.fill();
  ctx.restore();
  ctx.save();
  // 绘制第四条线
  ctx.beginPath();
  let grd4 = ctx.createLinearGradient((w / 5) * 3.1, (h / 3) * 1.2, (w / 5) * 3.2, (h / 2) * 1.5);
  grd4.addColorStop(0, "#e08d03");
  grd4.addColorStop(1, "#2e6a5c");
  ctx.strokeStyle = grd4;
  ctx.moveTo((w / 5) * 3.2, (h / 2) * 1.5);
  ctx.quadraticCurveTo((w / 5) * 3.35, (h / 2) * 1.2, (w / 5) * 3.1, (h / 3) * 1.2);
  ctx.stroke();
  // 绘制第四条曲线上的圆光效果
  ctx.beginPath();
  ctx.moveTo((w / 5) * 3.1, (h / 3) * 1.2);
  ctx.arc((w / 5) * 3.1, (h / 3) * 1.2, 5, 0, Math.PI * 2);
  ctx.fill();
  ctx.restore();
  ctx.save();
  // 绘制第五条线
  ctx.beginPath();
  let grd5 = ctx.createLinearGradient((w / 5) * 3.3, h / 4, (w / 5) * 3.2, (h / 2) * 1.9);
  grd5.addColorStop(0, "#e08d03");
  grd5.addColorStop(1, "#2e6a5c");
  ctx.strokeStyle = grd5;
  ctx.moveTo((w / 5) * 3.03, (h / 2) * 1.9);
  ctx.quadraticCurveTo((w / 5) * 3.8, (h / 2) * 1.2, (w / 5) * 3.3, h / 4);
  ctx.stroke();
  // 绘制第五条曲线上的圆光效果
  ctx.beginPath();
  ctx.moveTo((w / 5) * 3.3, h / 4);
  ctx.arc((w / 5) * 3.3, h / 4, 5, 0, Math.PI * 2);
  ctx.fill();
  ctx.restore();
  ctx.save();
  // 绘制第六条线
  ctx.beginPath();
  let grd = ctx.createLinearGradient((w / 5) * 3.8, (h / 2) * 1.2, (w / 5) * 2.9, h);
  grd.addColorStop(0, "#e08d03");
  grd.addColorStop(1, "#2e6a5c");
  ctx.strokeStyle = grd;
  ctx.moveTo((w / 5) * 2.9, h);
  ctx.quadraticCurveTo((w / 5) * 3.7, (h / 2) * 1.6, (w / 5) * 3.8, (h / 2) * 1.2);
  ctx.stroke();
  // 绘制第六条曲线上的圆光效果
  ctx.beginPath();
  ctx.moveTo((w / 5) * 3.8, (h / 2) * 1.2);
  ctx.arc((w / 5) * 3.8, (h / 2) * 1.2, 5, 0, Math.PI * 2);
  ctx.fill();
}
onMounted(() => {
  rainBg();

  dashed();
});
</script>

<style scoped>
@import "./style.css";
.dashed {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999;
  width: 600px;
}
.rain {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 999;
}
.chart-container {
  width: 100%;
  height: 100%;
}
.chart {
  width: 100%;
  height: 100%;
}
</style>
