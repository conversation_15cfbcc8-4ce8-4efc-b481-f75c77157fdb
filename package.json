{"name": "simple-admin", "private": true, "version": "1.0.0", "type": "module", "description": "SimpleAdmin是ElementUI最好看的开源通用业务型后台管理系统", "author": {"name": "huguodong", "email": "<EMAIL>", "url": "https://gitee.com/dotnetmoyu"}, "license": "MIT", "homepage": "https://gitee.com/dotnetmoyu/SimpleAdmin", "repository": {"type": "git", "url": "*************:dotnetmoyu/SimpleAdmin.git"}, "bugs": {"url": "https://gitee.com/dotnetmoyu/SimpleAdmin/issues"}, "scripts": {"dev": "vite", "serve": "vite", "build:dev": "vue-tsc && vite build --mode development", "build:test": "vue-tsc && vite build --mode test", "build:pro": " vite build --mode production", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "preview": "pnpm run build:dev && vite preview", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "release": "standard-version", "commit": "git add -A && czg && git push"}, "dependencies": {"@antv/g2plot": "^2.4.32", "@element-plus/icons-vue": "^2.3.1", "@highlightjs/vue-plugin": "^2.1.0", "@iconify/vue": "^4.1.2", "@popperjs/core": "^2.11.8", "@vueuse/core": "^11.0.3", "autofit.js": "^3.2.4", "axios": "^1.7.7", "dayjs": "^1.11.13", "default-passive-events": "^2.0.0", "dom-to-image": "^2.6.0", "echarts": "^5.5.1", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.7.6", "element-resize-detector": "^1.2.4", "highlight.js": "^11.10.0", "html-to-image": "1.11.11", "html2canvas": "^1.4.1", "lodash-es": "^4.17.21", "md5": "^2.3.0", "mitt": "^3.0.1", "moment": "^2.30.1", "mqtt": "^5.10.1", "nprogress": "^0.2.0", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^3.2.1", "print-js": "^1.6.0", "qs": "^6.13.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.3", "v-calendar": "^3.1.2", "vue": "^3.5.5", "vue-cropper": "^1.1.1", "vue-echarts": "^6.6.9", "vue-i18n": "^11.1.3", "vue-router": "^4.4.5", "vue3-scroll-seamless": "^1.0.6", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@iconify/json": "^2.2.247", "@types/md5": "^2.3.5", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.15", "@types/sm-crypto": "^0.3.4", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.14.1", "@vitejs/plugin-vue": "^5.1.3", "@vitejs/plugin-vue-jsx": "^4.0.1", "autoprefixer": "^10.4.20", "code-inspector-plugin": "^0.16.1", "cross-env": "^7.0.3", "cz-git": "^1.9.4", "czg": "^1.9.4", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.26.0", "lint-staged": "^15.2.10", "mockjs": "^1.1.0", "naive-ui": "^2.39.0", "postcss": "^8.4.45", "postcss-html": "^1.7.0", "prettier": "^3.3.3", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.78.0", "sm-crypto": "^0.3.13", "standard-version": "^9.5.0", "stylelint": "^16.9.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^5.1.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^13.1.0", "typescript": "^5.5.2", "unocss": "^0.62.3", "unplugin-auto-import": "^0.18.3", "unplugin-icons": "^0.19.3", "unplugin-vue-components": "^0.27.4", "unplugin-vue-setup-extend-plus": "^1.0.1", "vite": "^5.4.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mock": "^3.0.2", "vite-plugin-pwa": "^0.20.5", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.3.5", "vite-plugin-vue-inspector": "^5.3.1", "vue-tsc": "^2.1.6"}, "engines": {"node": ">=16.0.0"}, "browserslist": {"production": ["> 1%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}