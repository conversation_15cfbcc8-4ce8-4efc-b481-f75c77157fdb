<template>
  <router-link to="/mes/raw-material-warning/warning">
    <div class="cyber-metrics">
      <!-- 三列等宽布局 -->
      <div class="metric-row">
        <!-- 原材料箱数 -->
        <div class="metric-card material-card" @mouseenter="hoverCard('material')" @mouseleave="leaveCard()">
          <div class="card-border"></div>
          <div class="card-content">
            <div class="icon">
              <svg viewBox="0 0 24 24">
                <path d="M20 7H4V5H20V7Z" fill="currentColor" />
                <path d="M20 11H4V9H20V11Z" fill="currentColor" />
                <path d="M4 15H20V13H4V15Z" fill="currentColor" />
                <path d="M20 19H4V17H20V19Z" fill="currentColor" />
              </svg>
            </div>
            <div class="text">
              <div class="title">{{ t("dashboard.rightTop.materialCount") }}</div>
              <div class="value">{{ animatedMaterial }}</div>
              <!-- <div class="trend" :class="materialTrend">
              <span>{{ materialChange }}%</span>
            </div> -->
            </div>
          </div>
        </div>

        <!-- 超时未处理 -->
        <div
          class="metric-card overdue-card"
          @mouseenter="hoverCard('overdue')"
          @mouseleave="leaveCard()"
          :class="{ 'alert-pulse': overdueCritical }"
        >
          <div class="card-border"></div>
          <div class="card-content">
            <div class="icon">
              <svg viewBox="0 0 24 24">
                <path d="M13 13H11V7H13V13Z" fill="currentColor" />
                <path d="M13 17H11V15H13V17Z" fill="currentColor" />
                <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2Z" fill="currentColor" />
              </svg>
            </div>
            <div class="text">
              <div class="title">{{ t("dashboard.rightTop.overdueUnprocessed") }}</div>
              <div class="value">{{ animatedOverdue }}</div>
              <!-- <div class="subtext">平均 {{ averageOverdueTime }} 分钟</div> -->
            </div>
          </div>
        </div>

        <!-- 即将到期 -->
        <div class="metric-card expiring-card" @mouseenter="hoverCard('expiring')" @mouseleave="leaveCard()">
          <div class="card-border"></div>
          <div class="card-content">
            <div class="icon">
              <svg viewBox="0 0 24 24">
                <path
                  d="M12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20Z"
                  stroke="currentColor"
                  stroke-width="2"
                />
                <path d="M12 6V12L16 14" stroke="currentColor" stroke-width="2" />
              </svg>
            </div>
            <div class="text">
              <div class="title">{{ t("dashboard.rightTop.expiringSoon") }}</div>
              <div class="value">{{ animatedExpiring }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </router-link>
</template>

<script setup>
import { ref, computed, onMounted, watch, defineProps } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
  data: {
    type: Array,
    default: () => [
      {
        Control: "原材料预警",
        RawMaterialCount: 0,
        OverdueUnprocessedCount: 0,
        NearlyExpiredCount: 0
      }
    ]
  }
});

// 动画数字
const animatedMaterial = ref(0);
const animatedOverdue = ref(0);
const animatedExpiring = ref(0);

// 交互状态
const activeCard = ref(null);

const overdueCritical = computed(() => props.data[0].OverdueUnprocessedCount > 5);

// 数字动画
const animateValue = (target, current, duration = 800) => {
  const start = Date.now();
  const initial = current.value;
  const update = () => {
    const progress = Math.min((Date.now() - start) / duration, 1);
    current.value = Math.floor(initial + progress * (target - initial));
    if (progress < 1) requestAnimationFrame(update);
  };
  requestAnimationFrame(update);
};

// 卡片交互
const hoverCard = type => {
  activeCard.value = type;
};

const leaveCard = () => {
  activeCard.value = null;
};

// 初始化
onMounted(() => {
  animateValue(props.data[0].RawMaterialCount, animatedMaterial);
  animateValue(props.data[0].OverdueUnprocessedCount, animatedOverdue);
  animateValue(props.data[0].NearlyExpiredCount, animatedExpiring);

  watch(
    () => props.data[0].RawMaterialCount,
    newVal => animateValue(newVal, animatedMaterial)
  );
  watch(
    () => props.data[0].OverdueUnprocessedCount,
    newVal => animateValue(newVal, animatedOverdue)
  );
  watch(
    () => props.data[0].NearlyExpiredCount,
    newVal => animateValue(newVal, animatedExpiring)
  );
});
</script>

<style scoped lang="scss">
.cyber-metrics {
  width: 100%;
  font-family: Arial, sans-serif;
}
.metric-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  width: 100%;
}
.metric-card {
  position: relative;
  height: 120px;
  padding: 20px;
  overflow: hidden;
  background: rgb(89 98 123 / 23%);
  border-radius: 8px;
  transition: all 0.3s ease;
  .card-border {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 3px;
    transition: all 0.3s ease;
  }
  &.material-card {
    .card-border {
      background: linear-gradient(90deg, #3b7bff, #00d1ff);
    }
    .icon svg {
      color: #4fc08d;
    }
    .value {
      color: #4fc08d;
    }
  }
  &.overdue-card {
    .card-border {
      background: linear-gradient(90deg, #ff4d4f, #ff758c);
    }
    .icon svg {
      color: #ff4d4f;
    }
    .value {
      color: #ff4d4f;
    }
    &.alert-pulse {
      animation: pulse 1.5s infinite;
    }
  }
  &.expiring-card {
    .card-border {
      background: linear-gradient(90deg, #faad14, #ffd666);
    }
    .icon svg {
      color: #faad14;
    }
    .value {
      color: #faad14;
    }
  }
  &:hover {
    box-shadow: 0 5px 15px rgb(0 0 0 / 30%);
    transform: translateY(-3px);
    .card-border {
      height: 5px;
    }
  }
}
.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}
.icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  margin-right: 15px;
  svg {
    width: 30px;
    height: 30px;
  }
}
.text {
  flex: 1;
}
.title {
  margin-bottom: 5px;
  font-size: 16px;
  color: #d7dce4;
}
.value {
  margin: 5px 0;
  font-size: 50px;
  font-weight: bold;
}
.trend {
  display: inline-block;
  padding: 3px 8px;
  font-size: 12px;
  font-weight: bold;
  border-radius: 4px;
  &.up {
    color: #52c41a;
    background: rgb(82 196 26 / 20%);
  }
  &.down {
    color: #ff4d4f;
    background: rgb(255 77 79 / 20%);
  }
}
.subtext {
  font-size: 12px;
  color: #8a9bb8;
}
.progress {
  display: flex;
  align-items: center;
  margin-top: 5px;
  .progress-bar {
    height: 4px;
    margin-right: 8px;
    background: linear-gradient(90deg, #faad14, #ffd666);
    border-radius: 2px;
    transition: width 0.5s ease;
  }
  span {
    font-size: 12px;
    color: #faad14;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgb(255 77 79 / 40%);
  }
  70% {
    box-shadow: 0 0 0 8px rgb(255 77 79 / 0%);
  }
  100% {
    box-shadow: 0 0 0 0 rgb(255 77 79 / 0%);
  }
}

@media (width <= 768px) {
  .metric-row {
    grid-template-columns: 1fr;
  }
  .metric-card {
    height: auto;
    padding: 15px;
  }
}
</style>
