<template>
  <div class="table-box card table-main">
    <v-chart :autoresize="true" :option="lineChartOptions" class="chart" />
  </div>
</template>

<script setup>
import { ref, watch, defineProps } from "vue";

// 定义 props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
});

const lineChartOptions = ref({});

// 生成随机颜色
const getRandomColor = () => {
  const letters = "0123456789ABCDEF";
  let color = "#";
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
};

const updateChartOptions = () => {
  const machines = props.data.map(item => item.machine);
  const faultCounts = props.data.map(item => item.fault_time);

  const itemStyles = faultCounts.map(() => ({
    itemStyle: {
      color: getRandomColor()
    }
  }));

  lineChartOptions.value = {
    grid: {
      left: "3%",
      right: "3%",
      bottom: "3%",
      containLabel: true
    },
    legend: {
      data: ["故障时长"],
      top: "top",
      left: "center"
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        const dataIndex = params[0].dataIndex;
        const item = props.data[dataIndex];
        return `
          <div>机台: ${item.machine}</div>
          <div>故障时长: ${item.fault_time}</div>
        `;
      }
    },
    xAxis: {
      type: "category",
      data: machines
    },
    yAxis: {
      name: "故障时长",
      type: "value"
    },
    series: [
      {
        name: "故障时长",
        data: faultCounts.map((count, index) => ({
          value: count,
          ...itemStyles[index]
        })),
        type: "bar",
        label: {
          show: true,
          position: "top"
        }
      }
    ]
  };
};

// 初始化图表配置
updateChartOptions();

// 监听 props.data 的变化，若有变化则更新图表配置
watch(
  () => props.data,
  () => {
    updateChartOptions();
  }
);
</script>

<style scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>
