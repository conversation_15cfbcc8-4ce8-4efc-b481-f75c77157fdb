<template>
  <div class="main-box" id="651517457031237">
    <TreeFilter
      :default-expand-all="false"
      ref="treeFilter"
      label="name"
      :title="t('rawMaterialWarning.organizationList')"
      :request-api="rawMaterialApi.getTree"
      @change1="handleNodeClick"
    >
      <template #shit>
        <CalendarCustom current-theme="light" :header-title="title" :date-data="dateData"> </CalendarCustom>
      </template>
    </TreeFilter>
    <div class="table-box">
      <ProTable
        v-if="!isChart"
        ref="proTableRef"
        :init-param="initParam"
        :columns="columns"
        :request-api="fetchFilteredData"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
      >
        <template #tableHeader>
          <el-button type="primary" @click="openAddDialog">{{ t("rawMaterialWarning.add") }}</el-button>
          <!-- <el-button type="success" @click="importData">导入</el-button> -->
          <el-button type="primary" @click="exportData">{{ t("rawMaterialWarning.export") }}</el-button>
          <el-button type="danger" :disabled="!selectedRows.length" @click="deleteSelectedRows">{{ t("rawMaterialWarning.batchDelete") }}</el-button>
        </template>
        <template #toolButton>
          <el-tooltip :content="t('common.switchToChart')" placement="top">
            <el-button :icon="Switch" circle @click="changeTable" />
          </el-tooltip>
        </template>
        <template #operation="{ row }">
          <el-button type="primary" size="small" @click="openEditDialog(row)">{{ t("rawMaterialWarning.edit") }}</el-button>
          <el-button type="danger" size="small" @click="deleteRow(row)">{{ t("rawMaterialWarning.delete") }}</el-button>
          <el-button type="info" size="small" @click="openViewDialog(row)">{{ t("rawMaterialWarning.view") }}</el-button>
        </template>
      </ProTable>
      <SwitchChart :current-tree-type="currentTreeType" :init-params="initParam" v-else @toggle-view="changeTable"></SwitchChart>

      <el-dialog
        v-model="dialogVisible"
        :title="
          dialogType === 'add' ? t('rawMaterialWarning.add') : dialogType === 'edit' ? t('rawMaterialWarning.edit') : t('rawMaterialWarning.view')
        "
      >
        <el-form :model="formData" ref="formRef" label-width="120px" :rules="rules">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.factory')" prop="factory">
                <el-input v-model="formData.factory" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.workshop')" prop="workshop">
                <el-input v-model="formData.workshop" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.productionLine')" prop="productionLine">
                <el-input v-model="formData.productionLine" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.machineCode')" prop="machineCode">
                <el-select v-model="formData.machineCode" placeholder="请选择" :disabled="dialogType === 'view'" @change="handleMachCodeChange">
                  <el-option v-for="option in getMachineOptions" :key="option.value" :label="option.value" :value="option.value"></el-option>
                </el-select>
                <!-- <el-input v-model="formData.machineCode" :disabled="dialogType === 'view'"></el-input> -->
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.machineName')" prop="machineName">
                <el-input v-model="formData.machineName" :disabled="true"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.stationCode')" prop="stationCode">
                <el-input v-model="formData.stationCode" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.stationName')" prop="stationName">
                <el-input v-model="formData.stationName" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.materialType')" prop="materialType">
                <el-input v-model="formData.materialType" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.batch')" prop="batch">
                <el-input v-model="formData.batch" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.materialCode')" prop="materialCode">
                <el-input v-model="formData.materialCode" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.materialName')" prop="materialName">
                <el-input v-model="formData.materialName" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.unit')" prop="unit">
                <el-input v-model="formData.unit" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.totalQuantity')">
                <el-input v-model="formData.totalQuantity" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.usedQuantity')">
                <el-input v-model="formData.usedQuantity" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.remainingQuantity')">
                <el-input v-model="formData.remainingQuantity" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.isReplaced')">
                <el-switch v-model="formData.isReplaced" :disabled="dialogType === 'view'"></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.feedingTime')">
                <el-date-picker
                  v-model="formData.feedingTime"
                  type="datetime"
                  :placeholder="t('rawMaterialWarning.form.feedingTime')"
                  :disabled="dialogType === 'view'"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.operator')">
                <el-input v-model="formData.operator" :disabled="dialogType === 'view'"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="t('rawMaterialWarning.form.operationTime')">
                <el-date-picker
                  v-model="formData.operationTime"
                  type="datetime"
                  :placeholder="t('rawMaterialWarning.form.operationTime')"
                  :disabled="dialogType === 'view'"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <el-button @click="dialogVisible = false">{{ t("rawMaterialWarning.close") }}</el-button>
          <el-button v-if="dialogType !== 'view'" type="primary" @click="saveData">{{ t("rawMaterialWarning.save") }}</el-button>
        </template>
      </el-dialog>

      <el-dialog v-model="importDialogVisible" title="导入数据">
        <el-upload ref="uploadRef" action="/api/sys/upload/UploadExcelrcwh" :auto-upload="false" :before-upload="beforeUpload">
          <el-button type="primary">选择文件</el-button>
        </el-upload>
        <template #footer>
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="startImport">开始导入</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { productionReportCapacityApi, rawMaterialApi } from "@/api";
import * as XLSX from "xlsx";
import { Switch } from "@element-plus/icons-vue";
import SwitchChart from "./SwitchChart.vue";
// import moment from "moment";
import { clearObjectValues, convertCalendarData } from "@/utils";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
import { alramAnalysisApi } from "@/api";
// 添加 onMounted 钩子，页面加载时自动触发搜索
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
});
// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const getMachineOptions = computed(() => [
  // { label: "全部机台", value: "" }, // 添加空选项
  ...machineList.value.map(item => ({
    label: item.name,
    value: item.id
  }))
]);
const handleMachCodeChange = (value: string) => {
  // 根据选中的机台编码查找对应的机台名称
  const selectedMachine = machineList.value.find(machine => machine.id === value);
  if (selectedMachine) {
    formData.machineName = selectedMachine.name; // 填充机台名称
    console.log("formData.machineName updated to:", formData.machineName);
  } else {
    formData.machineName = ""; // 未找到时清空
    console.log("formData.machineName cleared");
  }
};
const title = computed(() => t("rawMaterialWarning.title"));
const dateData = ref([]);

const proTableRef = ref(null);
const initParam = ref({
  // time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")]
});
const exportParam = ref({});

const dialogVisible = ref(false);
const importDialogVisible = ref(false);
const dialogType = ref("add");
const formData = reactive({
  factory: "",
  workshop: "",
  productionLine: "",
  machineCode: "",
  machineName: "",
  stationCode: "",
  stationName: "",
  materialType: "",
  batch: "",
  materialCode: "",
  materialName: "",
  unit: "",
  totalQuantity: 0,
  usedQuantity: 0,
  remainingQuantity: 0,
  isReplaced: false,
  feedingTime: null,
  operator: "",
  operationTime: null
});
const formRef = ref(null);
const uploadRef = ref(null);
const selectedRows = ref([]);
const isChart = ref(false);
onMounted(async () => {
  const query = {
    Type: 17,
    IsPage: false
  };
  const { data } = await productionReportCapacityApi.getListMesReportData(query);
  dateData.value = convertCalendarData(data.list);
});
const changeTable = () => {
  isChart.value = !isChart.value;
};
const rules = reactive({
  factory: [{ required: true, message: "请填写工厂", trigger: "blur" }],
  workshop: [{ required: true, message: "请填写车间", trigger: "blur" }],
  productionLine: [{ required: true, message: "请填写线体", trigger: "blur" }],
  machineCode: [{ required: true, message: "请填写机台编码", trigger: "blur" }],
  machineName: [{ required: true, message: "请填写机台名称", trigger: "blur" }],
  stationCode: [{ required: true, message: "请填写工位编码", trigger: "blur" }],
  stationName: [{ required: true, message: "请填写工位名称", trigger: "blur" }],
  materialType: [{ required: true, message: "请填写原材料类型", trigger: "blur" }],
  batch: [{ required: true, message: "请填写原材料批次", trigger: "blur" }],
  materialCode: [{ required: true, message: "请填写原材料编码", trigger: "blur" }],
  materialName: [{ required: true, message: "请填写原材料名称", trigger: "blur" }],
  unit: [{ required: true, message: "请填写原材料单位", trigger: "blur" }]
});
const columns = computed(() => [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "id", label: "Id", isShow: false },
  { prop: "time", label: "时间", isShow: false },
  { prop: "factory", label: t("rawMaterialWarning.form.factory"), width: 150, align: "left", search: { el: "input" } },
  { prop: "workshop", label: t("rawMaterialWarning.form.workshop"), width: 150, align: "left", search: { el: "input" } },
  { prop: "productionLine", label: t("rawMaterialWarning.form.productionLine"), width: 150, align: "left", search: { el: "input" } },
  {
    prop: "machineCode",
    label: t("rawMaterialWarning.form.machineCode"),
    search: {
      el: "select",
      props: {
        clearable: false,
        placeholder: "请选择机台编码"
      },
      on: {
        change: (val: string) => {
          const selectedMachine = machineList.value.find(machine => machine.id === val);
          // 更新ProTable的搜索参数
          proTableRef.value.searchParam.machineCode = val;
          proTableRef.value.searchParam.machineName = selectedMachine?.name || "";
          proTableRef.value.getTableList(); // 重新加载数据
        }
      }
    },
    enum: computed(() => machineList.value.map(m => ({ label: m.id, value: m.id })))
  },
  // { prop: "machineName", label: "机台名称", width: 150, align: "left", search: { el: "input" } },
  {
    prop: "machineName",
    label: t("rawMaterialWarning.form.machineName"),
    search: {
      el: "select",
      props: {
        clearable: false,
        placeholder: "请选择机台名称"
      },
      on: {
        change: (val: string) => {
          const selectedMachine = machineList.value.find(machine => machine.name === val);
          // 更新ProTable的搜索参数
          proTableRef.value.searchParam.machineName = val;
          proTableRef.value.searchParam.machineCode = selectedMachine?.id || "";
          proTableRef.value.getTableList(); // 重新加载数据
        }
      }
    },
    enum: computed(() => machineList.value.map(m => ({ label: m.name, value: m.name })))
  },
  // { prop: "machineCode", label: "机台编码", width: 150, align: "left", search: { el: "input" } },
  // { prop: "machineName", label: "机台名称", width: 150, align: "left", search: { el: "input" } },
  { prop: "stationCode", label: t("rawMaterialWarning.form.stationCode"), width: 150, align: "left" },
  { prop: "stationName", label: t("rawMaterialWarning.form.stationName"), width: 150, align: "left", search: { el: "input" } },
  { prop: "materialType", label: t("rawMaterialWarning.form.materialType"), width: 150, align: "left" },
  { prop: "batch", label: t("rawMaterialWarning.form.batch"), width: 150, align: "left" },
  { prop: "materialCode", label: t("rawMaterialWarning.form.materialCode"), width: 150, align: "left" },
  { prop: "materialName", label: t("rawMaterialWarning.form.materialName"), width: 150, align: "left" },
  { prop: "unit", label: t("rawMaterialWarning.form.unit"), width: 100, align: "left" },
  { prop: "totalQuantity", label: t("rawMaterialWarning.form.totalQuantity"), width: 150, align: "left" },
  { prop: "usedQuantity", label: t("rawMaterialWarning.form.usedQuantity"), width: 150, align: "left" },
  { prop: "remainingQuantity", label: t("rawMaterialWarning.form.remainingQuantity"), width: 150, align: "left" },
  {
    prop: "isReplaced",
    label: t("rawMaterialWarning.form.isReplaced"),
    width: 120,
    formatter: row => (row.isReplaced ? t("rawMaterialWarning.status.yes") : t("rawMaterialWarning.status.no"))
    // search: {
    //   el: "select",
    //   props: {
    //     clearable: false,
    //     placeholder: "请选择是否更换原材料",
    //     options: [
    //       { value: true, label: "是" },
    //       { value: false, label: "否" }
    //     ]
    //   }
    // }
  },
  {
    prop: "feedingTime",
    label: t("rawMaterialWarning.form.feedingTime"),
    width: 180,
    align: "center",
    formatter: row => (row.feedingTime ? new Date(row.feedingTime).toLocaleString() : "")
    // search: { el: "date-picker", props: { type: "datetime" } }
  },
  { prop: "operator", label: t("rawMaterialWarning.form.operator"), width: 150, align: "left" },
  {
    prop: "operationTime",
    label: t("rawMaterialWarning.form.operationTime"),
    width: 180,
    align: "center",
    formatter: row => (row.operationTime ? new Date(row.operationTime).toLocaleString() : "")
    // search: { el: "date-picker", props: { type: "datetime" } }
  },
  { prop: "operation", label: t("rawMaterialWarning.operation"), width: 230, fixed: "right" }
]);
// 1. 获取机台列表（Type=0）
const fetchMachines = async () => {
  try {
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineModel || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // 添加：设置默认选中第一个机台
    // if (machineList.value.length > 0) {
    //   param.value.machine = machineList.value[0].id; // 设置默认机台 id
    // }
  } catch (error) {
    // console.error("机台列表请求失败:", error);
    // ElMessage.error("获取机台列表失败");
  }
};
const fetchData = async params => {
  return rawMaterialApi.ProductionMaterialRecordGet(params);
};

const fetchFilteredData = async params => {
  exportParam.value = params;
  const allData = await fetchData({ ...params, machineCode: params.deck || params.machine, productionLine: params.workline });
  exportParam.value.pageSize = allData.data.total;
  return allData;
};

const openAddDialog = () => {
  dialogType.value = "add";
  Object.keys(formData).forEach(key => {
    formData[key] = key === "totalQuantity" || key === "usedQuantity" || key === "remainingQuantity" ? 0 : "";
  });
  formData.isReplaced = false;
  formData.feedingTime = null;
  formData.operationTime = null;
  dialogVisible.value = true;
};

const openEditDialog = row => {
  dialogType.value = "edit";
  Object.assign(formData, row);
  dialogVisible.value = true;
};

const openViewDialog = row => {
  dialogType.value = "view";
  Object.assign(formData, row);
  dialogVisible.value = true;
};
const saveData = async () => {
  try {
    // if (dialogType.value === "view") return;
    // await formRef.value.validate(valid => {
    const valid = await formRef.value.validate();
    if (valid) {
      const params = {
        ...formData,
        id: dialogType.value === "edit" ? formData.id : undefined
      };
      let response;
      if (dialogType.value === "add") {
        response = await rawMaterialApi.ProductionMaterialRecordAdd(params);
      } else if (dialogType.value === "edit") {
        response = await rawMaterialApi.ProductionMaterialRecordEdit(params);
      }
      if (response.code === 200) {
        ElMessage.success(`${dialogType.value === "add" ? "新增" : "编辑"}成功`);
        dialogVisible.value = false;
        proTableRef.value.getTableList();
      } else {
        ElMessage.error(response.msg || "操作失败");
      }
    } else {
      ElMessage.error("请填写所有必填项");
    }
    // });
  } catch (error) {
    // ElMessage.error("网络请求失败，请重试");
  }
};

// const saveData = async () => {
//   try {
//     if (
//       !formData.factory ||
//       !formData.workshop ||
//       !formData.productionLine ||
//       !formData.machineCode ||
//       !formData.machineName ||
//       !formData.stationCode ||
//       !formData.stationName ||
//       !formData.materialType ||
//       !formData.batch ||
//       !formData.materialCode ||
//       !formData.materialName ||
//       !formData.unit
//     ) {
//       ElMessage.error("工厂、车间、线体、机台编码、机台名称、工位编码、工位名称、原材料类型、原材料批次、原材料编码、原材料名称、原材料单位为必填项");
//       return;
//     }

//     const params = { ...formData };
//     let response;
//     if (dialogType.value === "add") {
//       delete params.id;
//       response = await rawMaterialApi.ProductionMaterialRecordAdd(params);
//     } else if (dialogType.value === "edit") {
//       response = await rawMaterialApi.ProductionMaterialRecordEdit(params);
//     }

//     if (response.code === 200) {
//       ElMessage.success(`${dialogType.value === "add" ? "新增" : "编辑"}成功`);
//       dialogVisible.value = false;
//       proTableRef.value.getTableList();
//     } else {
//       ElMessage.error(response.msg || "操作失败");
//     }
//   } catch (error) {
//     ElMessage.error("网络请求失败，请重试");
//   }
// };

const deleteRow = async row => {
  try {
    const response = await rawMaterialApi.ProdProductionMaterialRecordDelete({ id: row.id });
    if (response.code === 200) {
      ElMessage.success(`删除 ${row.factory} 相关记录成功`);
      proTableRef.value.getTableList();
    } else {
      ElMessage.error(response.msg || "删除失败");
    }
  } catch (error) {
    ElMessage.error("网络请求失败，请重试");
  }
};

const deleteSelectedRows = async () => {
  try {
    const ids = selectedRows.value.map(row => row.id);
    if (ids.length === 0) {
      ElMessage.warning("请选择要删除的记录");
      return;
    }

    const response = await rawMaterialApi.ProdProductionMaterialRecordDelete({ ids });
    if (response.code === 200) {
      ElMessage.success(`批量删除 ${ids.length} 条记录成功`);
      selectedRows.value = [];
      proTableRef.value.getTableList();
    } else {
      ElMessage.error(response.msg || "批量删除失败");
    }
  } catch (error) {
    ElMessage.error("网络请求失败，请重试");
  }
};

const handleRowClick = row => {
  console.log("点击行数据：", row);
};

const handleSelectionChange = rows => {
  selectedRows.value = rows;
};

// const importData = () => {
//   importDialogVisible.value = true;
// };

const exportData = async () => {
  try {
    const params = {
      ...proTableRef.value.searchParam,
      page: 1,
      pageSize: exportParam.value.pageSize
    };

    const response = await fetchFilteredData(params);
    if (response.code !== 200) throw new Error(response.msg);
    const tableData = response.data.list;

    const exportColumns = columns.value.filter(col => !["selection", "operation", "id"].includes(col.type || col.prop));
    const headers = exportColumns.map(col => col.label);
    const dataRows = tableData.map(item => {
      return exportColumns.map(col => {
        if (col.prop === "feedingTime" && item[col.prop]) {
          return new Date(item[col.prop]).toLocaleString();
        }
        if (col.prop === "operationTime" && item[col.prop]) {
          return new Date(item[col.prop]).toLocaleString();
        }
        if (col.prop === "isReplaced") {
          return item[col.prop] ? t("rawMaterialWarning.status.yes") : t("rawMaterialWarning.status.no");
        }
        return item[col.prop] || "";
      });
    });

    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...dataRows]);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "设备数据");
    const exportDate = new Date().toISOString().slice(0, 10);
    XLSX.writeFile(workbook, `原材料录入记录_${exportDate}.xlsx`);
    ElMessage.success("导出成功");
  } catch (error) {
    ElMessage.error(`导出失败: ${error.message}`);
  }
};

const beforeUpload = () => {
  return true;
};

const startImport = () => {
  uploadRef.value!.submit();
  importDialogVisible.value = false;
  ElMessage.success("导入成功");
  proTableRef.value.getTableList();
};
const currentTreeType = ref<any>(0);

const handleNodeClick = (nodeObj: any) => {
  const initParam1: any = {};

  let currentNode = nodeObj;
  if (currentNode.level == 1) {
    currentTreeType.value = 1;
  } else if (currentNode.level == 2) {
    currentTreeType.value = 2;
  } else if (currentNode.level == 3) {
    currentTreeType.value = 3;
  } else if (currentNode.level == 4) {
    currentTreeType.value = 4;
  } else if (currentNode.level == 5) {
    currentTreeType.value = 5;
  }
  while (currentNode) {
    const nodeData = currentNode.data;
    if (nodeData && nodeData.type && nodeData.name) {
      // 以节点的type作为键，name作为值
      initParam1[nodeData.type] = nodeData.name;
    }
    currentNode = currentNode.parent;
  }

  const obj = clearObjectValues(initParam.value);
  initParam.value = { ...obj, ...initParam1 };
};
</script>

<style scoped>
:deep(.filter) {
  width: auto !important;
}
.custom-tree {
  width: 20%;
  padding: 10px;
  margin-right: 20px;
  overflow-y: auto;
  background-color: #fafafa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}
.custom-tree .el-tree-node__content {
  height: 32px;
  padding: 0 8px;
  font-size: 14px;
  line-height: 32px;
  color: #333333;
  border-radius: 4px;
  transition: background-color 0.3s;
}
.custom-tree .el-tree-node__content:hover {
  background-color: #eef1f6;
}
.custom-tree .el-tree-node.is-current > .el-tree-node__content {
  color: #ffffff;
  background-color: #409eff;
}
.custom-tree .el-tree-node__expand-icon {
  font-size: 12px;
  color: #909399;
}
.custom-tree .el-tree-node__expand-icon.expanded {
  transition: transform 0.3s;
  transform: rotate(90deg);
}
</style>
