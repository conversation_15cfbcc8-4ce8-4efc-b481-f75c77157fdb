<template>
  <div class="dashboard">
    <!-- 左上方气缸按钮 -->
    <div class="cylinder-button-container">
      <button
        v-for="(cylinder, index) in cylinders"
        :key="index"
        @click="selectCylinder(index)"
        :class="{ active: selectedCylinder === index }"
        :style="{ background: getCylinderColor(index) }"
      >
        气缸{{ index + 1 }}
        <span @click.stop="deleteCylinder(index)" class="delete-icon">&times;</span>
      </button>
      <button @click="addCylinder">添加气缸</button>
    </div>

    <!-- 右上角输入框 -->
    <div class="input-container">
      <input class="input-field" type="number" placeholder="退化值" v-model="degradationValue" />
      <input class="input-field" type="number" placeholder="告警值" v-model="alarmValue" />
    </div>

    <div class="content">
      <div class="info-panel">
        <div class="info-item">
          <p>品牌及型号: {{ deviceInfo.brand }}</p>
          <p>装机时间: {{ deviceInfo.installTime }}</p>
          <p>上次维护时间: {{ deviceInfo.lastMaintenance }}</p>
          <p>数据量: {{ deviceInfo.dataCount }}</p>
          <p>当前动作次数: {{ deviceInfo.currentActions }}</p>
          <p>剩余动作次数: {{ deviceInfo.remainingActions }}</p>
          <p>下次更换时间: {{ deviceInfo.nextReplacement }}</p>
          <p>最大值: {{ stats.maxValue }}ms</p>
          <p>最小值: {{ stats.minValue }}ms</p>
          <p>平均值: {{ stats.averageValue }}ms</p>
          <p>当前工单: {{ deviceInfo.currentOrder }}</p>
        </div>

        <!-- 按钮放在表格底部 -->
        <div class="button-container">
          <button @click="createMaintenanceOrder">创建维护工单</button>
          <button @click="checkOrderProgress">查询工单进度</button>
        </div>
      </div>

      <div class="chart-panel">
        <div class="chart-card">
          <h3>气缸{{ selectedCylinder + 1 }}动作时间</h3>
          <div ref="actionChartRef" class="chart"></div>
        </div>

        <div class="chart-card">
          <h3>压力表气压监控</h3>
          <div ref="pressureChartRef" class="chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import * as echarts from "echarts";

// 定义响应式数据
const deviceInfo = ref({
  brand: "ABCC",
  installTime: "2022-03-02 13:48",
  lastMaintenance: "2023-04-25 03:43",
  dataCount: 230,
  currentActions: 230,
  remainingActions: 8991,
  nextReplacement: "2022-08-15 17:47",
  currentOrder: "W_QG0113"
});
const stats = ref({
  maxValue: 800,
  minValue: 500,
  averageValue: 656
});
const actionChartRef = ref(null);
const pressureChartRef = ref(null);
const timer = ref(null);
const degradationValue = ref(null);
const alarmValue = ref(null);
const cylinders = ref([1, 2, 3, 4, 5]); // 5个气缸
const selectedCylinder = ref(0); // 默认选中第一个气缸
let actionChart = null;
let pressureChart = null;

// 初始化图表
const initCharts = () => {
  // 初始化气缸动作时间图表
  actionChart = echarts.init(actionChartRef.value);
  const actionOptions = {
    title: { text: `气缸${selectedCylinder.value + 1}动作时间`, subtext: "单位: ms", left: "center" },
    tooltip: { trigger: "axis" },
    xAxis: {
      type: "category",
      data: Array.from({ length: 50 }, (_, i) => `9:${String(i * 2).padStart(2, "0")}`)
    },
    yAxis: { type: "value", name: "时间 (ms)" },
    series: [
      {
        name: "动作时间",
        type: "line",
        data: Array.from({ length: 50 }, () => Math.floor(Math.random() * 300) + 500),
        lineStyle: { color: "#4CAF50" },
        areaStyle: { color: "#4CAF50" }
      }
    ]
  };
  actionChart.setOption(actionOptions);

  // 初始化压力表气压监控图表
  pressureChart = echarts.init(pressureChartRef.value);
  const pressureOptions = {
    title: { text: "压力表气压监控", subtext: "单位: Pa", left: "center" },
    tooltip: { trigger: "axis" },
    xAxis: {
      type: "category",
      data: Array.from({ length: 50 }, (_, i) => `9:${String(i * 2).padStart(2, "0")}`)
    },
    yAxis: { type: "value", name: "压力 (Pa)" },
    series: [
      {
        name: "气压",
        type: "line",
        data: Array.from({ length: 50 }, () => Math.floor(Math.random() * 60) + 120),
        lineStyle: { color: "#2196F3" },
        areaStyle: { color: "#2196F3" }
      }
    ]
  };
  pressureChart.setOption(pressureOptions);
};

// 开始自动刷新
const startAutoRefresh = () => {
  timer.value = setInterval(() => {
    refreshData();
    refreshCharts();
  }, 5000); // 每5秒刷新一次
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
};

// 刷新数据
const refreshData = () => {
  // 更新设备信息和统计数据
  deviceInfo.value = {
    ...deviceInfo.value,
    dataCount: Math.floor(Math.random() * 1000),
    currentActions: Math.floor(Math.random() * 1000),
    remainingActions: Math.floor(Math.random() * 10000)
  };

  stats.value = {
    ...stats.value,
    maxValue: Math.floor(Math.random() * 1000),
    minValue: Math.floor(Math.random() * 500),
    averageValue: Math.floor(Math.random() * 800)
  };
};

// 刷新图表
const refreshCharts = () => {
  // 更新气缸动作时间图表数据
  const newData = Array.from({ length: 50 }, () => Math.floor(Math.random() * 300) + 500);
  actionChart.setOption({
    title: { text: `气缸${selectedCylinder.value + 1}动作时间` },
    series: [{ data: newData }]
  });

  // 更新压力表气压监控图表数据
  const pressureData = Array.from({ length: 50 }, () => Math.floor(Math.random() * 60) + 120);
  pressureChart.setOption({ series: [{ data: pressureData }] });
};

// 调整图表大小
const resizeCharts = () => {
  if (actionChart) actionChart.resize();
  if (pressureChart) pressureChart.resize();
};

// 创建维护工单
const createMaintenanceOrder = () => {
  alert("创建维护工单功能尚未实现");
};

// 查询工单进度
const checkOrderProgress = () => {
  alert("查询工单进度功能尚未实现");
};

// 选择气缸
const selectCylinder = index => {
  selectedCylinder.value = index;
  refreshCharts(); // 切换气缸时刷新图表
};

// 获取气缸颜色
const getCylinderColor = index => {
  const colors = ["#FF5733", "#33FF57", "#5733FF", "#FF33E0", "#33E0FF"];
  return colors[index % colors.length];
};

// 添加气缸
const addCylinder = () => {
  const newCylinderIndex = cylinders.value.length + 1;
  cylinders.value.push(newCylinderIndex);
};

// 删除气缸
const deleteCylinder = index => {
  if (cylinders.value.length > 1) {
    cylinders.value.splice(index, 1);
    if (selectedCylinder.value === index) {
      selectedCylinder.value = 0;
    } else if (selectedCylinder.value > index) {
      selectedCylinder.value--;
    }
    refreshCharts();
  }
};

// 生命周期钩子
onMounted(() => {
  initCharts();
  startAutoRefresh();
  window.addEventListener("resize", resizeCharts);
});

onBeforeUnmount(() => {
  stopAutoRefresh();
  window.removeEventListener("resize", resizeCharts);
});

// 监听 selectedCylinder 变化
watch(selectedCylinder, () => {
  refreshCharts();
});
</script>

<style scoped>
.dashboard {
  box-sizing: border-box; /* 避免边框和内边距影响宽度 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%; /* 使用百分比宽度 */
  max-width: 1200px;
  height: 100vh;
  padding: 20px;
  margin: 0 auto;
  background: #ffffff; /* 光亮背景 */
  border-radius: 10px;
  box-shadow: 0 4px 8px rgb(0 0 0 / 20%);
}
.cylinder-button-container {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}
.cylinder-button-container button {
  position: relative;
  padding: 10px 20px;
  color: #ffffff;
  cursor: pointer;
  border: none;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 20%);
  transition:
    transform 0.3s,
    box-shadow 0.3s;
}
.cylinder-button-container button.active {
  box-shadow: 0 4px 8px rgb(0 0 0 / 30%);
  transform: translateY(-2px);
}
.cylinder-button-container button .delete-icon {
  position: absolute;
  top: 0;
  right: 5px;
  cursor: pointer;
}
.input-container {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  width: 100%; /* 使用百分比宽度 */
  max-width: 1200px;
  margin-bottom: 20px;
}
.input-field {
  width: 100px;
  padding: 8px;
  margin: 0 5px;
  color: #333333; /* 输入框文字颜色 */
  text-align: center;
  background: #f0f0f0; /* 输入框背景颜色 */
  border: 1px solid #cccccc;
  border-radius: 5px;
  outline: none;
}
.content {
  display: flex;
  width: 100%; /* 使用百分比宽度 */
  height: calc(100% - 150px); /* 减去头部和按钮的高度 */
}
.info-panel {
  box-sizing: border-box; /* 避免边框和内边距影响宽度 */
  display: flex;
  flex: 1;
  flex-direction: column; /* 垂直布局 */
  padding: 20px;
  overflow-y: auto;
  color: #333333; /* 信息面板文字颜色 */
  background: #f0f0f0; /* 信息面板背景颜色 */
  border-radius: 10px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}
.info-item {
  margin-bottom: 10px;
}
.button-container {
  display: flex;
  gap: 20px; /* 按钮间距 */
  justify-content: center; /* 按钮居中对齐 */
  margin-top: auto; /* 将按钮推至底部 */
}
.chart-panel {
  box-sizing: border-box; /* 避免边框和内边距影响宽度 */
  display: flex;
  flex: 2;
  flex-direction: column;
  gap: 20px;
  width: 100%; /* 使用百分比宽度 */
  margin-left: 20px;
}
.chart-card {
  box-sizing: border-box; /* 避免边框和内边距影响宽度 */
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: space-between;
  padding: 20px;
  background: #f0f0f0; /* 图表卡片背景颜色 */
  border-radius: 10px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
}
.chart {
  flex: 1;
  width: 100%; /* 使用百分比宽度 */
  height: 100%;
}
button {
  padding: 10px 20px;
  margin: 5px;
  color: #ffffff;
  cursor: pointer;
  background: #4caf50;
  border: none;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 20%);
  transition:
    transform 0.3s,
    box-shadow 0.3s;
}
button:hover {
  background: #45a049;
  box-shadow: 0 4px 8px rgb(0 0 0 / 30%);
  transform: translateY(-2px);
}
button:active {
  background: #3e8e41;
  box-shadow: 0 2px 4px rgb(0 0 0 / 20%);
  transform: translateY(0);
}
</style>
