<script setup lang="ts">
import { reactive, defineEmits, watch } from "vue";
import dayjs from "dayjs";
import type { DateDataType } from "./index.d";
import { useI18n } from "vue-i18n";
// import {useSettingStore} from "@/stores/index"

const emits = defineEmits(["export-data"]);
const { locale } = useI18n();

const dateData = reactive<DateDataType>({
  dateDay: "",
  dateYear: "",
  dateWeek: "",
  timing: null as unknown as NodeJS.Timer
});

// const { setSettingShow} =useSettingStore()
// const weekday = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
const weekdays = {
  zh: ["周日", "周一", "周二", "周三", "周四", "周五", "周六"],
  en: ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
};

const getWeekday = (day: number) => {
  return weekdays[locale.value as keyof typeof weekdays][day];
};

const timeFn = () => {
  dateData.timing = setInterval(() => {
    dateData.dateDay = dayjs().format("YYYY-MM-DD hh : mm : ss");
    dateData.dateWeek = getWeekday(dayjs().day());
  }, 1000);
};

// 监听语言变化
watch(locale, () => {
  dateData.dateWeek = getWeekday(dayjs().day());
});

timeFn();
const exportAll = () => {
  // eslint-disable-next-line vue/custom-event-name-casing
  emits("export-data");
};
</script>

<template>
  <div class="d-flex jc-center title_wrap">
    <div class="zuojuxing"></div>
    <div class="youjuxing"></div>
    <div class="guang"></div>
    <div class="d-flex jc-center">
      <div class="title">
        <span class="title-text">Fast-Care</span>
      </div>
    </div>
    <div class="timers">
      <svg @click="exportAll" class="h-6 w-6 cursor-pointer m-r-[10px]" viewBox="0 0 1024 1024" fill="white">
        <path
          d="M665.38 65.024c11.48 0 22.53 4.46 30.72 12.58l0.44 0.37 152.65 153.6c8.05 8.12 12.65 19.02 12.8 30.43v250.51a13.75 13.75 0 0 1-13.68 13.75h-28.6a13.75 13.75 0 0 1-13.75-13.75v-245.03L660.48 121.05H216.94v199.02h269.24c7.61 0 13.75 6.14 13.75 13.75v420.57a13.68 13.68 0 0 1-13.75 13.68H217.01v136.05h589.02v-24.72c0-7.61 6.14-13.75 13.68-13.75h28.53c7.61 0 13.75 6.14 13.75 13.75v44.62a35.99 35.99 0 0 1-35.33 36.06h-629.76a35.99 35.99 0 0 1-35.84-35.4V768h-83.38a13.68 13.68 0 0 1-13.68-13.75v-420.57c0-7.53 6.14-13.68 13.75-13.68H160.91V101.01c0-19.68 15.73-35.69 35.33-35.99h469.07zM361.33 437.98a54.86 54.86 0 0 0-42.13 19.53l-37.3 44.47-37.3-44.4a54.86 54.86 0 0 0-41.98-19.6h-30.13a6.88 6.88 0 0 0-5.27 11.26l79.51 94.72-79.51 94.72a6.88 6.88 0 0 0 5.27 11.26h30.28a54.86 54.86 0 0 0 41.98-19.6l37.16-44.32 37.3 44.32a54.86 54.86 0 0 0 41.98 19.6h30.21c5.85 0 9-6.8 5.27-11.26L317.22 543.96l79.51-94.72a6.88 6.88 0 0 0-5.19-11.26zm214.6-104.3c0-7.53 6.14-13.68 13.75-13.68h164.57c7.53 0 13.68 6.14 13.68 13.75v28.53a13.68 13.68 0 0 1-13.75 13.75h-164.57a13.68 13.68 0 0 1-13.68-13.75v-28.53zm13.75 102.33a13.68 13.68 0 0 0-13.75 13.68v28.6c0 7.61 6.14 13.75 13.75 13.75h164.57a13.68 13.68 0 0 0 13.68-13.75v-28.53a13.68 13.68 0 0 0-13.75-13.75h-164.57zm192 348.23a21.21 21.21 0 0 0-8.19 16.82v48.64c0 4.39 5.12 6.88 8.56 4.17l168.67-130.78a24.5 24.5 0 0 0 0-38.62L782.19 553.69a5.27 5.27 0 0 0-8.56 4.24v48.71c0 6.58 3 12.73 8.19 16.82l68.53 53.03H617.25a10.61 10.61 0 0 0-10.53 10.61v33.94c0 5.85 4.68 10.61 10.53 10.61h232.45l-67.95 52.66z"
        />
      </svg>
      {{ dateData.dateYear }} {{ dateData.dateWeek }} {{ dateData.dateDay }}

      <!-- <div class="setting_icon"   @click="setSettingShow(true)">
          <img src="@/assets/img/headers/setting.png" alt="设置">
      </div> -->
    </div>
  </div>
</template>

<style scoped lang="scss">
.title_wrap {
  position: relative;
  height: 60px;
  margin-bottom: 4px;
  background-image: url("../../../assets/img/top.png");
  background-position: center center;
  background-size: cover;
  .guang {
    position: absolute;
    bottom: -26px;
    width: 100%;
    height: 56px;
    background-image: url("../../../assets/img/guang.png");
    background-position: 80px center;
  }
  .zuojuxing,
  .youjuxing {
    position: absolute;
    top: -2px;
    width: 140px;
    height: 6px;
    background-image: url("../../../assets/img/headers/juxing1.png");
  }
  .zuojuxing {
    left: 11%;
  }
  .youjuxing {
    right: 11%;
    transform: rotate(180deg);
  }
  .timers {
    position: absolute;
    top: 30px;
    right: 0;
    display: flex;
    align-items: center;
    font-size: 18px;
    .setting_icon {
      width: 20px;
      height: 20px;
      margin-left: 12px;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
.title {
  position: relative;
  height: 60px;
  line-height: 46px;
  color: transparent;

  // width: 500px;
  text-align: center;
  background-size: cover;
  .title-text {
    width: 100%;
    font-size: 38px;
    font-weight: 900;
    letter-spacing: 6px;
    background: linear-gradient(92deg, #0072ff 0%, #00eaff 48.8525390625%, #01aaff 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
</style>
