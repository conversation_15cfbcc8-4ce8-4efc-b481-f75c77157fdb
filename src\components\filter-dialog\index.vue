<template>
  <div class="filter-container">
    <!-- 筛选按钮 -->
    <el-button type="primary" @click="dialogVisible = true" class="filter-btn"> 筛选 </el-button>

    <!-- 筛选弹框 -->
    <el-dialog v-model="dialogVisible" :title="title" width="40%" class="filter-dialog" @close="onClose">
      <div class="filter-content">
        <!-- 机台选择 -->
        <div v-if="showMachineFilter" class="filter-section">
          <div class="filter-label">选择机台：</div>
          <el-select v-model="selectedMachine" placeholder="请选择机台" clearable style="width: 240px" size="large">
            <el-option v-for="item in machineOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>

        <!-- 时间选择 -->
        <div v-if="showTimeFilter" class="filter-section">
          <div class="filter-label">选择时间：</div>
          <el-date-picker v-model="selectedTime" type="date" placeholder="选择日期" value-format="YYYY-MM-DD" style="width: 100%" />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="clearFilters">清空筛选</el-button>
          <el-button type="primary" @click="applyFilters">应用筛选</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from "vue";
import { ElDialog, ElSelect, ElOption, ElDatePicker, ElButton } from "element-plus";

const props = defineProps({
  title: {
    type: String,
    default: "筛选条件"
  },
  machineOptions: {
    type: Array,
    default: () => []
  },
  modelValue: {
    type: Object,
    default: () => ({ machine: "", time: "" })
  },
  showMachineFilter: {
    type: Boolean,
    default: true // 默认显示机台筛选
  },
  showTimeFilter: {
    type: Boolean,
    default: true // 默认显示时间筛选
  }
});

const emit = defineEmits(["update:modelValue", "apply-filters", "clear-filters"]);

const dialogVisible = ref(false); // 控制弹框显示
const selectedMachine = ref(props.modelValue.machine); // 选中的机台
const selectedTime = ref(props.modelValue.time); // 选中的时间

// 监听外部传入的 modelValue，同步到内部状态
watch(
  () => props.modelValue,
  newVal => {
    selectedMachine.value = newVal.machine;
    selectedTime.value = newVal.time;
  },
  { deep: true }
);

// 清空筛选条件
const clearFilters = () => {
  selectedMachine.value = "";
  selectedTime.value = "";
  emit("update:modelValue", { machine: "", time: "" }); // 同步到父组件
  // eslint-disable-next-line vue/custom-event-name-casing
  emit("clear-filters"); // 通知父组件清空筛选条件
};

// 应用筛选条件
const applyFilters = () => {
  emit("update:modelValue", { machine: selectedMachine.value, time: selectedTime.value }); // 同步到父组件
  // eslint-disable-next-line vue/custom-event-name-casing
  emit("apply-filters", { machine: selectedMachine.value, time: selectedTime.value }); // 通知父组件应用筛选条件
  dialogVisible.value = false; // 关闭弹框
};

// 弹框关闭时触发
const onClose = () => {
  emit("update:modelValue", { machine: selectedMachine.value, time: selectedTime.value }); // 同步到父组件
};
</script>

<style scoped lang="scss">
.filter-container {
  display: inline-block;
}
.filter-btn {
  margin-bottom: 16px;
}
.filter-dialog {
  .filter-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  .filter-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  .filter-label {
    font-size: 14px;
    font-weight: 500;
  }
  .dialog-footer {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
  }
}
</style>
