/**
 * @description  全局主题 hooks
 * @license Apache License Version 2.0
 * @Copyright (c) 2022-Now 少林寺驻北固山办事处大神父王喇嘛
 * @remarks
 * SimpleAdmin 基于 Apache License Version 2.0 协议发布，可用于商业项目，但必须遵守以下补充条款:
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改SimpleAdmin源码头部的版权声明。
 * 3.分发源码时候，请注明软件出处 https://gitee.com/dotnetmoyu/SimpleAdmin
 * 4.基于本软件的作品，只能使用 SimpleAdmin 作为后台服务，除外情况不可商用且不允许二次分发或开源。
 * 5.请不得将本软件应用于危害国家安全、荣誉和利益的行为，不能以任何形式用于非法为目的的行为不要删除和修改作者声明。
 * 6.任何基于本软件而产生的一切法律纠纷和责任，均于我司无关
 * @see https://gitee.com/dotnetmoyu/SimpleAdmin
 */
import { storeToRefs } from "pinia";
import { Theme } from "./interface";
import { ElMessage } from "element-plus";
import { DEFAULT_PRIMARY } from "@/config";
import { useGlobalStore } from "@/stores/modules/global";
import { getLightColor, getDarkColor } from "@/utils/color";
import { menuTheme } from "@/styles/theme/menu";
import { asideTheme } from "@/styles/theme/aside";
import { headerTheme } from "@/styles/theme/header";

/**
 * @description 全局主题 hooks
 * */
export const useTheme = () => {
  const globalStore = useGlobalStore();
  const { primary, isDark, isGrey, isWeak, layout, asideInverted, headerInverted } = storeToRefs(globalStore);

  // 切换暗黑模式 ==> 同时修改主题颜色、侧边栏、头部颜色
  const switchDark = () => {
    const html = document.documentElement as HTMLElement;
    if (isDark.value) {
      html.setAttribute("class", "dark");
      changePrimary1(primary.value);
    } else {
      html.setAttribute("class", "");
      changePrimary(primary.value);
      setAsideTheme();
      setHeaderTheme();
      // location.reload();
    }
    // location.reload();
  };
  // function setCssVariables() {
  //   const cssVariables = {
  //     "--el-bg-color-page": "#f2f3f5",
  //     "--el-bg-color": "#ffffff",
  //     "--el-bg-color-overlay": "#ffffff"
  //     // "--el-text-color-primary": "#303133",
  //     // "--el-text-color-regular": "#606266",
  //     // "--el-text-color-secondary": "#909399",
  //     // "--el-text-color-placeholder": "#a8abb2",
  //     // "--el-border-color-darker": "#cdd0d6",
  //     // "--el-border-color-dark": "#d4d7de",
  //     // "--el-border-color": "#dcdfe6",
  //     // "--el-border-color-light": "#e4e7ed",
  //     // "--el-border-color-lighter": "#ebeef5",
  //     // "--el-fill-color-darker": "#e6e8eb",
  //     // "--el-fill-color-dark": "#ebedf0",
  //     // "--el-fill-color": "#f0f2f5",
  //     // "--el-fill-color-light": "#f5f7fa",
  //     // "--el-fill-color-lighter": "#fafafa",
  //     // "--el-fill-color-blank": "#ffffff",
  //     // "--el-button-disabled-text-color": "",
  //     // "--el-card-bg-color": "",
  //     // "--el-box-shadow": "0px 12px 32px 4px rgba(0,0,0,0.04),0px 8px 20px rgba(0,0,0,0.08)",
  //     // "--el-box-shadow-light": "0px 0px 12px rgba(0,0,0,0.12)"
  //   };

  //   for (const [variable, value] of Object.entries(cssVariables)) {
  //     if (value) {
  //       document.documentElement.style.setProperty(variable, value);
  //     }
  //   }
  // }

  // 调用函数来设置 CSS 变量

  const changePrimary = (val: string | null) => {
    // changePrimary1(primary.value);
    // return;
    if (isDark.value) {
      changePrimary1(primary.value);
      return;
    }
    if (!val) {
      val = DEFAULT_PRIMARY;
      ElMessage({ type: "success", message: `主题颜色已重置为 ${DEFAULT_PRIMARY}` });
    }
    // 计算主题颜色变化
    document.documentElement.style.setProperty("--el-color-primary", val);
    document.documentElement.style.setProperty(
      "--el-color-primary-dark-2",
      isDark.value ? `${getLightColor(val, 0.2)}` : `${getDarkColor(val, 0.3)}`
    );
    for (let i = 1; i <= 9; i++) {
      const primaryColor = isDark.value ? `${getDarkColor(val, i / 10)}` : `${getLightColor(val, i / 10)}`;
      document.documentElement.style.setProperty(`--el-color-primary-light-${i}`, primaryColor);
    }
    globalStore.setGlobalState("primary", val);
  };
  // 修改主题颜色
  const changePrimary1 = (val: string | null) => {
    if (!val) {
      val = DEFAULT_PRIMARY;
      ElMessage({ type: "success", message: `主题颜色已重置为 ${DEFAULT_PRIMARY}` });
    }

    // 辅助函数：处理小数并确保 RGB 值合法
    const sanitizeRGB = (color: string) => color.replace(/(\d+\.\d+)/g, (_, d) => Math.round(parseFloat(d)).toString());

    // 核心颜色生成逻辑（暗色模式反向操作）
    const generateShade = (ratio: number, opacity?: number) => {
      let color = !isDark.value
        ? getLightColor(val, ratio) // 暗色模式生成亮色
        : getDarkColor(val, ratio); // 亮色模式生成暗色

      color = sanitizeRGB(color);
      return opacity ? `${color}${Math.round(opacity * 255).toString(16)}` : color;
    };
    document.documentElement.style.setProperty("--el-color-primary", val);
    // 设置主色及其衍生色
    document.documentElement.style.setProperty(
      "--el-color-primary-dark-2",
      isDark.value ? `${getLightColor(val, 0.2)}` : `${getDarkColor(val, 0.3)}`
    );
    for (let i = 1; i <= 9; i++) {
      const primaryColor = isDark.value ? `${getDarkColor(val, i / 10)}` : `${getLightColor(val, i / 10)}`;
      document.documentElement.style.setProperty(`--el-color-primary-light-${i}`, primaryColor);
    }

    /* ========== 全局布局 ========== */
    // 头部
    document.documentElement.style.setProperty("--el-header-logo-text-color", val);
    document.documentElement.style.setProperty("--el-header-bg-color", generateShade(0.93));
    document.documentElement.style.setProperty("--el-header-text-color", generateShade(0.2));
    document.documentElement.style.setProperty("--el-header-text-color-regular", generateShade(0.4));
    document.documentElement.style.setProperty("--el-header-border-color", generateShade(0.85, 0.5));

    // 侧边栏
    document.documentElement.style.setProperty("--el-aside-logo-text-color", generateShade(0.1));
    document.documentElement.style.setProperty("--el-aside-border-color", generateShade(0.9));

    // 菜单
    document.documentElement.style.setProperty("--el-menu-bg-color", generateShade(0.93));
    document.documentElement.style.setProperty("--el-menu-hover-bg-color", generateShade(0.7));
    document.documentElement.style.setProperty("--el-menu-active-bg-color", generateShade(0.5, 0.3));
    document.documentElement.style.setProperty("--el-menu-text-color", "#e5eaf3");
    document.documentElement.style.setProperty("--el-menu-active-color", val);
    document.documentElement.style.setProperty("--el-menu-hover-text-color", generateShade(0.1));

    /* ========== 全局基础变量 ========== */
    // 背景色
    document.documentElement.style.setProperty("--el-bg-color-page", generateShade(0.93));
    document.documentElement.style.setProperty("--el-bg-color", generateShade(0.93));
    document.documentElement.style.setProperty("--el-bg-color-overlay", generateShade(0.95));

    document.documentElement.style.setProperty("--el-text-color-primary", "#e5eaf3");
    document.documentElement.style.setProperty("--el-text-color-regular", "#cfd3dc");
    document.documentElement.style.setProperty("--el-text-color-secondary", "#a3a6ad");
    document.documentElement.style.setProperty("--el-text-color-placeholder", "#8d9095");
    // 边框色
    const borderBase = generateShade(0.85);
    document.documentElement.style.setProperty("--el-border-color-darker", borderBase);
    document.documentElement.style.setProperty("--el-border-color-dark", generateShade(0.8));
    document.documentElement.style.setProperty("--el-border-color", generateShade(0.75));
    document.documentElement.style.setProperty("--el-border-color-light", generateShade(0.6));
    document.documentElement.style.setProperty("--el-border-color-lighter", generateShade(0.65)); // 填充色
    document.documentElement.style.setProperty("--el-fill-color-darker", generateShade(0.9));
    document.documentElement.style.setProperty("--el-fill-color-dark", generateShade(0.85));
    document.documentElement.style.setProperty("--el-fill-color", generateShade(0.8));
    document.documentElement.style.setProperty("--el-fill-color-light", generateShade(0.75));
    document.documentElement.style.setProperty("--el-fill-color-lighter", generateShade(0.7));
    document.documentElement.style.setProperty("--el-fill-color-blank", "transparent");
    /* ========== 组件 ========== */
    // 按钮
    document.documentElement.style.setProperty("--el-button-disabled-text-color", `${generateShade(0.5)}80`); // 80 表示 50% 透明度

    // 卡片
    document.documentElement.style.setProperty(
      "--el-card-bg-color",
      getComputedStyle(document.documentElement).getPropertyValue("--el-bg-color-overlay")
    );

    // 阴影
    // const shadowColor = isDark.value ? generateShade(0.3) : generateShade(0.7);
    // document.documentElement.style.setProperty("--el-box-shadow", `0px 12px 32px 4px ${shadowColor}5C, 0px 8px 20px ${shadowColor}4D`);
    // document.documentElement.style.setProperty("--el-box-shadow-light", `0px 0px 12px ${shadowColor}33`);

    globalStore.setGlobalState("primary", val);
  };

  // 灰色和弱色切换
  const changeGreyOrWeak = (type: Theme.GreyOrWeakType, value: boolean) => {
    const body = document.body as HTMLElement;
    if (!value) return body.removeAttribute("style");
    const styles: Record<Theme.GreyOrWeakType, string> = {
      grey: "filter: grayscale(1)",
      weak: "filter: invert(80%)"
    };
    body.setAttribute("style", styles[type]);
    const propName = type === "grey" ? "isWeak" : "isGrey";
    globalStore.setGlobalState(propName, false);
  };

  // 设置菜单样式
  const setMenuTheme = () => {
    let type: Theme.ThemeType = "light";
    if (layout.value === "transverse" && headerInverted.value) type = "inverted";
    if (layout.value !== "transverse" && asideInverted.value) type = "inverted";
    if (isDark.value) type = "dark";
    const theme = menuTheme[type!];
    for (const [key, value] of Object.entries(theme)) {
      document.documentElement.style.setProperty(key, value);
    }
  };

  // 设置侧边栏样式
  const setAsideTheme = () => {
    let type: Theme.ThemeType = "light";
    if (asideInverted.value) type = "inverted";
    if (isDark.value) type = "dark";
    const theme = asideTheme[type!];
    for (const [key, value] of Object.entries(theme)) {
      document.documentElement.style.setProperty(key, value);
    }
    setMenuTheme();
  };

  // 设置头部样式
  const setHeaderTheme = () => {
    let type: Theme.ThemeType = "light";
    if (headerInverted.value) type = "inverted";
    if (isDark.value) type = "dark";
    const theme = headerTheme[type!];
    for (const [key, value] of Object.entries(theme)) {
      document.documentElement.style.setProperty(key, value);
    }
    setMenuTheme();
  };

  // init theme
  const initTheme = () => {
    switchDark();
    if (isGrey.value) changeGreyOrWeak("grey", true);
    if (isWeak.value) changeGreyOrWeak("weak", true);
  };

  return {
    initTheme,
    switchDark,
    changePrimary,
    changeGreyOrWeak,
    setAsideTheme,
    setHeaderTheme
  };
};
