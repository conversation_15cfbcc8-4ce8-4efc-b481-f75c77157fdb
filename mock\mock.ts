function UnifyContextTake() {
  return {
    // 这里可以添加额外的上下文信息
    context: "Mock context"
  };
}

// 定义一个通用的模拟响应函数
function mockResponse(data) {
  return {
    code: 200,
    msg: "请求成功",
    data: data,
    extras: UnifyContextTake(),
    time: new Date().toISOString()
  };
}
// {
//   url: "/api/user/real-info",
//   method: "get",
//   // 添加忽略标记
//   ignore: true, // 自定义标记
//   response: () => ({
//     /* 这个响应永远不会被使用 */
//   })
// }
// src/mock/user.js
// 新增对 getTree API 的 Mock
const treeData = [
  {
    name: "衢州极电",
    type: "fact",
    children: [
      {
        name: "极电二期",
        type: "wshop",
        children: [
          {
            name: "L5",
            type: "prodLine",
            children: [
              {
                name: "叠片",
                type: "mach",
                children: [
                  {
                    name: "整机",
                    type: "wstation",
                    children: []
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  }
];
export default [
  {
    url: "/api/user/shit",
    method: "get",
    response: () => {
      const data = {
        message: "这是成功响应的数据"
      };
      return mockResponse(data);
      /* 正常模拟数据 */
    }
  },
  {
    url: "/api/sys/mes/getTree",
    method: "get",
    response: () => {
      const data = treeData;
      return mockResponse(data);
      /* 正常模拟数据 */
    }
  }
];
