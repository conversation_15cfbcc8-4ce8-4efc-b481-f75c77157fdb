<template>
  <div class="workshop-status-panel">
    <div class="workshop" v-for="(shop, index) in shops" :key="index">
      <div class="header">
        <span class="name">{{ shop.name }}</span>
        <router-link to="/detail">
          <a href="#" class="details-link">[详情]</a>
        </router-link>
      </div>
      <div class="status">
        <div class="status-item">
          <div class="item1" style="display: flex; flex-direction: column; width: 100px">
            <span class="label">机台总数</span>
            <span class="value" style="font-size: 45px">{{ shop.total }}</span>
          </div>
          <div class="item2" style="display: flex; flex: 1; flex-wrap: wrap">
            <div class="status-item1">
              <span class="value" style="color: antiquewhite">{{ shop.working }}</span>
              <span class="label">作业</span>
            </div>
            <div class="status-item1">
              <span class="value" style="color: blueviolet">{{ shop.waiting }}</span>
              <span class="label">等待</span>
            </div>
            <div class="status-item1">
              <span class="value" style="color: brown">{{ shop.error }}</span>
              <span class="label">故障</span>
            </div>
            <div class="status-item1">
              <span class="value" style="color: cadetblue">{{ shop.stop }}</span>
              <span class="label">停机</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive } from "vue";

const shops = reactive([
  {
    name: "贴片车间",
    total: 45,
    working: 32,
    waiting: 8,
    error: 4,
    stop: 0
  },
  {
    name: "封装车间",
    total: 45,
    working: 32,
    waiting: 8,
    error: 4,
    stop: 0
  },
  {
    name: "焊接车间",
    total: 45,
    working: 32,
    waiting: 8,
    error: 4,
    stop: 0
  },
  {
    name: "贴片车间",
    total: 45,
    working: 32,
    waiting: 8,
    error: 4,
    stop: 0
  },
  {
    name: "贴片车间",
    total: 45,
    working: 32,
    waiting: 8,
    error: 4,
    stop: 0
  }
]);
</script>

<style scoped lang="scss">
.workshop-status-panel {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px 0;
  color: white;
  border-radius: 8px;
}
.workshop {
  flex: 1 1 200px;
  padding: 10px;
  margin-bottom: 20px;
  background-color: rgb(255 255 255 / 10%);
  border-radius: 8px;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .name {
      font-size: 20px;
      font-weight: bold;
      color: grey;
    }
    .details-link {
      font-size: 14px;
      color: #00ff00;
      text-decoration: none;
    }
  }
  .status {
    display: flex;
    flex-direction: column;
    .status-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      .status-item1 {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        width: 50%;
      }
      .label {
        font-size: 16px;
        color: grey;
      }
      .value {
        font-size: 24px;
        font-weight: bold;
      }
    }
  }
}
</style>
