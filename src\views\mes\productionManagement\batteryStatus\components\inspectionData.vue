<template>
  <div class="inspection-data-container">
    <el-empty v-if="!hasData" description="请输入查询内容进行查询" />
    <div v-else class="info-content">
      <el-tabs v-model="activeInspectionTab">
        <el-tab-pane v-for="item in inspectionTypes" :key="item.type" :label="item.name" :name="item.type">
          <el-table :data="getInspectionData(item.type)" border style="width: 100%">
            <el-table-column prop="itemName" label="检验项目" width="180" />
            <el-table-column prop="value" label="检验值" width="150" />
            <el-table-column prop="unit" label="单位" width="100" />
            <el-table-column prop="standard" label="标准" width="200" />
            <el-table-column prop="result" label="结果" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.result === 'PASS' ? 'success' : 'danger'">
                  {{ scope.row.result === "PASS" ? "合格" : "不合格" }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="inspectionTime" label="检验时间" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts" name="inspectionData">
import { ref, watch } from "vue";

// 定义组件属性
const props = defineProps({
  queryParams: {
    type: Object,
    required: true
  }
});

// 定义检验数据接口
interface InspectionItem {
  type: string;
  itemName: string;
  value: string | number;
  unit: string;
  standard: string;
  result: string;
  inspectionTime: string;
}

// 是否有数据标志
const hasData = ref(false);

// 检验数据列表
const inspectionList = ref<InspectionItem[]>([]);

// 检验类型
const inspectionTypes = [
  { type: "dimension", name: "尺寸检验" },
  { type: "electrical", name: "电性能检验" },
  { type: "appearance", name: "外观检验" }
];

// 当前选中的检验类型标签
const activeInspectionTab = ref("dimension");

// 监听查询参数变化
watch(
  () => props.queryParams,
  newVal => {
    if (newVal.queryValue) {
      fetchInspectionData(newVal.queryType, newVal.queryValue);
    }
  },
  { deep: true }
);

/**
 * 获取电芯检验数据
 */
function fetchInspectionData(queryType: string, queryValue: string) {
  // TODO: 替换为实际API调用
  console.log("查询检验数据:", queryType, queryValue);

  // 模拟数据，实际项目中应替换为API请求
  setTimeout(() => {
    inspectionList.value = [
      {
        type: "dimension",
        itemName: "长度",
        value: 65.2,
        unit: "mm",
        standard: "65.0±0.5mm",
        result: "PASS",
        inspectionTime: "2025-06-10 10:00:00"
      },
      {
        type: "dimension",
        itemName: "宽度",
        value: 32.1,
        unit: "mm",
        standard: "32.0±0.3mm",
        result: "PASS",
        inspectionTime: "2025-06-10 10:00:00"
      },
      {
        type: "dimension",
        itemName: "厚度",
        value: 3.8,
        unit: "mm",
        standard: "3.8±0.2mm",
        result: "PASS",
        inspectionTime: "2025-06-10 10:00:00"
      },
      {
        type: "electrical",
        itemName: "电压",
        value: 3.7,
        unit: "V",
        standard: "3.7±0.1V",
        result: "PASS",
        inspectionTime: "2025-06-10 10:30:00"
      },
      {
        type: "electrical",
        itemName: "内阻",
        value: 28,
        unit: "mΩ",
        standard: "≤30mΩ",
        result: "PASS",
        inspectionTime: "2025-06-10 10:30:00"
      },
      {
        type: "appearance",
        itemName: "外观",
        value: "正常",
        unit: "",
        standard: "无划痕、无变形",
        result: "PASS",
        inspectionTime: "2025-06-10 11:00:00"
      }
    ];
    hasData.value = true;
  }, 500);
}

/**
 * 根据检验类型获取对应的检验数据
 */
function getInspectionData(type: string) {
  return inspectionList.value.filter(item => item.type === type);
}
</script>

<style lang="scss" scoped>
.inspection-data-container {
  height: 100%;
  padding: 20px;
  .info-content {
    margin-top: 20px;
  }
}
</style>
