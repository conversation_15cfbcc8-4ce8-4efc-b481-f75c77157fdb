<template>
  <v-chart :autoresize="true" :option="options" class="chart card" ref="chartRef" />
</template>

<script lang="ts" setup>
import { transformChart, exportMultipleTablesToExcel } from "@/utils";
import moment from "moment";
import { ref, defineProps, computed } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
  machineData: {
    type: Array,
    default: () => []
  }
});

const chartRef = ref();
const exportExcel = () => {
  const arr1 = transformChart(chartRef.value, t("statusMonitoring.currentStatus.machine")).map((item: any, index) => {
    if (index == 0) {
      return [t("statusMonitoring.currentStatus.machine"), t("statusMonitoring.currentStatus.currentStatus")];
    } else {
      return [item[0], item[1].status];
    }
  });
  // 获取当前时间并格式化
  const currentTime = moment().format("YYYY-MM-DD_HH-mm-ss");
  // 将当前时间拼接到文件名中
  const fileName = `${t("statusMonitoring.currentStatus.machineStatus")}_${currentTime}.xlsx`;
  exportMultipleTablesToExcel([arr1], [t("statusMonitoring.currentStatus.machineStatus")], fileName);
};

const generateOptions = (machineData: any[]) => {
  // 每个机台只保留一个状态
  const uniqueMachineData = Array.from(new Map(machineData.map(item => [item.machine, item])).values());

  // 生成唯一状态列表和颜色映射
  const uniqueStatus = [...new Set(uniqueMachineData.map(item => item.machinestatuss))];
  const statusColor = [
    "#409EFF",
    "#67C23A",
    "#E6A23C",
    "#909399",
    "#F56C6C",
    "#2F4056",
    "#C0504D",
    "#93B7BE",
    "#8563E6",
    "#4FC08D",
    "#F8971C",
    "#26A69A",
    "#E84393",
    "#20A0FF",
    "#9575CD"
  ];

  // 生成每个机台对应的状态数据
  const seriesData = uniqueMachineData.map(item => {
    const statusIndex = uniqueStatus.indexOf(item.machinestatuss);
    return {
      value: 10,
      itemStyle: {
        color: statusColor[statusIndex]
      },
      name: item.machine,
      status: item.machinestatuss
    };
  });

  const root = document.documentElement;
  const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");

  return {
    title: {
      text: t("statusMonitoring.currentStatus.realTime.title"),
      left: "center",
      textStyle: {
        color: chartColor
      }
    },
    tooltip: {
      trigger: "item",
      formatter: function (params) {
        return `${params.name}<br/>${t("statusMonitoring.currentStatus.realTime.status")}: ${params.data.status}`;
      },
      textStyle: {
        color: chartColor
      }
    },
    toolbox: {
      show: true,
      feature: {
        myExcelExport: {
          show: true,
          title: t("statusMonitoring.currentStatus.realTime.exportExcel"),
          icon: "path://M665.38 65.024c11.48 0 22.53 4.46 30.72 12.58l0.44 0.37 152.65 153.6c8.05 8.12 12.65 19.02 12.8 30.43v250.51a13.75 13.75 0 0 1-13.68 13.75h-28.6a13.75 13.75 0 0 1-13.75-13.75v-245.03L660.48 121.05H216.94v199.02h269.24c7.61 0 13.75 6.14 13.75 13.75v420.57a13.68 13.68 0 0 1-13.75 13.68H217.01v136.05h589.02v-24.72c0-7.61 6.14-13.75 13.68-13.75h28.53c7.61 0 13.75 6.14 13.75 13.75v44.62a35.99 35.99 0 0 1-35.33 36.06h-629.76a35.99 35.99 0 0 1-35.84-35.4V768h-83.38a13.68 13.68 0 0 1-13.68-13.75v-420.57c0-7.53 6.14-13.68 13.75-13.68H160.91V101.01c0-19.68 15.73-35.69 35.33-35.99h469.07zM361.33 437.98a54.86 54.86 0 0 0-42.13 19.53l-37.3 44.47-37.3-44.4a54.86 54.86 0 0 0-41.98-19.6h-30.13a6.88 6.88 0 0 0-5.27 11.26l79.51 94.72-79.51 94.72a6.88 6.88 0 0 0 5.27 11.26h30.28a54.86 54.86 0 0 0 41.98-19.6l37.16-44.32 37.3 44.32a54.86 54.86 0 0 0 41.98 19.6h30.21c5.85 0 9-6.8 5.27-11.26L317.22 543.96l79.51-94.72a6.88 6.88 0 0 0-5.19-11.26zm214.6-104.3c0-7.53 6.14-13.68 13.75-13.68h164.57c7.53 0 13.68 6.14 13.68 13.75v28.53a13.68 13.68 0 0 1-13.75 13.75h-164.57a13.68 13.68 0 0 1-13.68-13.75v-28.53zm13.75 102.33a13.68 13.68 0 0 0-13.75 13.68v28.6c0 7.61 6.14 13.75 13.75 13.75h164.57a13.68 13.68 0 0 0 13.68-13.75v-28.53a13.68 13.68 0 0 0-13.75-13.75h-164.57zm192 348.23a21.21 21.21 0 0 0-8.19 16.82v48.64c0 4.39 5.12 6.88 8.56 4.17l168.67-130.78a24.5 24.5 0 0 0 0-38.62L782.19 553.69a5.27 5.27 0 0 0-8.56 4.24v48.71c0 6.58 3 12.73 8.19 16.82l68.53 53.03H617.25a10.61 10.61 0 0 0-10.53 10.61v33.94c0 5.85 4.68 10.61 10.53 10.61h232.45l-67.95 52.66z",
          onclick: exportExcel
        }
      },
      iconStyle: {
        color: chartColor
      }
    },
    xAxis: {
      type: "category",
      data: uniqueMachineData.map(item => item.machine),
      axisLabel: {
        interval: 0,
        color: chartColor
      },
      axisLine: {
        lineStyle: {
          color: chartColor
        }
      },
      axisTick: {
        lineStyle: {
          color: chartColor
        }
      }
    },
    yAxis: {
      show: false, // 隐藏 y 轴
      axisLine: {
        show: false // 隐藏 y 轴轴线
      },
      axisTick: {
        show: false // 隐藏 y 轴刻度线
      },
      name: t("statusMonitoring.currentStatus.realTime.machineStatus"),
      type: "value",
      nameTextStyle: {
        color: chartColor
      }
    },
    series: [
      {
        type: "bar",
        data: seriesData,
        label: {
          show: true,
          position: "top",
          // 柱体标签格式为对应状态
          formatter: function (params) {
            return params.data.status;
          },
          color: chartColor
        }
      }
    ]
  };
};

const options = computed(() => generateOptions(props.machineData));

defineExpose({
  chartRef // 暴露 v-chart 实例
});
</script>

<style lang="scss" scoped>
.chart {
  height: 100%; /* 设置图表高度 */
}
</style>
