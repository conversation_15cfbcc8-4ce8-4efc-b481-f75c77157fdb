<!-- 
 * @Description: 单页管理表单页面
 * @Author: huguo<PERSON> 
 * @Date: 2023-12-15 15:43:59
!-->
<template>
  <form-container v-model="visible" :title="`${messageProps.opt}预警配置`" form-size="700px">
    <el-form
      ref="messageFormRef"
      :rules="rules"
      :disabled="messageProps.disabled"
      :model="messageProps.record"
      :hide-required-asterisk="messageProps.disabled"
      label-width="auto"
      label-suffix=" :"
    >
      <s-form-item label="发送方式" prop="sendWay">
        <s-radio-group v-model="messageProps.record.sendWay" :options="sendWayOptions" button @change="handleSendWayChange" />
      </s-form-item>
      <s-form-item v-if="messageProps.record.sendWay === MessageSendWayDictEnum.DELAY" label="延迟时间" prop="delayTime">
        <el-input v-model="messageProps.record.delayTime">
          <template #append>秒</template>
        </el-input>
      </s-form-item>
      <s-form-item v-if="messageProps.record.sendWay === MessageSendWayDictEnum.SCHEDULE" label="指定时间" prop="sendTime">
        <date-picker v-model="messageProps.record.sendTime" :show-shortcuts="false" :disabled-date-before="true" type="datetime" />
      </s-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose"> 取消 </el-button>
      <el-button v-show="!messageProps.disabled" type="primary" @click="handleSubmit"> 确定 </el-button>
    </template>
  </form-container>
</template>

<script setup lang="ts">
import { SysMessage } from "@/api";
import { required } from "@/utils/formRules";
import { FormOptEnum, SysDictEnum, MessageSendWayDictEnum } from "@/enums";
import { FormInstance } from "element-plus";
import { useDictStore } from "@/stores/modules";

const dictStore = useDictStore();
const visible = ref(false); //是否显示表单

// 发送方式选项
const sendWayOptions = dictStore.getDictList(SysDictEnum.MESSAGE_WAY);

// 表单参数
const messageProps = reactive<FormProps.Base<SysMessage.SysMessageInfo>>({
  opt: FormOptEnum.ADD,
  record: {},
  disabled: false
});

// 表单验证规则
const rules = reactive({
  sendWay: [required("请选择发送方式")]
});

/**
 * 打开表单
 * @param props 表单参数
 */
function onOpen(props: FormProps.Base<SysMessage.SysMessageInfo>) {
  Object.assign(messageProps, props); //合并参数

  visible.value = true; //显示表单
  if (props.record?.id) {
    //如果传了id，就去请求api获取record
    messageApi.detail({ id: props.record.id }).then(res => {
      messageProps.record = res.data;
    });
  }
}

// 提交数据（新增/编辑）
const messageFormRef = ref<FormInstance>();
/** 提交表单 */
async function handleSubmit() {
  messageFormRef.value?.validate(async valid => {
    if (!valid) return; //表单验证失败

    //如果发送方式是延迟发送，但是没有填写延迟时间，提示用户填写延迟时间
    if (messageProps.record.sendWay == MessageSendWayDictEnum.DELAY && messageProps.record.delayTime == null) {
      ElMessage("请填写延迟时间");
      return;
    }
    //如果发送方式是定时发送，但是没有填写发送时间，提示用户填写发送时间
    if (messageProps.record.sendWay == MessageSendWayDictEnum.SCHEDULE && messageProps.record.sendTime == null) {
      ElMessage("请填写发送时间");
      return;
    }
    console.log("messageProps.record", messageProps.record);

    //提交表单
    await messageApi
      .submitForm(messageProps.record, messageProps.record.id != undefined)
      .then(() => {
        messageProps.successful!(); //调用父组件的successful方法
      })
      .finally(() => {
        onClose();
      });
  });
}

/** 关闭表单*/
function onClose() {
  visible.value = false;
}

/** 改变发送方式 */
function handleSendWayChange(value: any) {
  //发送方式改变时，清空发送方式信息
  // messageProps.record.sendType = [];
  messageProps.record.sendTime = undefined;
  messageProps.record.delayTime = undefined;
  messageProps.record.sendWay = value;
  return;
}

// 暴露给父组件的方法
defineExpose({
  onOpen
});
</script>
<style lang="scss" scoped>
:deep(.s-input-group__prepend) {
  padding: 0 10px !important;
}
</style>
