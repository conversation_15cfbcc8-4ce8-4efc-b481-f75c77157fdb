<!-- 
 * @Description: 菜单管理
 * @Author: huguodong 
 * @Date: 2023-12-15 15:43:08
!-->
<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      default-expand-all
      :init-param="initParam"
      :title="t('menu.management.title')"
      :search-col="4"
      :indent="20"
      :columns="columns"
      :request-api="filterData"
      :pagination="false"
    >
      <!-- <template #toolButton>
        <el-tooltip content="用户配置" placement="top">
          <el-button :icon="Switch" circle @click="changeTable" />
        </el-tooltip>
      </template> -->
      <!-- 表格 header 按钮 -->
      <!-- <template #tableHeader="scope">
        <el-button type="primary" @click="() => allEnable(scope)">批量启用</el-button>
      </template> -->
      <template #tableHeader="scope">
        <el-button type="primary" @click="() => allSave(scope)">{{ t("menu.management.saveAll") }}</el-button>
        <!-- 移除scope参数，直接调用方法 -->
      </template>
      <!-- 表格 菜单类型 按钮 -->
      <template #menuType="scope">
        <el-space>
          <el-tag>{{ dictStore.dictTranslation(SysDictEnum.MENU_TYPE, scope.row.menuType) }}</el-tag>
          <el-tag v-if="scope.row.isHome === true" type="warning">首页</el-tag>
        </el-space>
      </template>
      <!-- 状态 -->
      <template #status="scope">
        <el-tag v-if="scope.row.status === CommonStatusEnum.ENABLE" type="success">{{
          dictStore.dictTranslation(SysDictEnum.COMMON_STATUS, scope.row.status)
        }}</el-tag>
        <el-tag v-else type="danger">{{ dictStore.dictTranslation(SysDictEnum.COMMON_STATUS, scope.row.status) }}</el-tag>
      </template>
      <template #isWaring="scope">
        <el-switch
          v-if="checkCondition(scope.row)"
          :model-value="scope.row.isWaring === CommonStatusEnum.ENABLE"
          :loading="switchLoading"
          @change="editStatus(scope.row, scope)"
        />
      </template>
      <template #users="scope">
        <div style="display: flex; gap: 8px; align-items: center">
          <el-select
            v-if="checkCondition(scope.row)"
            filterable
            @change="handleUserChange(scope.row)"
            v-model="scope.row.selectedUser"
            style="flex: 1"
            placeholder="请选择"
          >
            <el-option v-for="user in userList" :key="user.id" :label="user.name" :value="user.id" />
          </el-select>
          <!-- <el-button v-if="checkCondition(scope.row)" type="primary" size="small" @click="handleConfirm(scope.row)"> 确定</el-button> -->
        </div>
      </template>
      <template #times="scope">
        <div v-if="checkCondition(scope.row)" class="time-container flex justify-center items-center">
          <div v-if="scope.row.timePoints?.length" class="time-display">
            <el-tag v-for="(time, index) in scope.row.timePoints" :key="index" type="info" class="time-tag mr-[2px]">
              {{ formatTime(time) }}
            </el-tag>
          </div>
          <el-button type="primary" size="small" @click="openTimeDialog(scope.row)"
            >{{ scope.row.timePoints?.length ? t("menu.management.timeSetting.modify") : t("menu.management.timeSetting.add") }}
          </el-button>
        </div>
      </template>
      <!-- 新增/删除行按钮 -->
      <template #operation="scope">
        <el-button
          v-if="
            ((scope.row.children && scope.row.children.length > 0 && scope.row.children[0]['title'] == scope.row.title) ||
              !scope.row.children?.length) &&
            !scope.row.copy
          "
          type="primary"
          size="small"
          @click="addChildRow(scope.row)"
          >{{ t("menu.management.addRow") }}</el-button
        >
        <el-button v-if="scope.row.copy" type="danger" size="small" @click="removeRow(scope.row)">{{ t("menu.management.deleteRow") }}</el-button>
        <el-button
          v-if="scope.row.children && scope.row.children.length > 0 && scope.row.children[0]['title'] == scope.row.title && !scope.row.copy"
          type="primary"
          size="small"
          @click="saveChildren(scope.row)"
          >{{ t("menu.management.save") }}</el-button
        >
      </template>
    </ProTable>
    <!-- 新增/编辑表单 -->
    <Form ref="formRef" />
    <!-- 更改模块 -->
    <ChangeModule ref="changeModuleFormRef" />
    <!-- 权限按钮 -->
    <Button ref="buttonFormRef" />
    <!-- 时间点设置弹窗 -->
    <el-dialog v-model="dialogVisible" :title="`${t('menu.management.timeSetting.title')} - ${currentRow?.title}`" width="800px">
      <template #title>
        <span>
          {{ t("menu.management.timeSetting.title") }} - {{ currentRow?.title }}
          <span style="font-size: 12px"> {{ t("menu.management.timeSetting.notice") }} </span>
        </span>
      </template>
      <el-row :gutter="20">
        <!-- 左侧时间点设置 -->
        <el-col :span="12">
          <div class="border-r pr-4">
            <div v-for="(time, index) in tempTimePoints" :key="index" class="time-item flex items-center mb-2">
              <el-time-picker
                v-model="tempTimePoints[index]"
                format="HH:mm"
                value-format="HH:mm"
                :placeholder="t('menu.management.timeSetting.selectTime')"
                class="w-full"
              />
              <el-button v-if="tempTimePoints.length > 1" @click="removeTime(index)" type="danger" :icon="Minus" circle size="small" class="ml-2" />
            </div>
            <el-button @click="addTime" type="primary" :icon="Plus" size="small">{{ t("menu.management.timeSetting.addTimePoint") }}</el-button>
          </div>
        </el-col>

        <!-- 右侧频率设置 -->
        <el-col :span="12">
          <el-radio-group v-model="tempFrequencyType">
            <el-radio label="day" size="large">{{ t("menu.management.timeSetting.daily") }}</el-radio>
            <el-radio label="week" size="large">{{ t("menu.management.timeSetting.weekly") }}</el-radio>
            <el-radio label="month" size="large">{{ t("menu.management.timeSetting.monthly") }}</el-radio>
          </el-radio-group>

          <!-- 每周选择 -->
          <div v-if="tempFrequencyType === 'week'" class="mt-4">
            <div class="text-gray-500 mb-2">{{ t("menu.management.timeSetting.selectWeek") }}</div>
            <el-radio-group v-model="tempWeekDay">
              <el-radio v-for="(day, index) in ['日', '一', '二', '三', '四', '五', '六']" :key="index" :label="index + 1">
                {{ t("menu.management.timeSetting.week") }}{{ day }}
              </el-radio>
            </el-radio-group>
          </div>

          <!-- 每月选择 -->
          <div v-if="tempFrequencyType === 'month'" class="mt-4">
            <div class="text-gray-500 mb-2">{{ t("menu.management.timeSetting.selectDate") }}</div>
            <el-select v-model="tempMonthDay" :placeholder="t('menu.management.timeSetting.selectDate')">
              <el-option v-for="item in 31" :key="item" :label="`${item}${t('menu.management.timeSetting.day')}`" :value="item" />
            </el-select>
          </div>
        </el-col>
      </el-row>

      <template #footer>
        <el-button @click="dialogVisible = false">{{ t("menu.management.timeSetting.cancel") }}</el-button>
        <el-button type="primary" @click="confirmTime">{{ t("menu.management.timeSetting.confirm") }}</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="sysMenu">
import { menuApi, sysUserApi } from "@/api";
import { ProTableInstance } from "@/components/ProTable/interface";
import { useDictStore } from "@/stores/modules";
import { SysDictEnum, CommonStatusEnum } from "@/enums";
import Form from "./components/form.vue";
import ChangeModule from "./components/changeModule.vue";
import Button from "../button/index.vue";
import { Plus, Minus } from "@element-plus/icons-vue";
import { useUserStore } from "@/stores/modules/user";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const userStore = useUserStore();
// const isChart = ref(false);
const dialogVisible = ref(false);
const currentRow: any = ref(null);
const tempTimePoints = ref([]);
const checkCondition = row => {
  return row.copy;
};
// 打开设置弹窗
const openTimeDialog = row => {
  currentRow.value = row;
  dialogVisible.value = true;
  // 初始化临时数据（至少保留一个）
  tempTimePoints.value = row.timePoints?.length ? [...row.timePoints] : [""];
  tempFrequencyType.value = row.frequencyType || "day";
  tempWeekDay.value = row.frequencyValue || 0;
  tempMonthDay.value = row.frequencyValue || 1;
};

// 添加时间点
const addTime = () => {
  tempTimePoints.value.push("");
};

// 移除时间点
const removeTime = index => {
  if (tempTimePoints.value.length > 1) {
    tempTimePoints.value.splice(index, 1);
  }
};
const tempFrequencyType = ref("day");
const tempWeekDay = ref(0);
const tempMonthDay = ref(1);
// 确认保存
const confirmTime = () => {
  if (tempTimePoints.value.some(t => !t)) {
    ElMessage.error(t("menu.management.timeSetting.fillAllTimePoints"));
    return;
  }

  // 处理频率值
  let frequencyValue;
  switch (tempFrequencyType.value) {
    case "week":
      frequencyValue = tempWeekDay.value;
      break;
    case "month":
      frequencyValue = tempMonthDay.value;
      break;
    default:
      frequencyValue = null;
  }

  // 更新数据
  currentRow.value.timePoints = [...new Set(tempTimePoints.value)].sort();
  currentRow.value.frequencyType = tempFrequencyType.value;
  currentRow.value.frequencyValue = frequencyValue;

  dialogVisible.value = false;
};

// 时间格式化显示
const formatTime = time => {
  return time?.substring(0, 5) || "--:--";
};
// const changeTable = () => {
//   isChart.value = !isChart.value;
// };
const proTable = ref<ProTableInstance>();
const userList: any = ref([
  { id: 1, name: "用户1" },
  { id: 2, name: "用户2" },
  { id: 3, name: "用户3" }
]);
// const handleConfirm = row => {
//   menuApi.submitForm(row, true);
// };
onMounted(async () => {
  const { data } = await sysUserApi.page({ pageNum: 1, pageSize: 100000 });
  userList.value = data.list.map(item => {
    return {
      id: item.account,
      name: `${item.name}\u00A0 ${item.account}`
    };
  });
});

const handleUserChange = row => {
  console.log(row);
};

function filterDisabledItems(menuItems) {
  // 递归过滤函数
  const filterRecursive = items => {
    return items.reduce((acc, item) => {
      // 如果当前项是禁用的，直接跳过
      if (item.status === "DISABLED") {
        return acc;
      }

      // 复制当前项（不修改原对象）
      const newItem = { ...item };

      // 如果有子项，递归过滤子项
      if (item.children && item.children.length > 0) {
        newItem.children = filterRecursive(item.children);
      } else {
        // 确保children属性存在且是数组（根据你的数据结构可能需要）
        newItem.children = [];
      }

      // 将处理后的项添加到结果中
      acc.push(newItem);
      return acc;
    }, []);
  };

  return filterRecursive(menuItems);
}
// // 序列化函数
// function serializeTree(nodes) {
//   return nodes.map(node => {
//     const copyChildren = node.children ? node.children.filter(child => child.copy) : [];
//     node.warningInfos = JSON.stringify(copyChildren);

//     if (node.children) {
//       node.children = node.children.filter(child => !child.copy);
//     }

//     if (node.children && node.children.length > 0) {
//       node.children = serializeTree(node.children);
//     }
//     return node;
//   });
// }

// // 反序列化函数
function deserializeTree(nodes) {
  return nodes.map(node => {
    if (node.warningInfos) {
      const deserializedChildren = JSON.parse(node.warningInfos);
      node.children = (node.children || []).concat(deserializedChildren);
      delete node.warningInfos;
    }

    if (node.children && node.children.length > 0) {
      node.children = deserializeTree(node.children);
    }
    return node;
  });
}

const filterData = async params => {
  const res = await menuApi.tree(params);
  const data = deserializeTree(filterDisabledItems(res.data));
  return { ...res, data };
};
/**
 * 刷新表格
 */
function RefreshTable() {
  proTable.value?.refresh();
}
// const allEnable = data => {
//   menuApi.update({ ids: data.selectedListIds });
//   RefreshTable();
// };
// 递归更新父节点状态
// const updateParentStatus = node => {
//   let current = node;
//   while (current.parent) {
//     const parent = current.parent;
//     const allChildrenEnabled = parent.children.every(child => child.isWarning);

//     // 仅在状态变化时更新
//     if (parent.isWarning !== allChildrenEnabled) {
//       parent.isWarning = allChildrenEnabled;
//       current = parent; // 继续向上检查
//     } else {
//       break; // 状态未变化时停止
//     }
//   }
// };
function editStatus(row: any) {
  switchLoading.value = true;
  const status1 = row.isWaring === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE; // 状态取反
  // updateParentStatus(shit);
  row.isWaring = status1;
  switchLoading.value = false;
  // menuApi
  //   .submitForm({ ...row, isWaring: status1 }, row.id != undefined)
  //   .then(() => {
  //     switchLoading.value = false;
  //     // ElMessage.success("修改成功");
  //     row.isWaring = status1;
  //   })
  //   .catch(() => {
  //     switchLoading.value = false;

  //     ElMessage.success("修改失败");
  //   });
}
// const iswarningMap = {
//   false: "DISABLED",
//   true: "ENABLE"
// };
const initParam = ref({
  module: 212755263003743
});
const switchLoading = ref(false);

const dictStore = useDictStore();
//遍历模块列表，生成下拉选项

// 获取 ProTable 元素，调用其获取刷新数据方法（还能获取到当前查询参数，方便导出携带参数）
// 表格配置项
const columns = computed(() => [
  { type: "selection", fixed: "left", width: 50 },
  { prop: "id", label: "Id", isShow: false },
  { prop: "title", label: t("menu.management.menuName"), align: "left", search: { el: "input" } },
  {
    prop: "isWaring",
    label: t("menu.management.isWarning")
  },
  {
    prop: "users",
    label: t("menu.management.warningUser")
  },
  {
    prop: "times",
    label: t("menu.management.warningTime")
  },
  { prop: "operation", label: t("menu.management.operation"), width: 230, fixed: "right" }
]);

// 表单引用
const formRef = ref<InstanceType<typeof Form> | null>(null);

/**
 * 打开表单
 * @param opt  操作类型
 * @param record  记录
 */

// 修改模块表单引用
const changeModuleFormRef = ref<InstanceType<typeof ChangeModule> | null>(null);
// 权限按钮表单引用
const buttonFormRef = ref<InstanceType<typeof Button> | null>(null);

// 新增子行
const addChildRow = row => {
  if (userStore.isAlarm) {
    return ElMessage.error("请先关闭定时推送任务");
  }
  if (!row.children) {
    row.children = [];
  }
  let newRow;
  if (row.copy) {
    newRow = {
      title: row.title,
      children: [],
      isWaring: CommonStatusEnum.DISABLE,
      selectedUser: [],
      timePoints: [],
      parentId: row.parentId,
      id: Date.now(),
      copy: true
    };
  } else {
    newRow = {
      title: row.title,
      children: [],
      isWaring: CommonStatusEnum.DISABLE,
      selectedUser: [],
      timePoints: [],
      parentId: row.id,
      id: Date.now(),
      copy: true
    };
  }

  row.children.unshift(newRow);
};

// 递归查找父节点
const findParent = (tree, targetNode, parent = null) => {
  for (const node of tree) {
    if (node === targetNode) return parent;
    if (node.children) {
      const found = findParent(node.children, targetNode, node);
      if (found) return found;
    }
  }
  return null;
};
// 删除行
const removeRow = targetNode => {
  if (userStore.isAlarm) {
    return ElMessage.error("请先关闭定时推送任务");
  }
  const parent = findParent(proTable.value?.tableData, targetNode);

  if (parent) {
    const index = parent.children.findIndex(n => n === targetNode);
    if (index > -1) {
      parent.children.splice(index, 1);
    }
  }
  let shitrow: any = [];
  if (parent.children && parent.children.length > 0) {
    shitrow = parent.children.filter(item => item.copy);
  }
  shitrow = JSON.stringify(shitrow);
  parent.warningInfos = shitrow;
  parent.children = parent.children.filter(item => !item.copy);

  menuApi.submitAllForm(parent, true).finally(() => {
    RefreshTable();
  });
  // proTable.value?.tableData.splice(index, 1);
  // console.log(proTable.value?.tableData, "proTable.value?.tableData", index);
};

// 保存子行
const saveChildren = async row => {
  if (userStore.isAlarm) {
    return ElMessage.error("请先关闭定时推送任务");
  }
  // if (row.children && row.children.length > 0) {
  //   for (const child of row.children) {
  //     await menuApi.submitForm(child, true);
  //   }
  // const parent = findParent(proTable.value?.tableData, row);
  const parent = row;
  let shitrow: any = [];
  if (parent.children && parent.children.length > 0) {
    shitrow = parent.children.filter(item => item.copy);
  }
  shitrow = JSON.stringify(shitrow);
  parent.warningInfos = shitrow;
  parent.children = parent.children.filter(item => !item.copy);

  menuApi.submitAllForm(parent, true).finally(() => {
    RefreshTable();
  });
};
// 在script setup中添加以下逻辑
// const allSave = async () => {
//   // 获取表格所有数据
//   const tableData = proTable.value?.tableData || [];
//   if (tableData.length === 0) return ElMessage.info("无数据需保存");
//   try {
//     // 遍历所有行，处理需要保存的节点（逻辑与单个保存一致）
//     for (const row of tableData) {
//       if (!row.children || row.children.length === 0) continue;
//       // 过滤出标记为临时新增/修改的子行（copy为true）
//       const tempRows = row.children.filter(child => child.copy);
//       // 序列化临时数据到父节点warningInfos字段
//       row.warningInfos = JSON.stringify(tempRows);
//       // 保留非临时子行（copy为false）
//       row.children = row.children.filter(child => !child.copy);
//       // 调用现有保存接口（与单个保存按钮逻辑一致）
//       await menuApi.submitForm(row, true); // 第二个参数为是否是更新操作（true表示更新）
//     }
//     // 保存完成后刷新表格
//     RefreshTable();
//     ElMessage.success("全部保存成功");
//   } catch (error) {
//     console.error("批量保存失败:", error);
//     ElMessage.error("保存失败，请检查网络或重试");
//   }
// };
const allSave = async () => {
  const tableData = proTable.value?.tableData || [];
  if (tableData.length === 0) return ElMessage.info("无数据需保存");

  try {
    const processNode = async (node: any) => {
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          await processNode(child);
        }
        const tempChildren = node.children.filter(child => child.copy);
        node.warningInfos = JSON.stringify(tempChildren);
        node.children = node.children.filter(child => !child.copy);
        await menuApi.submitAllForm(node, true, false);
      }
    };

    for (let i = 0; i < tableData.length; i++) {
      await processNode(tableData[i]);
    }

    ElMessage.success("全部保存成功");
    RefreshTable();
  } catch (error) {
    console.error("批量保存失败:", error);
    ElMessage.error("保存失败，请检查网络或重试");
  }
};
// const allSave = async () => {
//   const tableData = proTable.value?.tableData || [];
//   if (tableData.length === 0) return ElMessage.info("无数据需保存");

//   try {
//     // 递归处理每个节点，深度优先后序遍历
//     const processNode = async node => {
//       if (node.children && node.children.length > 0) {
//         // 先处理所有子节点
//         for (const child of node.children) {
//           await processNode(child);
//         }

//         // 处理当前节点的临时子节点
//         const tempChildren = node.children.filter(child => child.copy);
//         node.warningInfos = JSON.stringify(tempChildren);
//         node.children = node.children.filter(child => !child.copy);

//         // 提交当前节点的修改
//         await menuApi.submitForm(node, true);
//       }
//     };

//     // 遍历所有根节点
//     for (const row of tableData) {
//       await processNode(row);
//     }

//     RefreshTable();
//     ElMessage.success("全部保存成功");
//   } catch (error) {
//     console.error("批量保存失败:", error);
//     ElMessage.error("保存失败，请检查网络或重试");
//   }
// };
</script>

<style lang="scss" scoped>
.el-dropdown-link {
  display: flex;
  align-items: center;
  color: var(--el-color-primary);
  cursor: pointer;
}
:deep(.el-link__inner) {
  padding-left: 6px !important;
}
</style>
