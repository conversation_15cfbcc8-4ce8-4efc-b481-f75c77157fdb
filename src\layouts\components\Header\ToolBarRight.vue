<template>
  <div class="tool-bar-ri">
    <div class="header-icon">
      <!-- <el-tooltip v-if="account == 'superAdmin'" :content="userStore.isAlarm ? '当前预警已开启' : '当前预警已关闭'" placement="top">
        <template #content>
          <el-switch v-model="userStore.isAlarm" @change="handleChange"></el-switch>
        </template>
      </el-tooltip> -->
      <el-switch v-if="account == 'superAdmin'" v-model="userStore.isAlarm" @change="handleChange"></el-switch>
      <el-input v-if="true" :width="120" v-model="shittest" placeholder="">
        <template #suffix>
          <el-button @click="uploadFeishuImage(shittest, true)">确定</el-button>
        </template>
      </el-input>
      <AssemblySize id="assemblySize" />

      <SearchMenu id="searchMenu" />
      <Language id="language" />

      <ThemeSetting id="themeSetting" />
      <Message id="message" />
      <Fullscreen id="fullscreen" />
      <ChangeModule id="changeModule" />
    </div>
    <span class="username">{{ username }}</span>
    <Avatar />
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useUserStore } from "@/stores/modules/user";
import AssemblySize from "./components/AssemblySize.vue";
import ThemeSetting from "./components/ThemeSetting.vue";
import Message from "./components/Message.vue";
import Fullscreen from "./components/Fullscreen.vue";
import Avatar from "./components/Avatar.vue";
import ChangeModule from "./components/ChangeModule.vue";
import SearchMenu from "./components/SearchMenu.vue";
import Language from "./components/Language.vue";

// const switchValue = ref(false);
const shittest: string = ref();
import { uploadFeishuImage } from "@/utils";

const handleChange = value => {
  if (value) {
    ElMessage.success("预警开");
  } else {
    ElMessage.warning("预警关闭");
  }
};
const userStore = useUserStore();
const username = computed(() => userStore.userInfo?.name);
const account = computed(() => userStore.userInfo?.account);
</script>

<style scoped lang="scss">
.tool-bar-ri {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 25px;
  .header-icon {
    display: flex;
    align-items: center;
    & > * {
      margin-left: 21px;
      color: var(--el-header-text-color);
    }
  }
  .username {
    margin: 0 20px;
    font-size: 15px;
    color: var(--el-header-text-color);
  }
}
</style>
