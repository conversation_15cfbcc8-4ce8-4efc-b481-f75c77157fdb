<template>
  <div class="main-box">
    <Menu></Menu>
  </div>
  <!-- <div class="main-box">
    <div class="flex card">
      <div class="w-[65%] mr-[20px]">
        <User></User>
      </div>
      <div class="w-[35%]">
        <Menu></Menu>
      </div>
    </div>
  </div> -->
  <!-- <User></User> -->
</template>

<script lang="ts" setup>
// import User from "./user/index.vue";
import Menu from "./menu/index.vue";
</script>

<style lang="scss" scoped></style>
